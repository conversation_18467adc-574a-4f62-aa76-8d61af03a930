<template>
    <el-drawer  v-model="isShow" :title="title" size="1250" :before-close="onClose" direction="rtl">
        <StepPage ref="stepPage"/>
        <template #footer>
            <el-space>
                <el-button type="warning" @click="onClose">取消</el-button>
                <el-button type="primary" @click="onSubmit('submit')">下一步</el-button>
            </el-space>
        </template>
    </el-drawer>
</template>

<script>
import { ElMessageBox } from 'element-plus'
import StepPage from './stepPage.vue'

export default {
    components: {
        StepPage
    },
    data() {
        return {
            isShow: false,
            title: '开始批改',
        }
    },
    created() {
      // this.$refs.stepPage.setActiveStep(1);
    },
    methods: {
        show() {
            this.isShow = true
        },
        onClose() {
          ElMessageBox.confirm('确定停止批改样卷吗')
              .then(() => {
                this.$refs.stepPage.resetFields()
                this.isShow = false
                this.$emit('onClose')
              })
              .catch(() => {
                // catch error
              })
        },
        onSubmit(action) {
            this.$refs.stepPage.submit('submit')
        },

    }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small) {
    margin-bottom: 5px;
}

:deep(.el-form-item) {
    margin-bottom: 15px;
}
</style>