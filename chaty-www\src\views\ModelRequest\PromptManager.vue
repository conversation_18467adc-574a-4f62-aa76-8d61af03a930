<template>
  <el-dialog
      title="Prompt 管理"
      v-model="visible"
      width="100vw"
      :before-close="onClose"
      :close-on-click-modal="false"
      custom-class="full-screen-dialog"
  >
    <!-- 新增部分：按类型和 Key 选择并点击新增 -->
    <el-form inline class="add-form" style="margin-bottom: 16px;">
      <el-form-item label="选择类型">
        <el-select v-model="newType" placeholder="请选择类型" style="width: 200px;" @change="onTypeChange">
          <el-option
              v-for="type in types"
              :key="type"
              :label="type"
              :value="type"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择 Key">
        <el-select v-model="newKey" placeholder="请选择 Key" style="width: 300px;" :disabled="!newType">
          <el-option
              v-for="key in filteredKeys"
              :key="key"
              :label="`${promptDefinitions[key].name} (${key})`"
              :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onAdd" :disabled="!newKey || addLoading" :loading="addLoading">
          新增
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 底部筛选按钮 -->
    <div class="filter-buttons" style="margin-bottom: 16px;">
      <el-button plain :type="listFilterType === '' ? 'primary' : 'default'" @click="listFilterType = ''">全部</el-button>
      <el-button
          v-for="type in types"
          :key="type"
          plain
          :type="listFilterType === type ? 'primary' : 'default'"
          @click="listFilterType = type"
      >
        {{ type }}
      </el-button>
    </div>

    <el-divider />

    <!-- 列表部分 -->
    <el-scrollbar style="height: 60vh;">
      <div
          v-for="item in filteredEntries"
          :key="item.key"
          class="prompt-item"
      >
        <div class="prompt-title">
          {{ item.name }} ({{ item.key }})
          <el-tag
              v-if="(item.name && (item.name.includes('提示') || item.name.includes('模板'))) || item.type === '普通题目-大题类型' || item.type === '标准卷批注'"
              type="primary"
              style="margin-left: 8px;"
          >string</el-tag>
          <el-tag
              v-else-if="item.name && item.name.includes('格式')"
              type="danger"
              style="margin-left: 8px;"
          >json</el-tag>
        </div>
        <el-input
            type="textarea"
            v-model="item.value"
            autosize
            class="prompt-input"
        />
        <div class="prompt-actions">
          <el-button
              type="primary"
              @click="updatePrompt(item)"
              :loading="loadingMap[item.key]"
          >更改</el-button>
          <el-button
              type="warning"
              @click="resetPrompt(item)"
              :loading="loadingMap[item.key]"
          >重置</el-button>
          <el-button
              type="danger"
              @click="deletePrompt(item)"
              :loading="loadingMap[item.key]"
          >删除</el-button>
        </div>
        <el-divider />
      </div>
    </el-scrollbar>

    <template #footer>
      <el-button @click="onClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'PromptManagerDialog',
  data() {
    return {
      visible: false,
      record: null,
      promptDefinitions: {},
      promptEntries: [],
      entriesMap: {},
      newType: '',
      newKey: '',
      listFilterType: '',
      addLoading: false,
      loadingMap: {}
    };
  },
  computed: {
    types() {
      return Array.from(new Set(
          Object.values(this.promptDefinitions).map(d => d.type)
      ));
    },
    filteredKeys() {
      return Object.entries(this.promptDefinitions)
          .filter(([, meta]) => meta.type === this.newType && !this.entriesMap[meta.key])
          .map(([key]) => key);
    },
    filteredEntries() {
      return this.listFilterType
          ? this.promptEntries.filter(e => e.type === this.listFilterType)
          : this.promptEntries;
    }
  },
  methods: {
    show(rec) {
      this.record = rec;
      this.visible = true;
      this.init();
    },
    onClose() {
      this.visible = false;
      this.resetState();
      this.$emit('refresh');
    },
    resetState() {
      this.record = null;
      this.promptEntries = [];
      this.entriesMap = {};
      this.newType = '';
      this.newKey = '';
      this.listFilterType = '';
      this.loadingMap = {};
    },
    async init() {
      const res = await this.$axios.get('/api/model-request/getAllPromptKey');
      this.promptDefinitions = res.data.data || res.data;
      const obj = this.record.prompt ? JSON.parse(this.record.prompt) : {};
      this.promptEntries = Object.entries(this.promptDefinitions)
          .filter(([key]) => obj.hasOwnProperty(key))
          .map(([key, meta]) => ({
            key,
            name: meta.name,
            type: meta.type,
            value: obj[key]
          }));
      this.entriesMap = {};
      this.promptEntries.forEach(e => { this.entriesMap[e.key] = true; });
    },
    onTypeChange() {
      this.newKey = '';
    },
    async onAdd() {
      if (!this.newKey) return;
      this.addLoading = true;
      try {
        const defaultValue = this.promptDefinitions[this.newKey].value || '';
        await this.$axios.post('/api/model-request/addPrompt', {
          modelRequestId: this.record.id,
          key: this.newKey,
          value: defaultValue
        });
        const meta = this.promptDefinitions[this.newKey];
        this.promptEntries.unshift({
          key: this.newKey,
          name: meta.name,
          type: meta.type,
          value: defaultValue
        });
        this.entriesMap[this.newKey] = true;
        this.$message.success('新增成功');
        this.newKey = '';
      } catch {
        this.$message.error('新增失败');
      } finally {
        this.addLoading = false;
      }
    },
    async updatePrompt(item) {
      this.loadingMap[item.key] = true;
      try {
        await this.$axios.post('/api/model-request/updatePrompt', {
          modelRequestId: this.record.id,
          key: item.key,
          value: item.value
        });
        this.$message.success('更改成功');
      } catch {
        this.$message.error('更改失败');
      } finally {
        this.loadingMap[item.key] = false;
      }
    },
    async resetPrompt(item) {
      this.loadingMap[item.key] = true;
      try {
        const defaultValue = this.promptDefinitions[item.key].value || '';
        await this.$axios.post('/api/model-request/updatePrompt', {
          modelRequestId: this.record.id,
          key: item.key,
          value: defaultValue
        });
        item.value = defaultValue;
        this.$message.success('重置成功');
      } catch {
        this.$message.error('重置失败');
      } finally {
        this.loadingMap[item.key] = false;
      }
    },
    async deletePrompt(item) {
      this.loadingMap[item.key] = true;
      try {
        await this.$axios.post('/api/model-request/deletePrompt', {
          modelRequestId: this.record.id,
          key: item.key
        });
        this.promptEntries = this.promptEntries.filter(e => e.key !== item.key);
        delete this.entriesMap[item.key];
        this.$message.success('删除成功');
      } catch {
        this.$message.error('删除失败');
      } finally {
        this.loadingMap[item.key] = false;
      }
    }
  }
};
</script>

<style scoped>
.full-screen-dialog .el-dialog {
  width: 100vw !important;
  height: 100vh;
}
.full-screen-dialog .el-dialog__body,
.full-screen-dialog .el-dialog__footer {
  max-height: calc(100vh - 80px);
}
.prompt-item {
  margin-bottom: 16px;
}
.prompt-title {
  font-weight: bold;
  margin-bottom: 8px;
}
.prompt-input {
  width: 100%;
}
.prompt-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}
.add-form {
  align-items: center;
}
.filter-buttons {
  display: flex;
  gap: 8px;
}
</style>
