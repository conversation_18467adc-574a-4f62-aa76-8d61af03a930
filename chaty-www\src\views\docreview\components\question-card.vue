<template>
    <div class="question-card">
        <div class="card-header">
            <div class="card-header-title">
                {{ question.name || `${$t('common.question')}${index + 1}` }}
                <el-button type="warning" text icon="Edit" style="padding: 0;" size="small" @click="rename"></el-button>
            </div>
            <div class="card-header-action">
                <!-- <el-button text icon="Edit"></el-button> -->
                <!-- <el-button text icon="Check"></el-button> -->
                <el-button text icon="Delete" @click="doDelete"></el-button>
            </div>
        </div>
        <div class="card-main">
            <el-form :model="data" label-position="top">
                <el-form-item prop="question">
                    <template v-slot:label>
                        <div class="form-item-label">
                            <label>{{ `${$t('common.question')}：` }}</label>
                            <div class="form-item-label__right">
                                <el-button v-if="data.crop === 'question'" icon="Crop" size="small" type="warning"
                                    @click="doCrop"></el-button>
                                <el-dropdown v-else @command="cropOption">
                                    <el-button :icon="options.loadingQuestion ? 'Loading' : 'Crop'"
                                        :type="options.loadingQuestion ? 'warning' : ''" size="small"
                                        @click="crop('question', null, 'paddleOCRService')"></el-button>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item v-for="item in ocrServices" :key="item.name"
                                                :command="{ type: 'question', ocr: item.service }">
                                                {{ item.name }}
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                        </div>
                    </template>
                    <el-input v-model="data.question" type="textarea" :autosize="autosize"
                        :placeholder="$t('common.pleaseEnter', [$t('common.question')])"></el-input>
                </el-form-item>
                <el-form-item prop="correctAnswer">
                    <template v-slot:label>
                        <div class="form-item-label">
                            <label>{{ `${$t('common.answer')}：` }}</label>
                            <div class="form-item-label__right">
                                <el-button v-if="data.crop === 'correctAnswer'" icon="Crop" size="small" type="warning"
                                    @click="doCrop"></el-button>
                                <el-dropdown v-else @command="cropOption">
                                    <el-button :icon="options.loadingCorrectAnswer ? 'Loading' : 'Crop'"
                                        :type="options.loadingCorrectAnswer ? 'warning' : ''" size="small"
                                        @click="crop('correctAnswer', null, 'paddleOCRService')"></el-button>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item v-for="item in ocrServices" :key="item.name"
                                                :command="{ type: 'correctAnswer', ocr: item.service }">
                                                {{ item.name }}
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                        </div>
                    </template>
                    <el-input v-model="data.correctAnswer" type="textarea" :autosize="autosize"
                        :placeholder="$t('common.pleaseEnter', [$t('common.answer')])"></el-input>
                </el-form-item>
                <el-form-item v-show="isScore">
                    <template v-slot:label>
                        <div class="form-item-label">
                            <label>{{ $t('docReview.score') }}</label>
                        </div>
                    </template>
                    <el-input-number v-model="data.score" :controls="false" :precision="1" :autosize="autosize" />
                </el-form-item>
                <el-form-item :label="`${$t('common.evaluation')}：`" prop="defaultReview">
                    <template #label>
                        <el-switch v-model="data.isDefaultReview" size="large"
                            :active-text="$t('common.defaultEvaluation')"
                            :inactive-text="`AI${$t('common.evaluation')}`" @change="onReviewTypeChange" />
                    </template>
                    <el-input v-show="data.isDefaultReview" v-model="data.defaultReview" type="textarea"
                        :autosize="autosize" placeholder="请输入默认评价"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button v-if="data.answerArea && data.crop !== 'answer'" type="success" icon="check"
                        @click="crop('answer', data.answerArea)">{{ $t('docReview.answerArea') }}</el-button>
                    <el-button v-else-if="data.crop !== 'answer'" plain icon="Crop" @click="crop('answer')">{{
                        $t('docReview.answerArea') }}</el-button>
                    <el-button v-else-if="data.crop === 'answer'" type="warning" icon="check" @click="doCrop">{{
                        $t('docReview.answerArea') }}</el-button>

                    <el-button v-if="data.reviewArea && data.crop !== 'review'" type="success" icon="check"
                        @click="crop('review', data.reviewArea)">{{ $t('docReview.evaluationArea') }}</el-button>
                    <el-button v-else-if="data.crop !== 'review'" plain icon="Crop" @click="crop('review')">{{
                        $t('docReview.evaluationArea') }}</el-button>
                    <el-button v-else-if="data.crop === 'review'" type="warning" icon="check" @click="doCrop">{{
                        $t('docReview.evaluationArea') }}</el-button>

                    <el-button v-if="data.checkArea && data.crop !== 'check'" type="success" icon="check"
                        @click="crop('check', data.checkArea)">{{ $t('docReview.flagArea') }}</el-button>
                    <el-button v-else-if="data.crop !== 'check'" plain icon="Crop" @click="crop('check')">{{
                        $t('docReview.flagArea')
                        }}</el-button>
                    <el-button v-else-if="data.crop === 'check'" type="warning" icon="check" @click="doCrop">{{
                        $t('docReview.flagArea')
                        }}</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import InputMd from '@/components/form/input-md.vue'
export default {
    components: { InputMd },
    props: {
        data: {
            type: Object,
            default: () => {
                return {}
            }
        },
        index: {
            type: Number,
            default: 0
        },
        isScore: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            autosize: {
                minRows: 3
            },
            question: {
                name: '',
                question: '',
                correctAnswer: '',
                score: 0,
                answerArea: null,
                reviewArea: null,
                checkArea: null,
                defaultReview: '',
                crop: '',
                isDefaultReview: false,
            },
            options: {
                loadingQuestion: false,
                loadingCorrectAnswer: false,
            },
            ocrServices: [
                { name: 'PaddleOCR', service: 'paddleOCRService' },
                { name: 'MathPix', service: 'mathPixOCRService' },
                { name: 'Tencent', service: 'tencentOCRService' },
                { name: 'gpt-4-vision', service: 'openaiService' },
            ]
        }
    },
    mounted() {
        this.question = Object.assign(this.question, this.data)
    },
    watch: {
        data(val) {
            this.question = Object.assign(this.question, val)
        }
    },
    methods: {
        updateValue() {
            this.$emit('update:data', this.question)
        },
        crop(type, area, options) {
            this.$emit('crop', type, area, options)
        },
        doCrop() {
            this.$emit('doCrop')
        },
        doDelete() {
            this.$emit('doDelete')
        },
        onReviewTypeChange(val) {
            if (!val) {
                this.data.defaultReview = null // 确保默认评价为空
            } else {
                this.data.defaultReview = ""
            }
        },
        updateOptions(prop, value) {
            this.options[prop] = value
        },
        cropOption(options) {
            this.crop(options.type, options.area, options)
        },
        rename() {
            this.$prompt(this.$t('docReview.updateDocName'), '', {
                confirmButtonText: this.$t('common.confirm'),
                cancelButtonText: this.$t('common.cancel'),
                inputValue: this.question.name || `${$t('common.question')}${this.index + 1}`,
            }).then(res => {
                this.question.name = res.value
                this.updateValue()
            })
        }
    },
}
</script>

<style lang="scss" scoped>
.question-card {
    padding: 20px;
    border-radius: var(--el-border-radius-large);
    border: 1px solid var(--el-border-color);
    box-shadow: var(--el-box-shadow);

    .card-header {
        height: 20px;
        display: flex;
        align-items: center;
        padding-right: 20px;
        // border-bottom: 1px solid var(--el-border-color);

        .card-header-title {
            font-size: 16px;
            font-weight: 400;
        }

        .card-header-action {
            margin-left: auto;
        }

    }

    .card-main {
        margin-top: 15px;

        .form-item-label {
            display: flex;
            align-items: center;
        }

        .form-item-label__right {
            margin-left: auto;
        }
    }
}
</style>