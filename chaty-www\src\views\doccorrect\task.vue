<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-input class="filter-item" v-model="filter.name" placeholder="请输入名称"/>
          <el-select class="filter-item" v-model="filter.status" placeholder="请选择状态" clearable>
            <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
          <el-button type="primary" @click="loadData">查询</el-button>
          <div class="right"/>
          <el-statistic class="header-action header-stats" title="试卷数量" :value="statusStats.total"/>
          <el-statistic class="header-action header-stats" title="批改中" value-style="color: #E6A23C;"
                        :value="statusStats[2]"/>
          <el-statistic class="header-action header-stats" title="已批改" value-style="color: #67C23A;"
                        :value="statusStats[3]"/>
          <el-button type="primary" class="header-action" icon="Setting" @click="toConfig()">试卷配置</el-button>
          <el-button class="header-action" type="primary" icon="Upload" @click="pdfSplit">PDF拆分</el-button>
          <el-button class="header-action" type="primary" icon="Download" @click="downloadStats">统计结果</el-button>
          <el-button class="header-action" type="primary" icon="Download" @click="downloadReviewed">批改结果下载</el-button>
          <el-button class="header-action" type="primary" icon="Download" @click="downloadZip">打包批改结果</el-button>
          <el-button class="header-action" type="primary" icon="Plus" @click="onAddTask">新增</el-button>
        </div>
      </template>
    </el-page-header>
    <div class="main-content">
      <el-table v-loading="taskLoading" :data="tasks" style="height: 100%" empty-text="无数据" :border="false">
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center" :fixed="column.prop === 'operations' ? 'right':''">
          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-space :size="5">
              <el-button v-if="scope.row.status !== 2" type="primary" text size="small"
                         @click="correctTask(scope.row.id)">批改
              </el-button>
              <el-dropdown>
                  <span>
                    <el-icon class="el-icon--right">
                      <more/>
                    </el-icon>
                  </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="scope.row.status === 3" :loading="resultSaving[scope.$index]"
                                      @click="saveResult(scope.row.id, scope.$index)">保存结果
                    </el-dropdown-item>
                    <el-dropdown-item v-if="scope.row.status === 2" @click="correctName(scope.row.id)">
                      姓名校正
                    </el-dropdown-item>

                    <el-dropdown-item @click="onEditTask(scope.row)" divided>编辑</el-dropdown-item>
                    <el-dropdown-item @click="onDeleteTask(scope.row.id)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-space>
          </template>
          <template v-else-if="column.prop === 'record'" v-slot="scope">
            <el-link type="primary" @click="toRecord(scope.row.id)">查看</el-link>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag :type="statusOptions[scope.row.status].type">{{
                statusOptions[scope.row.status].label
              }}
            </el-tag>
          </template>
          <template v-else-if="column.prop === 'configName'" v-slot="scope">
            <el-link v-if="scope.row.configId" type="primary" @click="toConfig(scope.row.configId)">
              {{ scope.row.configName }}
            </el-link>
          </template>
          <template v-else-if="column.prop === 'progress'" v-slot="scope">
            <el-text v-if="scope.row.status === 2">{{
                `${scope.row.finishedCount ||
                0}/${scope.row.recordCount || 0}`
              }}
            </el-text>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer-bar">
      <el-pagination background layout="prev, pager, next" v-model:page-size="pageSize"
                     v-model:current-page="pageNumber" :total="total" @current-change="loadData"/>
    </div>

    <task-form ref="taskForm" @onClose="loadData"/>
    <download-form ref="downloadForm" @submit="onDownloadSubmit" :submiting="downloading"/>
    <stats-download-form ref="statsDownloadForm" @submit="onStatsDownloadSubmit" :submiting="statsDownloading"/>
    <pdf-parse-form ref="pdfParseForm" @submit="loadData"/>
    <task-selector ref="taskSelector" @submit="doSyncDocName"/>
    <zip-file-form ref="zipFileForm" @submit="onZipSubmit" :submiting="zipFileDownloading"/>
  </div>
</template>

<script>
import PdfParseForm from "./components/pdfparseform.vue";
import TaskForm from "./components/taskform.vue";
import {useUserStore} from "@/store/index";
import {mapState} from "pinia";
import DownloadForm from "./components/download-form.vue";
import StatsDownloadForm from "./components/statsdownload-form.vue"
import TaskSelector from "./components/taskSelector.vue";
import ZipFileForm from "./components/ZipFileForm.vue";

export default {
  components: {
    PdfParseForm,
    TaskForm,
    DownloadForm,
    StatsDownloadForm,
    TaskSelector,
    ZipFileForm
  },
  data() {
    return {
      filter: {
        name: "",
        status: "",
      },
      tasks: [],
      columns: [
        {label: "名称", prop: "name"},
        {label: "试卷配置", prop: "configName"},
        {label: "试卷", prop: "record"},
        {label: "状态", prop: "status", width: 200},
        {label: "进度", prop: "progress", width: 200},
        {label: "操作", prop: "operations", width: 110},
        {label: "更新时间", prop: "updateTime", width: 200},
      ],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      taskLoading: false,
      statusOptions: {
        1: {value: 1, label: "未批改"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      refresher: null,
      statusStats: {
        1: 0,
        2: 0,
        3: 0,
        total: 0,
      },
      downloading: false,
      statsDownloading: false,
      resultSaving: {},
      zipFileDownloading: false
    }
  },
  created() {
    this.loadData();
    this.loadStatusStats();
  },
  computed: {
    ...mapState(useUserStore, ["getUser"]),
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.loadData()
      vm.loadStatusStats()
    })
  },
  methods: {
    onAddTask() {
      this.$refs.taskForm.show({title: "新增"});
    },
    onEditTask(data) {
      this.$refs.taskForm.show({title: "编辑", data});
    },
    onDeleteTask(id) {
      this.$confirm("确认删除该任务吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$axios.post(`/api/docCorrectTask/delete?id=${id}`).then(res => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    loadData() {
      this.taskLoading = true
      let form = {
        page: {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
        },
        countRecord: true,
        essay: false
      }
      form = Object.assign(form, this.filter);
      this.$axios.post("/api/docCorrectTask/page", form).then(res => {
        this.tasks = res.data.records;
        this.total = res.data.total;
      }).finally(() => {
        this.taskLoading = false;
      })
    },
    toRecord(id) {
      this.$router.push(`/docrecord/${id}`);
    },
    uploadDoc(response, file, fileList) {
      console.log(response)
      let files = response.data
      if (!files || files.length === 0) {
        this.$message.error("文件上传失败!");
        return;
      }
      let filename = file.name.substring(0, file.name.lastIndexOf("."));
      let form = {
        filename,
        docList: files
      };
      this.$axios.post("/api/docCorrectTask/add/file", form).then(res => {
        this.$message.success("文件上传成功");
        this.loadData();
      });
    },
    getParseConfig() {
      return {
        config: JSON.stringify(this.$refs.pdfParseForm.form)
      };
    },
    toConfig(configId) {
      let query = {}
      if (configId) {
        query.id = configId
      }
      this.$router.push({
        name: "docconfig",
        query,
      });
    },
    refreshTasks() {
      let isRefresh = this.statusStats[2] > 0
      if (isRefresh && !this.refresher) {
        this.refresher = setInterval(() => {
          this.loadData()
          this.loadStatusStats()
        }, 10 * 1000);
      }
      if (!isRefresh && this.refresher) {
        clearInterval(this.refresher)
        this.refresher = null
      }
    },
    downloadReviewed() {
      this.$refs.downloadForm.show()
    },
    onDownloadSubmit(formData) {
      let form = {
        isPreview: formData.isPreview,
        scoreMerge: formData.scoreMerge,
        showQsScore: formData.showQsScore,
        isReversed: formData.isReversed,
        isRotate: formData.isRotate,
        taskIds: formData.tasks.map(task => task.id),
        remoteFileProps: {
          save: formData.save2Remote
        },
        printerProps: {
          print: !!formData.printer
        }
      }
      let loadingMessage = this.$message({
        message: "正在生成批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/download/reviewed", form).then(res => {
        return this.downloadFile(res.data.fileUrl, `${formData.tasks[0].name} 批改结果(${formData.isPreview ? '含原卷' : '不含原卷'}).pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
    downloadFile(url, name) {
      // 使用fetch获取文件内容
      return fetch(this.$fileserver.fileurl(url))
          .then(response => response.blob())
          .then(blob => {
            // 如果需要下载，可以使用前面提到的下载代码
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = URL.createObjectURL(blob);
            a.download = name;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(a.href);
          })
          .catch(error => {
            console.error('发生错误:', error);
          });
    },
    pdfSplit() {
      this.$refs.pdfParseForm.show()
    },
    loadStatusStats() {
      this.$axios.get("/api/docCorrectTask/status/stats").then(res => {
        let stats = {
          1: 0,
          2: 0,
          3: 0,
          total: 0,
        }
        res.data.forEach(item => {
          stats[item.status] = item.count
          if (item.status !== 1) {
            stats.total += item.count
          }
        })
        this.statusStats = stats
        this.refreshTasks()
      })
    },
    correctTask(taskId) {
      const store = useUserStore();
      let form = {
        id: taskId,
        aimodel: store.getDefaultCorrectModal,
        ocrType: '2',
        responseFormat: true,
        jsonobject: store.getDefaultJsonObject,
        jsonschema: store.getDefaultJsonSchema,
      }
      this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
        this.$message.success("提交成功")
        this.loadData()
        this.loadStatusStats()
      })
    },
    downloadStats() {
      this.$refs.statsDownloadForm.show()
    },
    onStatsDownloadSubmit(formData) {
      let form = {
        taskIds: formData.tasks.map(task => task.id),
        fontSize: formData.fontSize,
        segedScore: formData.segedScore,
        remoteFileProps: {
          save: formData.save2Remote
        },
        printerProps: {
          print: !!formData.printer
        }
      }
      let loadingMessage = this.$message({
        message: "正在生成统计结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/download/stats", form).then(res => {
        return this.downloadFile(res.data.fileUrl, `${formData.tasks[0].name} 统计结果.pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
    correctName(id) {
      this.$axios.post("/api/docCorrectTask/correctName", {
        id,
      }).then(res => {
        this.$message.success("校正中，请稍后查看...")
      })
    },
    doCorrectName(command, id) {
      if (command === 'sync') {
        // 同步另一张试卷的姓名
        this.$refs.taskSelector.show({
          title: "同步试卷姓名", data: {
            id,
          }
        })
      }
    },
    doSyncDocName(formData) {
      this.$message.success("同步中，请稍后查看...")
      this.$axios.post("/api/docCorrectTask/syncDocName", formData).then(() => {
        this.$message.success("同步成功")
      })
    },
    saveResult(taskId, rowIdx) {
      this.resultSaving[rowIdx] = true
      this.$axios.post(`/api/docCorrectResult/saveTask?taskId=${taskId}`).then(res => {
        this.$message.success("保存成功")
      }).finally(() => {
        this.resultSaving[rowIdx] = false
      })
    },
    downloadZip() {
      this.$refs.zipFileForm.show()
    },
    onZipSubmit(formData) {
      const tasks = formData.tasks
      if (!tasks && tasks.length === 0) {
        this.$message.error("请至少选择一份试卷")
        return
      }
      this.zipFileDownloading = true
      const name = tasks[0].name
      let loadingMessage = this.$message({
        message: "正在打包下载批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/file/zip", {
        ...formData,
        taskIds: formData.tasks.map(t => t.id),
        name,
        remoteFileProps: {
          save: formData.save2Remote
        },
        printerProps: {
          print: !!formData.printer
        }
      }).then(res => {
        this.downloadFile(res.data.fileUrl, `${name} 批改结果.zip`).then(() => {
          this.$message.success("批改结果生成成功!")
        })
      }).finally(() => {
        this.zipFileDownloading = false
        loadingMessage.close()
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>