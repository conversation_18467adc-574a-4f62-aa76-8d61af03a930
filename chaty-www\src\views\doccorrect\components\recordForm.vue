<template>
  <el-dialog v-model="isShow" :title="title" width="500" :before-close="onClose" append-to-body>
    <el-form :model="form" :label-width="100">
      <div v-if="nameExactMatch" class="match-tip">
        检测到完全匹配的姓名：{{ form.identify }}，是否填入学号：{{ nameExactMatch.studentId }}？
        <span class="action-link" @click="fillStudentNumber">立即填入</span>
      </div>
      <div v-if="studentNumberExactMatch" class="match-tip">
        检测到完全匹配的学号：{{ form.studentNumber }}，是否填入姓名：{{ studentNumberExactMatch.nickname }}？
        <span class="action-link" @click="fillIdentify">立即填入</span>
      </div>
      <el-form-item label="名称：" prop="name">
        <el-input v-model="form.name" disabled style="width: 320px"/>
      </el-form-item>
      <el-form-item label="姓名：">
        <el-autocomplete
            v-model="form.identify"
            placeholder="请输入姓名"
            :fetch-suggestions="querySearchName"
            value-key="nickname"
            style="width: 320px" />
      </el-form-item>
      <el-form-item label="学号：" >
        <el-autocomplete
            v-model="form.studentNumber"
            type="number"
            placeholder="请输入学号"
            :fetch-suggestions="querySearchStudentNumber"
            value-key="studentId"
            style="width: 320px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button type="primary" @click="doSubmit" :loading="submitLoading">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      form: {
        name: '',
        identify: '',
        studentNumber: ''
      },
      submitLoading: false,
      isShow: false,
      title: "试卷编辑",
      students: [] // 建议初始化为数组，但同时在代码中增加判空措施
    }
  },
  computed: {
    nameExactMatch() {
      if (!this.students) {
        return null;
      }
      const student = this.students.find(student => student.nickname === this.form.identify) || null;
      return student && student.studentId === this.form.studentNumber ? null : student;
    },
    studentNumberExactMatch() {
      if (!this.students) {
        return null;
      }
      const student = this.students.find(student => student.studentId === this.form.studentNumber) || null;
      return student && student.nickname === this.form.identify ? null : student;
    }
  },
  methods: {
    querySearchName(queryString, cb) {
      const list = this.students ? this.students : [];
      const results = queryString
          ? list.filter(student => student.nickname.toLowerCase().includes(queryString.toLowerCase()))
          : list;
      cb(results);
    },
    querySearchStudentNumber(queryString, cb) {
      const list = this.students ? this.students : [];
      const results = queryString
          ? list.filter(student => student.studentId.toLowerCase().includes(queryString.toLowerCase()))
          : list;
      cb(results);
    },
    onClose() {
      this.$emit("onClose", this.form);
      this.isShow = false;
      this.form = {
        name: '',
        identify: '',
        studentNumber: ''
      }
    },
    show(data, students) {
      this.students = students;
      this.form = Object.assign({}, this.form, data);
      this.isShow = true;
    },
    doSubmit() {
      this.submitLoading = true;
      const param = {
        id: this.form.id,
        identify: this.form.identify,
        studentNumber: this.form.studentNumber
      };
      this.$axios.post("/api/docCorrectRecord/update", param).then(res => {
        this.submitLoading = false;
        this.$message.success("更新成功");
        this.onClose();
      });
    },
    fillStudentNumber() {
      if (this.nameExactMatch) {
        this.form.studentNumber = this.nameExactMatch.studentId;
      }
    },
    fillIdentify() {
      if (this.studentNumberExactMatch) {
        this.form.identify = this.studentNumberExactMatch.nickname;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.match-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
.action-link {
  text-decoration: underline;
  cursor: pointer;
  margin-left: 5px;
}
</style>
