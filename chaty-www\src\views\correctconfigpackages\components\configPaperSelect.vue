<template>
  <el-dialog
      title="选择试卷标准卷"
      v-model="isShow"
      width="500px"
      @close="handleClose"
  >
    <el-select
        v-model="selectedPackageId"
        placeholder="输入试卷标准卷名称 搜索添加"
        filterable
        clearable
        remote
        :remote-method="loadPackageOptions"
        :loading="loading"
        style="width: 100%;"
    >
      <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.name"
          :value="item.id"
      />
    </el-select>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="confirmSelect">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'StandardPackageSelectorDialog',
  data() {
    return {
      isShow: false,
      selectedPackageId: null,
      options: [],
      loading: false,
      configIndex: -1
    };
  },
  methods: {
    /** 外部调用：打开对话框 */
    show(configIndex = -1) {
      this.configIndex = configIndex;
      this.isShow = true;
    },
    /** 关闭对话框并重置状态 */
    handleClose() {
      this.isShow = false;
      this.selectedPackageId = null;
      this.options = [];
      this.loading = false;
      this.configIndex = -1;
    },
    /** 点击“确定”后抛出事件并关闭 */
    confirmSelect() {
      if (this.selectedPackageId == null) {
        this.$message.warning('请先选择一个试卷包');
        return;
      }
      this.$emit('confirm', this.selectedPackageId, this.configIndex);
      this.handleClose();
    },
    /** 远程搜索接口 */
    loadPackageOptions(query) {
      if (!query) {
        this.options = [];
        return;
      }
      this.loading = true;
      this.$axios
          .post('/api/docCorrectConfig/page', {
            name: query,
            page: { pageNumber: 1, pageSize: -1 }
          })
          .then(res => {
            this.options = res.data.records;
          })
          .finally(() => {
            this.loading = false;
          });
    }
  }
};
</script>

<style scoped>
/* 根据需要自行调整 */
</style>
