<template>
  <div>
    <el-form :model="form" ref="formRef">
      <div class="container">
        <el-page-header class="header-bar" @back="onBack">
          <template #content>
            <div style="display: flex">
              <el-image src="/icon/16.png" class="icon"></el-image>
              <el-text class="text">批改作文</el-text>
            </div>
          </template>
        </el-page-header>
        <div class="bottom-area">
          <div class="left-content">
            <!-- <el-image src="/icon/essayStep.png" class="step"></el-image> -->
            <el-button
                style="background: transparent;border: none;width: 540px;height: 42px;z-index: 9999;position: fixed;top: 125px;left: 210px"
                ref="yinDaoRef1" id="yinDaoRef1"></el-button>
            <el-button
                style="background: transparent;border: none;width: 480px;height: 42px;z-index: 9999;position: fixed;top: 125px;left: 750px"
                ref="yinDaoRef2" id="yinDaoRef2"></el-button>

            <div class="description" style="margin-top: 25px">
              <div class="divider"></div>
              <div class="title">选择上传方式：</div>
            </div>
            <div class="bottom">
              <div class="upload-area">
                <el-upload v-if="isEnglishEssay" class="upload-container" ref="pdfUploader" accept=".pdf"
                           drag
                           :data="getParseConfig"
                           :action="$fileserver.uploadUrl"
                           :with-credentials="true"
                           :show-file-list="true"
                           :auto-upload="false"
                           :file-list="docList"
                           :on-change="onDocChange"
                           :on-success="uploadDoc">
                  <div class="upload-item">
                    <el-image src="/icon/upload1.svg" class="left-content"></el-image>
                    <div class="right-content">
                      <div class="title">文件上传</div>
                      <div class="subtitle">请点击文件上传到此处上传，可上传PDF格式的文件<br>
                        支持作文快速上传
                      </div>
                    </div>
                  </div>

                </el-upload>

                <el-upload v-if="isEnglishEssay" class="upload-container" accept=".pdf"
                           :with-credentials="true"
                           :show-file-list="true"
                           ref="remoteUploaderRef"
                           :file-list="remoteFileList"
                           :auto-upload="false">
                  <div class="upload-item" @click.stop="uploadDoc">
                    <el-image src="/icon/upload2.svg" class="left-content"></el-image>
                    <div class="right-content">
                      <div class="title">打印机文件上传</div>
                      <div class="subtitle">支持多份试卷批量上传</div>
                    </div>
                  </div>
                </el-upload>
                <el-upload v-else
                           class="upload-container"
                           ref="imageUploader"
                           accept=".png,.jpg,.jpeg"
                           drag
                           multiple
                           :action="$fileserver.imageToPdfUrl"
                           :data="getParseConfig"
                           :with-credentials="true"
                           :show-file-list="true"
                           :file-list="imageList"
                           :on-change="onImageChange"
                           :on-success="uploadImagePdf"
                           limit="100"
                >
                  <div class="upload-item">
                    <el-image src="/icon/upload1.svg" class="left-content"></el-image>
                    <div class="right-content">
                      <div class="title">文件上传</div>
                      <div class="subtitle">请点击文件，可上传PNG或JPG格式的图片<br>支持作文快速上传</div>
                    </div>
                  </div>
                </el-upload>


              </div>


            </div>
            <div class="description">
              <div class="divider"></div>
              <div class="title">文件信息：</div>
            </div>

            <div class="file-area">
              <el-form-item label="名称：" prop="name" label-width="120px">
                <el-input style="width: 200px" v-model="form.name"/>
              </el-form-item>
              <el-form-item label="批改模型：" prop="code" label-width="120px">
                <el-select style="margin-bottom: 10px;width: 200px" suffix-icon="Search"
                           placeholder="请选择模型"
                           v-model="form.modelValue">
                  <el-option v-for="item in getAimodelOptions" :key="item.value" :label="item.label" :value="item.value"
                             style="width: 200px"/>
                </el-select>
              </el-form-item>
              <el-form-item v-if="isEnglishEssay" label="文件大小：" label-width="120px" >
                <el-input style="width: 200px" v-model="fileInformation.size" disabled/>
              </el-form-item>
              <el-form-item label="试卷份数：" label-width="120px">
                <el-input style="width: 200px" v-model="fileInformation.cnt" disabled/>
              </el-form-item>
              <el-form-item v-if="isEnglishEssay" label="预计批改时间：" label-width="120px">
                <el-input style="width: 200px"  v-model="fileInformation.correctTime" disabled/>
              </el-form-item>
              <!--          <el-form-item label="页数：" prop="pageNum" label-width="90">-->
              <!--            <el-input-number style="width: 200px" :min="1" :controls="false" v-model="form.pageNum"-->
              <!--                             :value-on-clear="1"/>-->
              <!--          </el-form-item>-->
            </div>

            <div class="description" style="margin-bottom: 12px">
              <div class="divider"></div>
              <div class="title">作文信息：</div>
            </div>
            <task-form ref="taskForm" @onClose="" @changeIsEnglishEssay="changeIsEnglishEssay"/>
          </div>

          <div class="right-content">
            <cropper ref="cropper"
                     class="canvas"
                     :loading="loadingCropper"
                     :is-empty="!cropperImageList.length"
                     :default-is-freeze-keyboard-without-force="true"
                     @arrow-key-pressed="arrowKeyPressed">
            </cropper>
            <div class="pagination" v-show="cropperImageList.length">
              <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="cropperImageList.length"
                  v-model:current-page="nowCropperPageIdx"
                  :page-size="1"
                  class="right"
              />
            </div>
          </div>
        </div>

        <div style="flex-grow: 1;"></div>
        <div class="bottom-button-area">
          <el-button class="confirm-button" @click="onSubmit">下一步</el-button>
          <el-button class="cancel-button" @click="onBack">取消</el-button>
        </div>
      </div>
    </el-form>
    <el-tour v-model="needEssayGuide" type="primary" :mask="false" @close="finishTour">
      <el-tour-step target="#yinDaoRef1" title="上传样卷" description="首先需要上传班级作文，并填写这一批作文的名称">
      </el-tour-step>
      <el-tour-step
          target="#yinDaoRef2"
          title="批改作文"
          description="上传完成后，点击下一步，开始批改作文"
      />
    </el-tour>
  </div>
</template>

<script>
import {ElLoading} from "element-plus";
import TaskForm from "@/views/essay/components/taskform.vue";
import {useUserStore} from "@/store";
import {mapActions, mapState} from "pinia";
import Cropper from "@/components/cropper/forPreview.vue";
import * as pdfjsLib from "pdfjs-dist";
function formatDate(date) {
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以下标加1
  const dd = String(date.getDate()).padStart(2, '0');
  const hh = String(date.getHours()).padStart(2, '0');
  const min = String(date.getMinutes()).padStart(2, '0');

  return `${yyyy}-${mm}-${dd} ${hh}:${min}`;
}
const store = useUserStore();
export default {
  components: {Cropper, TaskForm},
  data() {
    return {
      title: '开始批改作文',
      form: {
        name: '',
        pageNum: 1,
        modelValue: store.getDefaultCorrectModal,
        url: ''
      },
      docList: [],
      action: 'correct',
      codeLoading: false,
      loadingInstance: null,
      fileInformation: {
        size: '',
        cnt: null,
        correctTime: null
      },
      files: null,
      packagesLoading: false,
      packagesName: '',
      packageConfigDetail: [],
      useDefaultConfig: false,
      uploadFile: null,
      fileUploading: false,
      loadingCropper: false,
      cropperImageList: [],
      nowCropperPageIdx: 1,
      imgDataUrl: '',
      filenames: [],
      imagesList: {},
      isEnglishEssay: true,
      imageFileList: [],
      imageList: []
    }
  },
  computed: {
    ...mapState(useUserStore, ['getAimodelOptions', 'needEssayGuide']),
    // 根据选中状态动态设置样式
    containerStyles() {
      if (this.fileInformation.size)
        return {
          backgroundColor: '#efeeff',
          borderColor: '#332CF5',
          borderWidth: 'thin',
          borderStyle: 'solid',
        };
      else return {};
    },
  },
  watch: {
    "form.pages.length": {
      handler(val) {
        this.form.pageNum = val;
      }
    },
    'nowCropperPageIdx': {
      handler(val) {
        this.refresh();
      }
    },
    'form.pageNum': {
      handler(val) {
        this.refreshPdfs();
      }
    },
  },
  methods: {
    ...mapActions(useUserStore, ['setGuideStatus']),
    onImageChange(file, fileList) {
      if (!this.form.name) {
        this.form.name = '图片批改-' + formatDate(new Date());;
      }
    },
    async uploadImagePdf(response, file, fileList) {
      console.log('response', response)
      this.imageFileList.push({
        url: response.data
      })
      let files = this.imageFileList
      if (!files || files.length === 0) {
        this.$message.error("文件上传失败!");
        return;
      }
      this.filenames = this.imageFileList;
      this.fileInformation.cnt = files.length;
      this.files = files;
      this.fileUploading = false;

      const pdf2ImgResponse = await this.$axios.get("/api/file/pdf2Img?filename=" + response.data.split('/').pop() + '&pageNum=1');
      if (!pdf2ImgResponse.data) {
        this.$message.error("文件转换失败");
        return;
      }
      this.cropperImageList.push({
        pdfUrl: pdf2ImgResponse.data.docpath,
        imageUrl: pdf2ImgResponse.data.url
      })
      this.refresh()
    },

    changeIsEnglishEssay(e) {
      this.isEnglishEssay = e;
    },
    finishTour() {
      this.setGuideStatus('essay', false)
    },
    arrowKeyPressed(e) {
      let pageIdx = this.nowCropperPageIdx;
      if (e === 1) {
        if (pageIdx === this.cropperImageList.length) {
          // 到头之后不再重新
          this.$message.warning('已经是最后一页');
        } else {
          this.nowCropperPageIdx = e + pageIdx;
        }
      } else if (e === -1) {
        if (pageIdx === 1) {
          // 到头之后不再重新
          this.$message.warning('已经是第一页');
        } else {
          this.nowCropperPageIdx = e + pageIdx;
        }
      }
    },
    preview() {
      this.$refs.cropper.setImg(this.imgDataUrl, [], []);
    },
    async loadNextImage(nextDocUrl) {
      if (!nextDocUrl) return;
      if (nextDocUrl in this.imagesList) return;
      try {
        const response = await fetch(this.$fileserver.fileurl(nextDocUrl));

        if (!response.ok) {
          throw new Error('Failed to fetch PDF');
        }

        const blob = await response.blob();
        const pdfData = await blob.arrayBuffer();
        const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
        const totalPages = pdfDoc.numPages;
        const images = [];
        const targetDPI = 300;
        const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

        if (totalPages === 1) {
          const page = await pdfDoc.getPage(1);
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          const viewport = page.getViewport({scale: scaleFactor});
          canvas.width = viewport.width;
          canvas.height = viewport.height;

          // 将页面渲染到 canvas 上
          await page.render({
            canvasContext: context,
            viewport: viewport,
          }).promise;

          // 将 canvas 转换为 Base64 编码的图片（JPEG 格式）
          const imgDataUrl = canvas.toDataURL('image/jpeg');
          images.push(imgDataUrl);

          const keys = Object.keys(this.imagesList);
          if (keys.length > 30) {
            const firstKey = keys[0];
            delete this.imagesList[firstKey];
          }
          this.imagesList[nextDocUrl] = imgDataUrl;
        }

      } catch (error) {
        console.error('加载下一个图像失败', error);
      }
    },
    async refreshPdfs() {
      console.log(this.filenames)
      for (let pageNum = 1; pageNum <= this.filenames.length; pageNum++) {
        if (pageNum - 1 < this.cropperImageList.length) {
          continue;
        }
        const pdf2ImgResponse = await this.$axios.get("/api/file/pdf2Img?filename=" + this.filenames[pageNum - 1].url.split('/').pop() + '&pageNum=1');
        if (!pdf2ImgResponse.data) {
          this.$message.error("文件转换失败");
          return;
        }
        this.cropperImageList.push({
          pdfUrl: pdf2ImgResponse.data.docpath,
          imageUrl: pdf2ImgResponse.data.url
        })
        if (pageNum === 1) {
          this.refresh()
        }
      }
    },
    refresh() {
      this.imgDataUrl = this.cropperImageList[this.nowCropperPageIdx - 1];
      this.convertPdfUrlToBase64(this.cropperImageList[this.nowCropperPageIdx - 1].pdfUrl, this.cropperImageList[this.nowCropperPageIdx]?.pdfUrl);
    },
    async convertPdfUrlToBase64(pdfUrl, nextDocUrl) {
      this.loadingCropper = true;
      try {
        // 通过 fetch 下载 PDF 文件
        if (pdfUrl in this.imagesList) {
          this.imgDataUrl = this.imagesList[pdfUrl];
          this.loadingCropper = false;
          this.preview();
          this.loadNextImage(nextDocUrl)
        } else {
          const response = await fetch(this.$fileserver.fileurl(pdfUrl));
          // 异步提前缓存下一个
          this.loadNextImage(nextDocUrl)
          if (!response.ok) {
            throw new Error('Failed to fetch PDF');
          }
          const blob = await response.blob();
          const pdfData = await blob.arrayBuffer();
          const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;

          // 获取 PDF 的总页数
          const totalPages = pdfDoc.numPages;
          const images = [];

          // 设置目标 DPI（300 DPI）
          const targetDPI = 300;

          // 获取每英寸的像素数，假设 PDF 使用标准的 72 DPI
          const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

          // 遍历每一页，将其转换为 Base64 图像
          if (totalPages === 1) {
            const page = await pdfDoc.getPage(1);

            // 创建一个 canvas 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 获取页面的渲染视口
            const viewport = page.getViewport({scale: scaleFactor});

            // 设置 canvas 大小为页面大小
            canvas.width = viewport.width;
            canvas.height = viewport.height;

            // 将页面渲染到 canvas 上
            await page.render({
              canvasContext: context,
              viewport: viewport,
            }).promise;
            const imgDataUrl = canvas.toDataURL('image/jpeg');
            images.push(imgDataUrl);
            this.loadingCropper = false;
            this.imgDataUrl = imgDataUrl;
            this.imagesList[pdfUrl] = imgDataUrl;
            this.preview();
          }
        }

      } catch (error) {
        console.error('PDF 转换失败', error);
      }
    },
    onBack() {
      this.$router.back()
    },
    async onPackagesSelect(e) {
      this.form.packagesConfig = e;
      let configIds = JSON.parse(e.config);
      this.packageConfigDetail = []
      for (let i = 0; i < configIds.length; i++) {
        const response = await this.$axios.get(`/api/docCorrectConfig/get?id=${configIds[i]}`);
        if (!response.data) {
          this.$message.error("获取配置失败");
          return;
        }
        this.packageConfigDetail.push(response.data);
      }
      // 渲染每一页的配置
      let pages = []
      this.packageConfigDetail.forEach((item, index) => {
        pages.push({
          configId: item.id,
          configName: item.name,
          configOptions: [
            {id: item.id, name: item.name}
          ],
          docs: [],
          configLoading: false,
          isParse: true,
          isRotation: false,
          options: []
        })
      });
      this.form.pages = pages;
    },
    onClose(id) {
      this.$refs.formRef.resetFields()
      this.docList = []
      this.isShow = false
      this.$emit("finishUpload", id);
    },
    onSubmit(action) {
      this.action = action || 'correct'
      if ((this.docList.length === 0 && this.isEnglishEssay) || (!this.isEnglishEssay && this.imageFileList.length === 0)) {
        this.$message.error("请上传试卷");
        return;
      }
      if (this.fileUploading) {
        this.$message.warning("请等待试卷上传完成");
        return;
      }
      this.submit()
    },
    submit() {
      this.loadingInstance = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      let isExecute = false;
      let essayData = this.$refs.taskForm.form
      let createForm = {
        ...this.form,
        pages: [{
          isParse: false,
          isRotation: false,
          configId: '',
          configOptions: [],
          configLoading: false,
          docs: this.files
        }],
        essayData,
        isEssay: 1
      };

      const timerId = setInterval(() => {
        if (createForm && !isExecute) {
          isExecute = true;
          clearInterval(timerId);
          // 新增对应配置模型，首先需要pdf转image
          let pdfName = this.files[0].url.split('/').pop();
          this.$axios.get('/api/file/pdf2Img?filename=' + pdfName + '&pageNum=1').then(pdf2ImgRes => {
            let img = pdf2ImgRes.data.url;
            let imageInfo = pdf2ImgRes.data.imageInfo;
            let areas = [
              {
                "areaType": 3,
                "area": {
                  ...imageInfo,
                  "scaleX": 1,
                  "scaleY": 1
                },
                "enabled": true,
                "opinion": 1,
                "questions": [
                  {
                    "name": essayData.name,
                    "question": essayData.essayTitle,
                    "qsInfo": JSON.stringify(essayData),
                    "answer": "无",
                    "score": essayData.score,
                    "isAdditional": 1,
                    "flagArea": {
                      "x": 0,
                      "y": 0,
                      "width": 1,
                      "height": 1,
                      "rotate": 0,
                      "scaleX": 1,
                      "scaleY": 1
                    },
                    "reviewType": 2,
                    "defaultReview": "",
                    "scoreType": "总分",
                    "opinion": 2,
                    "isScorePoint": 0,
                    "scorePoints": ""
                  }
                ]
              }
            ]
            let addModalForm = {
              name: this.form.name + "_作文配置",
              docurl: this.files[0].url,
              img: img,
              docType: store.getDefaultDocType,
              config: "{\"score\":true,\"scoreFormat\":1,\"scoreColor\":\"red\",\"scoreFontSize\":10,\"fontSize\":10,\"flagSize\":20,\"flagColor\":\"red\",\"errorFlagColor\":\"red\",\"errorFlagSize\":20,\"errorFlag\":\"x\",\"correctFlag\":\"a\",\"nameArea\":null,\"scoreArea\":null,\"additionalName\":\"附加\",\"prompt\":null,\"scoreTypes\":[\"总分\"]}",
              areas: JSON.stringify(areas)
            }
            this.$axios.post("/api/docCorrectConfig/update", addModalForm).then(docCorrectConfigUpdateRes => {
              let configId = docCorrectConfigUpdateRes.data.id;
              Object.assign(createForm, {
                configId: configId,
                configName: this.form.name + "_作文配置",
                configOptions: [
                  {id: configId, name: this.form.name + "_作文配置"}
                ]
              });
              createForm.pages[0].configId = configId
              this.$axios.post("/api/docCorrectFile/create", createForm).then(taskAddRes => {
                this.$message.success("文件上传成功");
                this.onClose(taskAddRes.data.id);
              }).finally(() => {
                if (this.loadingInstance) {
                  this.loadingInstance.close();
                }
              });

            });
          });
        }
      }, 1000);
    },
    async uploadDoc(response) {
      this.form.url = response.data.url;
      const multiPdfRes = await this.$axios.post('/api/file/upload/multiPdf', {
        config: JSON.stringify({
          ...this.getParseConfig(),
          file: response.data.url
        })
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      let files = multiPdfRes.data
      if (!files || files.length === 0) {
        this.$message.error("文件上传失败!");
        return;
      }
      this.filenames = multiPdfRes.data[0];
      // 计算页数和预计时间
      const count = multiPdfRes.data.reduce((total, innerArray) => total + innerArray.length, 0);
      this.fileInformation.cnt = count;
      this.fileInformation.correctTime = Math.floor(count / 10) || 1 + '分钟';

      this.files = files[0];
      this.fileUploading = false;
      this.cropperImageList = [];
      await this.refreshPdfs();
    },
    onDocChange(uploadFile) {
      if (uploadFile === this.uploadFile) {
        return;
      }
      this.uploadFile = uploadFile;
      this.fileInformation.size = (uploadFile.size / 1024 / 1024).toFixed(2) + 'MB';
      let status = uploadFile.status
      if (status === 'ready') {
        this.docList = [uploadFile]
        this.form.name = uploadFile.name?.split('.')[0]
      }
      this.packagesName = uploadFile.name?.split('.')[0] + "_配置";
      this.useDefaultConfig = true;
      this.fileUploading = true;
      this.$refs.pdfUploader.submit();
    },
    getParseConfig() {
      let form = JSON.parse(JSON.stringify(this.form));
      form.pageNum = 1;
      return {
        config: JSON.stringify(form)
      };
    },
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small) {
  margin-bottom: 5px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

.file-uploader {
  width: 200px;

  :deep(.el-upload-dragger) {
    padding: 0;
    text-align: left;
    width: 482px;
  }
}

.uploadFileContainer {
  transition: all 0.3s ease;
  display: flex;
  width: 500px;
  height: 90px;
  border: 1px solid #D8D8D8;
  border-radius: 10px;
  align-items: center;

  .left-content {
    width: 30px;
    height: 30px;
    margin: 15px;
  }

  .right-content {
    display: flex;
    flex-direction: column;
    height: 70px;

    .title {
      font-size: 15px;
      font-weight: bold;
      color: #332CF5;
    }

    .subtitle {
      font-size: 13px;
      color: #8F959E;
      line-height: 1.2;
    }
  }
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header-bar {
    display: flex;
    align-items: center;

    .icon {
      width: 28.28px;
      height: 22.89px;
    }

    .text {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-left: 10px;
    }
  }

  .bottom-area {
    display: flex;
    width: 100%;
    gap: 20px;

    .left-content {
      flex-shrink: 0;

      .step {
        width: 800px;
        margin-top: 20px;
      }

      .bottom {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        margin-left: 12px;

        .upload-area {
          display: flex;
          gap: 30px;
          width: 100%;

          .upload-container {
            width: 406px;
            height: 118px;
            display: flex;
            flex-direction: column;

            :deep(.el-upload-dragger) {
              padding: 0;
              text-align: left;
            }

            .upload-item {
              width: 506px;
              height: 98px;
              background: #3981ff1a;
              border-radius: 6px;
              align-items: center;
              display: flex;

              .el-upload-list {
                position: fixed !important;
                top: 10px;
              }

              .left-content {
                width: 60.58px;
                height: 58px;
                margin-left: 10px;
              }

              .right-content {
                display: flex;
                flex-direction: column;
                margin-left: 9px;
                width: 100%;
                align-items: flex-start;

                .title {
                  font-size: 16px;
                  color: #3981FF;
                  font-weight: bolder;
                }

                .subtitle {
                  font-size: 13px;
                  color: #999999;
                  letter-spacing: 0;
                  margin-top: 7px;
                }
              }

            }
          }

        }
      }

      .description {
        display: flex;
        align-items: center;
        margin-left: 12px;
        margin-top: 20px;

        .divider {
          width: 3px;
          height: 16px;
          top: 2.5px;
          border-radius: 2px 0 0 0;
          background: #3981FF;
        }

        .title {
          font-size: 15px;
          font-weight: bolder;
          line-height: 21px;
          text-align: left;
          margin-left: 6px;
        }
      }

      .file-area {
        margin-top: 29.5px;
        margin-left: 12px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        width: 800px;
        gap: 12px 68px;

        .item {
          width: 220px;
          height: 32px;
        }
      }
    }

    .right-content {
      height: calc(100vh - 200px);
      padding: 0 0 10px 0;
      width: 100%;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .canvas {
        height: 100%;
        width: 100%;
      }

      .pagination {
        height: 50px;
        margin-top: 5px;
        display: flex;
        align-items: center;

        .left {
          width: 200px;
          flex-shrink: 0;
        }

        .right {
          flex-grow: 1;
        }
      }
    }
  }

  .bottom-button-area {
    display: flex;
    height: 45px;
    width: 100%;
    flex-direction: row-reverse;
    gap: 24px;
    border-top: 2px solid #eeeeee;
    padding: 13px 0 0 0;

    .cancel-button {
      width: 74px;
      height: 32px;
      background: #FFFFFF;
      border: 1px solid #00000026;
      border-radius: 6px;

      .el-button__text {
        font-weight: 400;
        font-size: 14px;
        color: #000000e0;
        text-align: center;
        line-height: 22px;
      }
    }

    .confirm-button {
      width: 74px;
      height: 32px;
      background: #1677FF;
      border-radius: 6px;
      color: #FFFFFF !important;
    }
  }

}
</style>