<template>
  <el-dialog v-model="isShow" title="选择文件" width="1000px" :before-close="onClose">
    <div style="display: flex;justify-content: space-between">
      <el-breadcrumb class="breadcrumb" separator-icon="ArrowRight">
        <el-breadcrumb-item v-for="(path, index) in paths" :key="index">
          <el-button @click="onPathSelect(index)" text link>{{ index === 0 ? '首页' : path }}</el-button>
        </el-breadcrumb-item>
      </el-breadcrumb>
      <el-button type="primary" @click="openDialog">新建文件夹</el-button>
    </div>

    <el-table :data="files" v-loading="loading" height="450px" :highlight-current-row="true" @row-click="onFileSelect"
              :row-style="{ cursor: 'pointer' }"
              empty-text="无数据">
      <el-table-column prop="name" label="名称" width="600px" show-overflow-tooltip>
        <template #default="{ row }">
          <el-space>
            <template v-if="row.type === 1">
              <el-image src="/icon/folder.svg" style="width: 28px"></el-image>
            </template>
            <template v-else>
              <el-image src="/icon/pdf.svg" style="width: 28px"></el-image>
            </template>
            <el-text>{{ row.name }}</el-text>
          </el-space>
        </template>
      </el-table-column>
      <el-table-column prop="size" label="大小">
        <template #default="{ row }">
          {{ row.type === 1 ? '' : formatFileSize(row.size) }}
        </template>
      </el-table-column>
      <el-table-column prop="modified" label="修改时间">
        <template #default="{ row }">
          {{ dayjs(row.modified).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </el-table>
    <printer-setting ref="printerSettingRef" @printerSettingsSubmit="printFile"></printer-setting>
    <el-dialog v-model="isFolderNameDialogShow" title="输入文件夹名称" width="400px" @close="closeDialog">
      <el-input v-model="folderName" placeholder="输入文件夹名称，不要重复" style="padding: 20px"></el-input>
      <div style="text-align: right;padding: 10px 20px">
        <el-button  @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="createFolder" >确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { Folder, Document } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import PrinterSetting from './PrinterSetting.vue';

export default {
  components: {
    Folder,
    Document,
    PrinterSetting
  },
  data() {
    return {
      dayjs,
      isShow: false,
      loading: false, // 加载状态
      paths: [''], // 路径列表，用于面包屑导航
      files: [], // 文件列表
      selectFile: null,
      isFolderNameDialogShow: false,
      folderName: ''
    }
  },
  methods: {
    closeDialog() {
      this.isFolderNameDialogShow = false;
      this.$emit('closeInput');
    },
    openDialog() {
      this.isFolderNameDialogShow = true;
      this.$emit('openInput')
    },
    createFolder() {
      const path = this.paths.join('/');
      this.$axios.get(`/api/remote/file/createFolder?path=${path}&folderName=${this.folderName}`).then(res => {
        this.$message.success('创建成功');
        this.loadFiles();
      }).finally(() => {
      })
      this.closeDialog();
    },
    show() {
      this.isShow = true;
      this.loadFiles();
    },
    onClose() {
      this.paths = ['']
      this.files = []
      this.isShow = false;
    },
    // 加载文件
    loadFiles() {
      const path = this.paths.join('/');
      this.loading = true;
      this.$axios.get(`/api/remote/file/allList?path=${path}`).then(res => {
        this.files = res.data;
      }).finally(() => {
        this.loading = false;
      })
    },
    onPathSelect(index) {
      // 处理路径选择事件
      this.paths = this.paths.slice(0, index + 1);
      this.loadFiles();
    },
    formatFileSize(sizeInBytes) {
      const sizeInKB = sizeInBytes / 1024;
      if (sizeInKB < 1024) {
        return `${sizeInKB.toFixed(2)} K`;
      } else {
        const sizeInMB = sizeInKB / 1024;
        return `${sizeInMB.toFixed(2)} M`;
      }
    },
    onFileSelect(row) {
      if (row.type === 1) {
        // 如果是文件夹
        this.paths.push(row.name);
        this.loadFiles();
      } else {
        // 如果文件后缀名是 pdf
        if (row.name.endsWith('.pdf')) {
          // 弹窗确认选择该文件
          this.$confirm(`确定选择文件 ${row.name} 打印吗？`, '提示', {
            confirmButtonText: '下一步',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.selectFile = row;
            this.showPrinterSetting()
          })
        }
      }
    },
    showPrinterSetting() {
      this.$refs.printerSettingRef.show();
    },
    printFile(printerSetting) {
      const file = this.selectFile;
      const fileUrl = this.$fileserver.remoteOutFile(file, this.paths.join('/'));
      let form = Object.assign({
        print: true,
        url: fileUrl
      },printerSetting)
      this.$axios.post('/api/remote/file/print', form).then(res => {
        this.$message.success('打印成功');
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.breadcrumb {
  margin: 20px 0;
}
</style>