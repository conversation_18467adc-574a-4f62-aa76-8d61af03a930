<script>
import {mapActions} from "pinia";
import {useUserStore} from "@/store";

export default {
  created() {
    this.initGuideStatus()
  },
  methods: {
    ...mapActions(useUserStore, ['initGuideStatus']),
  }
}
</script>

<template>
  <div id="scale-wrapper">
    <div id="app-container">
      <router-view />
    </div>
  </div>
</template>
<style>
html, body, #app {
  height: 100vh;
  margin: 0;
}
#scale-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app-container {
  transform-origin: top left;
  transform: scale(0.9);
  width: calc(100vw / 0.9);
  height: calc(100vh / 0.9);
}
</style>

<style scoped>

:deep(.el-page-header__header) {
  width: 100% !important;
}

header {
  line-height: 1.5;
  max-height: 100vh;
}

:deep(.header-bar ) {
  flex-shrink: 0;
  height: 48px;
}

:deep(.el-button--primary) {
  background-color: #533ee3 !important;
  border-color: #533ee3 !important;
  color: white !important;
}

:deep(.el-button--small) {
  font-size: 13px !important;
}

:deep(.el-pagination li.is-active) {
  background-color: #EBF1FF !important;
  border: 1px solid #1759dd !important;
  color: #1664FF !important;
}
:deep(.el-pagination li),
:deep(.el-pagination button) {
  font-size: 13px;
  background: transparent !important;
  border: 1px solid #dde2e9 !important;
  border-radius: 4px !important;
}

:deep(.el-overlay-message-box) {
  padding: 0 !important;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

nav {
  width: 100%;
  font-size: 12px;
  text-align: center;
  margin-top: 2rem;
}

nav a.router-link-exact-active {
  color: var(--color-text);
}

nav a.router-link-exact-active:hover {
  background-color: transparent;
}

nav a {
  display: inline-block;
  padding: 0 1rem;
  border-left: 1px solid var(--color-border);
}

nav a:first-of-type {
  border: 0;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }

  nav {
    text-align: left;
    margin-left: -1rem;
    font-size: 1rem;

    padding: 1rem 0;
    margin-top: 1rem;
  }
}

</style>
