<template>
  <div class="menu-bar-grid">
    <div class="menu-left" @click="toHome">
      <el-tooltip placement="bottom" content="展开侧边栏">
        <el-image
            v-if="getIsCollapse"
            src="/icon/arrow_menu_collapse.png"
            class="collapse-icon"
            @click.stop="changeIsCollapse"
        />
      </el-tooltip>

      <el-image class="logo" src="/logo.png" @click.stop="toHome"/>
      <div class="title">纸质作业留痕批改机器</div>
    </div>

    <div class="menu-area-grid" v-show="getIsCollapse">
      <el-menu
          :default-active="activeIndex"
          mode="horizontal"
          :ellipsis="false"
          router
          active-text-color="#3981FF"
      >
        <el-menu-item
            v-for="item in menuItems"
            v-permission="item.authority"
            v-bind="item"
            :key="item.index"
        >
          <component class="icons" :is="item.icon"/>
          <template #title>{{ $t(item.t) }}</template>
        </el-menu-item>
      </el-menu>
    </div>

    <div class="menu-right">
      <el-image
          src="/icon/miaomiaoCaoZuo.png"
          class="left-image"
          @click="redirectToExternalLink"
      />
      <SchoolSelector style="margin-left: 20px"/>
      <el-image src="/icon/avatar.png" class="i18n-icon"/>

      <template v-if="getUser">
        <el-dropdown @command="onUserActions">
          <el-text type="primary" class="text">{{ getUser.username }}</el-text>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                  v-for="action in filteredUserActions"
                  :key="action.command"
                  :command="action.command"
              >
                <el-icon v-if="action.command === 'modelRequest'" style="margin-right: 6px;"><CollectionTag /></el-icon>
                <el-icon v-else-if="action.command === 'systemSetting'" style="margin-right: 6px;"><Setting /></el-icon>
                <el-icon v-else-if="action.command === 'SchoolPermissionManager'" style="margin-right: 6px;"><Lock /></el-icon>
                <el-icon v-else-if="action.command === 'QuestionTypeManager'" style="margin-right: 6px;"><Grid /></el-icon>
                <el-icon v-else-if="action.command === 'logout'" style="margin-right: 6px;"><SwitchButton /></el-icon>
                {{ action.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <template v-else>
        <el-button type="primary" text="primary" class="text" link @click="doLogin">
          {{ $t("login.login") }}
        </el-button>
      </template>
    </div>
  </div>
</template>

<script>
import { useUserStore } from "@/store/index";
import { mapState, mapActions } from "pinia";
import SchoolSelector from "../../components/SchoolSelector.vue";
import { CollectionTag, Setting, SwitchButton } from '@element-plus/icons-vue';

export default {
  name: "NavBar",
  components: { SchoolSelector },
  props: {
    menuItems: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeIndex: "/review",
      // 下拉菜单所有项及其权限标识
      userActions: [
        { command: 'systemSetting', label: '提示词管理',   authority: 'systemSetting' },
        { command: 'modelRequest',  label: '模型仓库',   authority: 'modelRequest'  },
        { command: 'SchoolPermissionManager',  label: '待办权限',   authority: 'SchoolPermissionManager'  },
        { command: 'QuestionTypeManager',  label: '题型管理',   authority: 'QuestionTypeManager'  },
        { command: 'logout',        label: this.$t('common.logout'), authority: '' }
      ],
    };
  },
  computed: {
    ...mapState(useUserStore, ["getUser", "isShowLogin", "getLocale", "getIsCollapse", "getRoles"]),
    filteredUserActions() {
      return this.userActions.filter(
          item => !item.authority || this.hasAuth(item.authority)
      );
    }
  },
  created() {
    this.setActiveIndex(this.$route.path);
    this.$i18n.locale = this.getLocale;
  },
  watch: {
    $route(to) {
      this.setActiveIndex(to.path);
    }
  },
  methods: {
    ...mapActions(useUserStore, ["showLogin", "setUser", "setLocale", "changeIsCollapse"]),
    setActiveIndex(path) {
      const pathSegments = path.split('/');
      if (pathSegments[1] === 'docconfig') {
        this.activeIndex = '/correctConfigPackages';
      } else if (pathSegments[1] === 'class') {
        this.activeIndex = '/class/config';
      } else if (pathSegments[1] === 'grading') {
        this.activeIndex = '/grading/correct';
      } else if (pathSegments[1] === 'survey') {
        this.activeIndex = '/survey/page';
      } else if (pathSegments[1] === 'errorCorrectionStatistics') {
        this.activeIndex = '/errorCorrectionStatistics/index';
      } else {
        this.activeIndex = '/' + (pathSegments[1] || '');
      }
    },

    redirectToExternalLink() {
      window.open(
          "https://scnduqrj7624.feishu.cn/wiki/LTCmwHSp1ieUNAk600icuuJFnch",
          "_blank"
      );
    },

    doLogin() {
      this.$router.push({ path: "/login" });
    },

    // 检查权限的通用方法
    hasAuth(name) {
      try {
        const rolesStr = this.getRoles?.[0]?.roleAuth || '';
        const parsed = JSON.parse(rolesStr) || {};
        const roles = Array.isArray(parsed.menus) ? parsed.menus : [];
        return roles.includes(name);
      } catch (err) {
        console.error('[hasAuth] 解析角色权限失败：', err);
        return false;
      }
    },

    onUserActions(command) {
      if (command === "logout") {
        this.doLogout();
      } else if (command === "systemSetting") {
        this.$router.push("/systemSetting/index");
      } else if (command === 'modelRequest') {
        this.$router.push("/modelRequest");
      } else if (command === 'SchoolPermissionManager') {
        this.$router.push('/SchoolPermissionManager')
      } else if (command === 'QuestionTypeManager') {
        this.$router.push('/QuestionTypeManager')
      }
    },

    doLogout() {
      this.$axios.post("/api/logout").then(() => {
        this.$message.success(this.$t("login.logoutSuccess"));
        this.setUser(null);
        this.$router.push("/login");
      });
    },

    toHome() {
      this.$router.push("/");
    },

    onLocaleChange(locale) {
      this.$i18n.locale = locale;
      this.setLocale(locale);
    },
  },
};
</script>

<style lang="scss" scoped>
.menu-bar-grid {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  height: 62px;
  background-image: linear-gradient(270deg, #2d7cf7 0%, #2eb5ff 98%);
  width: 100%;
  padding: 0 26px;
}

.menu-left {
  display: flex;
  align-items: center;
  column-gap: 5px;
  cursor: pointer;

  .collapse-icon {
    width: 20px;
    height: 20px;
  }

  .logo {
    width: 40px;
  }

  .title {
    font-weight: 700;
    font-size: 18px;
    color: #ffffff;
    letter-spacing: 1.5px;
  }
}

.menu-area-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  justify-self: center;

  ::v-deep .el-menu {
    height: 45px !important;
    line-height: 45px !important;
    background-color: transparent !important;
    box-shadow: none !important;
    border-bottom: none !important;
  }

  ::v-deep .el-menu-item {
    color: #ffffff !important;
    background-color: transparent !important;
  }

  ::v-deep .el-menu-item:hover {
    color: #333333 !important;
    background-color: transparent !important;
  }

  ::v-deep .el-menu-item.is-active {
    background-color: #ffffff !important;
    color: #2d7cf7 !important;
    border-radius: 4px;
  }
}

.menu-right {
  display: flex;
  align-items: center;
  justify-self: end;

  .left-image {
    cursor: pointer;
    background-image: linear-gradient(270deg, #abf478 0%, #f2fed7 98%);
    box-shadow: 0 2px 4px 0 #c5c5c580;
    border-radius: 14px;
    width: 185.89px;
    height: 30px;
  }

  .i18n-icon {
    width: 26px;
    height: 26px;
    margin-left: 42px;
    margin-right: 4.5px;
    border-radius: 100%;
  }

  .text {
    font-weight: 700;
    font-size: 16px;
    color: #ffffff;
    letter-spacing: 0;
    margin-left: 10px;
  }
}

::v-deep .el-dropdown-menu__item:not(.is-disabled):hover {
  font-weight: bold;
}
</style>
