<template>
  <el-dialog
      title="模型详情"
      v-model="detailDialogVisible"
      width="600px"
      @close="close"
  >
    <el-descriptions :column="1" border>
      <!-- 基本信息 -->
      <el-descriptions-item label="ID">
        {{ detailForm.modelRequestId ?? detailForm.id ?? '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="名称">
        {{ detailForm.label ?? detailForm.name ?? '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="模型值">
        {{ detailForm.value ?? detailForm.modelValue ?? '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="输出类型">
        {{ getResponseFormat(detailForm) }}
      </el-descriptions-item>

      <!-- 基础策略参数 -->
      <el-descriptions-item label="普通题目两轮询问">
        {{ fmtBool(detailForm.enableNormalQsTwoRequest) }}
      </el-descriptions-item>

      <!-- 两轮关闭：显示单轮提示词类型 -->
      <el-descriptions-item
          v-if="!isTwoRound"
          label="一轮问询提示词类型"
      >
        {{ fmtPrompt(detailForm.singleRoundPromptType) }}
      </el-descriptions-item>

      <!-- 两轮开启：显示第二轮相关 & 两轮提示词类型 -->
      <el-descriptions-item
          v-if="isTwoRound"
          label="第一轮提示词类型"
      >
        {{ fmtPrompt(detailForm.firstRoundPromptType) }}
      </el-descriptions-item>

      <el-descriptions-item
          v-if="isTwoRound"
          label="第二轮使用JSON比对"
      >
        {{ fmtBool(detailForm.isSecondRoundJsonComparison) }}
      </el-descriptions-item>

      <el-descriptions-item
          v-if="isTwoRound && !isJsonComparison"
          label="第二轮提示词类型"
      >
        {{ fmtPrompt(detailForm.secondRoundPromptType) }}
      </el-descriptions-item>

      <el-descriptions-item
          v-if="isTwoRound"
          label="第二轮开启图片"
      >
        {{ fmtBool(detailForm.isSecondRoundUseImage) }}
      </el-descriptions-item>

      <!-- 图像预处理参数 -->
      <el-descriptions-item label="图像增强">
        {{ fmtBool(detailForm.enableImageEnhancement) }}
      </el-descriptions-item>

      <!-- 时间（存在才显示） -->
      <el-descriptions-item v-if="detailForm.createTime" label="创建时间">
        {{ detailForm.createTime }}
      </el-descriptions-item>
      <el-descriptions-item v-if="detailForm.updateTime" label="更新时间">
        {{ detailForm.updateTime }}
      </el-descriptions-item>

      <!-- 其他参数 JSON -->
      <el-descriptions-item label="其他参数">
        <json-viewer :value="parseJson(detailForm.content)" expand-depth="2" />
      </el-descriptions-item>
    </el-descriptions>

    <template #footer>
      <el-button @click="detailDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';

export default {
  components: { JsonViewer },
  data() {
    return {
      dialogLoading: false,
      dialogForm: {},
      detailDialogVisible: false,
      detailForm: {}
    };
  },
  computed: {
    // 是否开启普通题目两轮询问
    isTwoRound() {
      return !!this.detailForm?.enableNormalQsTwoRequest;
    },
    // 是否开启二轮 JSON 比对
    isJsonComparison() {
      return !!this.detailForm?.isSecondRoundJsonComparison;
    }
  },
  methods: {
    parseJson(str) {
      try { return JSON.parse(str); } catch { return str || {}; }
    },
    getResponseFormat(row) {
      if (row?.jsonobject) return 'JsonObject';
      if (row?.jsonschema) return 'JsonSchema';
      return 'Text';
    },
    fmtBool(v) {
      if (v === true) return '开';
      if (v === false) return '关';
      return '-';
    },
    fmtPrompt(v) {
      return v || '-';
    },
    show(e) {
      this.detailDialogVisible = true;
      // 兼容外部字段命名（label/name、value/modelValue）
      this.detailForm = { ...e };
    },
    close() {
      this.detailDialogVisible = false;
      this.detailForm = {};
    }
  }
};
</script>
