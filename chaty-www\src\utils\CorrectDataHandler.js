export const sortRowsByStudentNumberThenStudentName = (rows) => {
    const invalidValues = ['', '无', null];
    return rows.sort((a, b) => {
        const aInvalid = invalidValues.includes(a[1]);
        const bInvalid = invalidValues.includes(b[1]);

        // 如果一项为无效而另一项为有效，则无效项排在后面
        if (aInvalid && !bInvalid) return 1;
        if (!aInvalid && bInvalid) return -1;

        // 对于两项均为有效的情况，按照学生编号降序排列
        if (!aInvalid && !bInvalid) {
            if (a[1] !== b[1]) {
                return a[1] - b[1];
            }
        }

        // 当学生编号相等或两项均为无效时，根据学生姓名升序排列
        if (a[2] < b[2]) return 1;
        if (a[2] > b[2]) return -1;
        return 0;
    });
};


/**
 * 试卷、试卷配置数据合并
 */
export const mergeRecordConfig = ({config, records}) => {
    const areas = JSON.parse(config.areas)
    return records.map(record => {
        const reviewed = record.reviewedObj
        const questions = []
        for (let areaIdx = 0; areaIdx < areas.length; areaIdx++) {
            let area = areas[areaIdx]
            let areaReviewed = reviewed[areaIdx]
            for (let qsIdx = 0; qsIdx < area.questions.length; qsIdx++) {
                let question = area.questions[qsIdx]
                let qsReviewed = areaReviewed?.reviewed?.[qsIdx]
                let isTrue = qsReviewed?.isCorrect === 'Y'
                let qsRes = Object.assign({
                    areaIdx,
                    qsIdx,
                }, question, qsReviewed)
                if (area.status !== '1') {
                    qsRes.review = area.error
                }
                if (area.areaType === 3 && question.review) {
                    qsRes.review = JSON.stringify(question.review)
                }
                if (question.isScorePoint === 2) {
                    qsRes.scored = qsReviewed?.scored
                    qsRes.scoreType = question.scoreType || qsRes.scoreType
                } else {
                    qsRes.scored = isTrue ? question.score : 0
                }
                questions.push(qsRes)
            }
        }
        return {
            record,
            questions
        }
    })
}

export const getScoreGrade = (scoreItem, gradeRange) => {
    for (let i = 0; i < gradeRange.length; i++) {
        if (scoreItem <= gradeRange[i].maxx && scoreItem >= gradeRange[i].minn) {
            return gradeRange[i].name;
        }
    }
    return '无';
}

export const getScoreTypeAndMaxScore = (tasks) => {
    if (!tasks || tasks.length === 0) {
        return null;
    }
    let recordSize = 0
    const taskRows = tasks.map(task => {
        recordSize = Math.max(recordSize, task.records.length)
        return mergeRecordConfig({config: task.config, records: task.records})
    })
    const rows = []
    const headers = []
    const headerMap = {}
    const scoreTypeMap = {}
    const recordScoreTypeMap = {}
    const scoreTypes = []
    const recordMap = []
    const total = {
        score: 0,
        scored: 0
    }
    let itemSize = 0
    let scoreTypeCount = {}
    for (let i = 0; i < taskRows.length; i++) {
        const task = taskRows[i]
        for (let j = 0; j < recordSize; j++) {
            const record = task[j]
            if (record) {
                let totalScore = 0
                const items = record.questions.map((qs, qsIdx) => {
                    let scoreType = qs.scoreType === '总分' ? '总分' : qs.scoreType
                    // 初始化分数类型和表头
                    if (j === 0) {
                        if (!scoreTypeCount[scoreType]) {
                            scoreTypes.push(scoreType)
                            scoreTypeMap[scoreType] = {
                                totalScored: 0,
                                totalScore: 0
                            }
                        }
                        scoreTypeCount[scoreType] = (scoreTypeCount[scoreType] || 0) + 1
                        scoreTypeMap[scoreType].totalScore = scoreTypeMap[scoreType].totalScore + qs.score
                        //
                        headers.push({
                            name: `${scoreType}${scoreTypeCount[scoreType]}`,
                            score: qs.score
                        })
                        total.score += qs.score
                    }
                    //
                    headerMap[itemSize + qsIdx] = (headerMap[itemSize + qsIdx] || 0) + qs.scored

                    if (qs.scoreType !== '总分') {
                        if (!recordScoreTypeMap[j]) {
                            recordScoreTypeMap[j] = {}
                        }
                        recordScoreTypeMap[j][scoreType] = (recordScoreTypeMap[j][scoreType] || 0) + qs.scored
                        scoreTypeMap[scoreType].totalScored = scoreTypeMap[scoreType].totalScored + qs.scored
                    }
                    total.scored += qs.scored
                    totalScore += qs.scored
                    return qs.scored
                })
                if (i === 0) {
                    recordMap[j] = {
                        identify: record.record.identify,
                        totalScore: totalScore
                    }
                    rows.push(items)
                } else {
                    recordMap[j].totalScore = recordMap[j].totalScore + totalScore
                    rows[j] = [...rows[j], ...items]
                }
            }
        }
    }

    // Prepare the result
    const headerScoreTypes = scoreTypes.map(scoreType => `${scoreType}`);
    const headerScoreTypesMaxScore = scoreTypes.map(scoreType => `${scoreTypeMap[scoreType].totalScore}`);

    return {
        scoreTypes: [...headerScoreTypes, '总分'],
        scoreTypesMaxScore: [...headerScoreTypesMaxScore, `${total.score}`]
    };
};


/**
 * 合并试卷
 */
export const createTasksExcelData = (tasks, isSortByStuNo = true) => {
    if (!tasks || tasks.length === 0) {
        return null
    }
    // 合并多个任务
    let recordSize = 0
    const taskRows = tasks.map(task => {
        recordSize = Math.max(recordSize, task.records.length)
        return mergeRecordConfig({config: task.config, records: task.records})
    })
    //
    let rows = []
    const headers = []
    const headerMap = {}
    const scoreTypeMap = {}
    const recordScoreTypeMap = {}
    const scoreTypes = []
    const recordMap = []
    const total = {
        score: 0,
        scored: 0
    }
    let itemSize = 0
    let scoreTypeCount = {}
    for (let i = 0; i < taskRows.length; i++) {
        const task = taskRows[i]
        for (let j = 0; j < recordSize; j++) {
            const record = task[j]
            if (record) {
                let totalScore = 0
                const items = record.questions.map((qs, qsIdx) => {
                    let scoreType = qs.scoreType === '总分' ? '总分' : qs.scoreType
                    if (scoreType === '4' || scoreType === 4) {
                        scoreType = getScoreTypeByQsIdx(tasks[i].config.areasObj, qsIdx);
                    }
                    // 初始化分数类型和表头
                    if (j === 0) {
                        if (!scoreTypeCount[scoreType]) {
                            scoreTypes.push(scoreType)
                            scoreTypeMap[scoreType] = {
                                totalScored: 0,
                                totalScore: 0
                            }
                        }
                        scoreTypeCount[scoreType] = (scoreTypeCount[scoreType] || 0) + 1
                        scoreTypeMap[scoreType].totalScore = scoreTypeMap[scoreType].totalScore + qs.score
                        //
                        headers.push({
                            name: `${scoreType}${scoreTypeCount[scoreType]}`,
                            score: qs.score
                        })
                        total.score += qs.score
                    }
                    //
                    headerMap[itemSize + qsIdx] = (headerMap[itemSize + qsIdx] || 0) + qs.scored

                    if (qs.scoreType !== '总分') {
                        if (!recordScoreTypeMap[j]) {
                            recordScoreTypeMap[j] = {}
                        }
                        recordScoreTypeMap[j][scoreType] = (recordScoreTypeMap[j][scoreType] || 0) + qs.scored
                        scoreTypeMap[scoreType].totalScored = scoreTypeMap[scoreType].totalScored + qs.scored
                    }
                    total.scored += qs.scored
                    totalScore += qs.scored
                    return qs.scored
                })
                if (i === 0) {
                    recordMap[j] = {
                        identify: record.record.identify,
                        totalScore: totalScore,
                        studentNumber: record.record.studentNumber,
                        docname: record.record.docname.split('_').pop()
                    }
                    rows.push(items)
                } else {
                    recordMap[j].totalScore = recordMap[j].totalScore + totalScore
                    rows[j] = [...rows[j], ...items]
                }
            }
        }
        itemSize += task[0].questions.length
    }

    for (let i = 0; i < recordSize; i++) {
        const scoreTypeItems = scoreTypes.map(scoreType => {
            if (scoreType === '总分') return undefined;
            return recordScoreTypeMap[i]?.[scoreType] || 0; // 如果不存在则返回 0
        }).filter(item => item !== undefined)
        rows[i] = [recordMap[i].docname.split('_').pop() ,recordMap[i].studentNumber, recordMap[i].identify, ...rows[i], ...scoreTypeItems, recordMap[i].totalScore]
    }

    const headerItems = headers.map((header, idx) => {
        return `${header.name}(平均分/满分 ${(headerMap[idx] / recordSize).toFixed(2)}/${header.score})`
    })
    const headerScoreTypes = scoreTypes.map(scoreType => {
        if (scoreType === '总分') return undefined;
        return `${scoreType}(平均分/满分 ${(scoreTypeMap[scoreType].totalScored / recordSize).toFixed(2)}/${scoreTypeMap[scoreType].totalScore})`
    }).filter(item => item !== undefined)

    return {
        headers: ['序号','学号','姓名', ...headerItems, ...headerScoreTypes, `总分(平均分/满分 ${(total.scored / recordSize).toFixed(2)}/${total.score})`],
        rows: isSortByStuNo ? sortRowsByStudentNumberThenStudentName(rows) : rows,
    }
}

export const getScoreTypeByQsIdx = (areas, qsIdx) => {
    let idx = 0;
    for (let i = 0;i<areas.length;i++) {
        for(let j=0;j<areas[i].questions.length;j++) {
            if (idx === qsIdx) {
                return areas[i].questions[j].scoreType;
            }
            idx++;
        }
    }
    return '';
}

export const createTasksExcelDataType2 = (tasks, gradeRanges, students, filterEnabled, needSort = true) => {
    if (!tasks || tasks.length === 0) {
        return null;
    }

    // 筛选task
    let paperSize = tasks.length;
    if (filterEnabled) {
        for (let i = 0; i < tasks[0].records.length; i++) {
            if (!(tasks[0].records[i].identify in students) && filterEnabled) {
                // 筛掉
                for (let j = 0; j < paperSize; j++) {
                    tasks[j].records.splice(i, 1);
                }
                i--;
            }
        }
    }

    // 合并多个任务
    let recordSize = 0;
    const taskRows = tasks.map(task => {
        recordSize = Math.max(recordSize, task.records.length);
        return mergeRecordConfig({config: task.config, records: task.records});
    });

    let rows = [];
    const headerMap = {};
    const scoreTypeMap = {};
    const recordScoreTypeMap = {};
    const scoreTypes = [];
    const recordMap = [];
    const total = {
        score: 0,
        scored: 0
    };
    let itemSize = 0;
    let scoreTypeCount = {};

    for (let i = 0; i < taskRows.length; i++) {
        const task = taskRows[i];
        for (let j = 0; j < recordSize; j++) {
            const record = task[j];
            if (record) {
                let totalScore = 0;
                const items = record.questions.map((qs, qsIdx) => {
                    let scoreType = qs.scoreType === '总分' ? '总分' : qs.scoreType;
                    if (scoreType === '4' || scoreType === 4) {
                        scoreType = getScoreTypeByQsIdx(tasks[i].config.areasObj, qsIdx);
                    }
                    // 初始化分数类型和表头
                    if (j === 0) {
                        if (!scoreTypeCount[scoreType]) {
                            scoreTypes.push(scoreType);
                            scoreTypeMap[scoreType] = {
                                totalScored: 0,
                                totalScore: 0
                            };
                        }
                        scoreTypeCount[scoreType] = (scoreTypeCount[scoreType] || 0) + 1;
                        scoreTypeMap[scoreType].totalScore = scoreTypeMap[scoreType].totalScore + Number(qs.score); // Ensure score is a number

                        total.score += Number(qs.score); // Ensure score is a number
                    }

                    headerMap[itemSize + qsIdx] = (headerMap[itemSize + qsIdx] || 0) + Number(qs.scored); // Ensure scored is a number

                    if (qs.scoreType !== '总分') {
                        if (!recordScoreTypeMap[j]) {
                            recordScoreTypeMap[j] = {};
                        }
                        recordScoreTypeMap[j][scoreType] = (recordScoreTypeMap[j][scoreType] || 0) + Number(qs.scored); // Ensure scored is a number
                        scoreTypeMap[scoreType].totalScored = scoreTypeMap[scoreType].totalScored + Number(qs.scored); // Ensure scored is a number
                    }

                    total.scored += Number(qs.scored); // Ensure scored is a number
                    totalScore += Number(qs.scored); // Ensure scored is a number
                    return Number(qs.scored); // Ensure scored is a number
                });

                if (i === 0) {
                    recordMap[j] = {
                        identify: record.record.identify,
                        totalScore: totalScore,
                        studentNumber: record.record.studentNumber,
                        docname: record.record.docname.split('_').pop()
                    };
                    rows.push(items);
                } else {
                    recordMap[j].totalScore = recordMap[j].totalScore + totalScore;
                    rows[j] = [...rows[j], ...items];
                }
            }
        }
        itemSize += task[0].questions.length;
    }

    let newRows = [];
    let gradeCount = {};
    for (let i = 0; i < recordSize; i++) {
        if (!(recordMap[i].identify in students) && filterEnabled) continue;

        const scoreTypeItems = scoreTypes.map(scoreType => {
            if (scoreType !== '总分')
                return Number(recordScoreTypeMap[i]?.[scoreType]) || 0;
            return undefined;
        }).filter(item => item !== undefined);

        // 只有当gradeRanges存在时才计算等级
        const scoreGradeTypeItems = gradeRanges ? scoreTypes.map(scoreType => {
            if (scoreType === '总分') return undefined;
            let score = Number(recordScoreTypeMap[i]?.[scoreType]) || 0;
            let grade = (gradeRanges && gradeRanges[scoreType]) ? getScoreGrade(score, gradeRanges[scoreType]) : '无';
            if (!(scoreType in gradeCount)) {
                gradeCount[scoreType] = {};
            }
            if (!(grade in gradeCount[scoreType])) {
                gradeCount[scoreType][grade] = 0;
            }
            gradeCount[scoreType][grade] += 1;
            return grade;
        }).filter(item => item !== undefined) : [];

        let totalGrade = (gradeRanges && gradeRanges['总分']) ? getScoreGrade(Number(recordMap[i].totalScore), gradeRanges['总分']) : '无';
        if (gradeRanges && gradeRanges['总分']) {
            if (!('总分' in gradeCount)) {
                gradeCount['总分'] = {};
            }
            if (!(totalGrade in gradeCount['总分'])) {
                gradeCount['总分'][totalGrade] = 0;
            }
            gradeCount['总分'][totalGrade] += 1;
        }
        if (recordMap[i].identify in students) {
            newRows.push([recordMap[i].docname, Number(students[recordMap[i].identify]) || recordMap[i].studentNumber, recordMap[i].identify, ...scoreTypeItems, recordMap[i].totalScore, ...scoreGradeTypeItems, totalGrade]);
        } else if (!filterEnabled) {
            newRows.push([recordMap[i].docname, recordMap[i].studentNumber, recordMap[i].identify, ...scoreTypeItems, recordMap[i].totalScore, ...scoreGradeTypeItems, totalGrade]);
        }
    }
    if (needSort) {
        rows = sortRowsByStudentNumberThenStudentName(newRows)
    } else {
        rows = newRows;
    }

    // 平均分的一行
    let rowAverageScoreItems = scoreTypes.map(scoreType => {
        if (scoreType === '总分') return undefined;
        return ` ${(Number(scoreTypeMap[scoreType].totalScored) / recordSize).toFixed(2)}`; // Ensure totalScored is a number
    }).filter(item => item !== undefined);

    let averageTotalScore = (Number(total.scored) / recordSize).toFixed(2);
    let gradeTypes = getGradeTypes(gradeCount);
    for (let i=0;i<gradeTypes.length;i++) {
        let rowAverage = [];
        if (i === 0) {
            rowAverage = ['平均分', '','', ...rowAverageScoreItems, averageTotalScore];
        }else {
            let size = 4 + rowAverageScoreItems.length;
            for (let j = 0; j < size; j++) {
                rowAverage.push('');
            }
        }
        for (let j = 0; j < scoreTypes.length; j++) {
            if (scoreTypes[j] !== '总分') {
                rowAverage.push(`${gradeTypes[i]} ${gradeCount[scoreTypes[j]][gradeTypes[i]] || 0}`);
            }
        }
        rowAverage.push(`${gradeTypes[i]} ${gradeCount['总分'][gradeTypes[i]] || 0}`);
        rows.push(rowAverage);
    }


    const headerScoreTypes = scoreTypes.map(scoreType => {
        if (scoreType !== '总分')
            return `${scoreType}`;
        return undefined
    }).filter(item => item !== undefined);

    // 只有当gradeRanges存在时才添加等级表头
    const headerScoreGradeTypes = gradeRanges ? scoreTypes.map(scoreType => {
        if (scoreType === '总分') return undefined;
        return `${scoreType}等级`;
    }).filter(item => item !== undefined) : [];

    const result = {
        headers: ['序号','学号', '姓名', ...headerScoreTypes, `总分`, ...headerScoreGradeTypes, gradeRanges ? '总分等级' : ''],
        rows,
    };
    // 如果没有gradeRanges，去掉等级相关的表头和数据列
    if (!gradeRanges) {
        // 去掉 headers 里所有包含 "等级" 的列
        result.headers = result.headers.filter(h => h && !h.includes('等级'));
        // 去掉 rows 里对应的列
        const gradeColStart = result.headers.length; // 等级列本应在最后
        result.rows = result.rows.map(row => row.slice(0, gradeColStart));
    }
    return result;
};

export const getGradeCountItems = (gradeCount) => {
    let gradeCountItems = [];
    for (let scoreType in gradeCount) {
        let gradeCountItem = '';
        for (let grade in gradeCount[scoreType]) {
            gradeCountItem += `${grade}${gradeCount[scoreType][grade]} `;
        }
        gradeCountItems.push(gradeCountItem);
    }
    return gradeCountItems;
}

export const getGradeTypes = (gradeCount) => {
    let gradeCountItems = [];
    for (let scoreType in gradeCount) {
        for (let grade in gradeCount[scoreType]) {
            if (gradeCountItems.indexOf(grade) === -1) {
                gradeCountItems.push(grade);
            }
        }
    }
    // 排序
    gradeCountItems.sort((a, b) => {
        return a.localeCompare(b);
    });
    return gradeCountItems;
}


/**
 * 个性化3
 * @param rows
 * @param needSort
 * @param ranges 分数段数据
 * @returns {{headers: string[], rows: *[]}|null}
 */
export const createTasksExcelDataType3 = (rows, needSort, ranges) => {
    // 转换数字类型
    for (let i = 0;i<rows.length;i++) {
        for(let j=0;j<rows[i].length;j++) {
            if (!isNaN(Number(rows[i][j]))) {
                rows[i][j] = Number(rows[i][j]);
            }
        }
    }

    // 如果有分数段数据，添加等级列
    if (ranges) {
        const headers = rows[0];
        const newRows = [];
        
        // 处理每一行数据
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const newRow = [...row];
            
            // 为每个分数类型添加等级
            for (let j = 0; j < headers.length; j++) {
                const header = headers[j];
                const score = row[j];
                if (typeof score === 'number') {
                    // 从表头中提取分数类型名称
                    const scoreType = header.split('(')[0].trim();
                    // 如果该分数类型在ranges中有定义
                    if (ranges[scoreType]) {
                        const grade = getScoreGrade(score, ranges[scoreType]);
                        newRow.push(grade);
                    }
                }
            }
            newRows.push(newRow);
        }

        // 更新表头
        const newHeaders = [...headers];
        for (let j = 0; j < headers.length; j++) {
            const header = headers[j];
            const scoreType = header.split('(')[0].trim();
            if (ranges[scoreType]) {
                newHeaders.push(`${scoreType}等级`);
            }
        }

        return {
            headers: newHeaders,
            rows: needSort ? sortRowsByStudentNumberThenStudentName(newRows) : newRows
        };
    }

    return {
        headers: rows[0],
        rows: needSort ? sortRowsByStudentNumberThenStudentName(rows.slice(1)) : rows.slice(1)
    };
}