<template>
  <el-card shadow="hover" style="margin-bottom: 20px;">
    <template #header>
      <span>等级设置</span>
    </template>
    <el-form-item label="依次选择分数类型" label-width="130px" prop="type">
      <div style="display: flex;width: 100%;">
        <el-radio-group v-model="type">
          <el-radio v-for="(type, index) in scoreTypes" :key="index" :label="type">{{ type }}
            <el-input v-model="scoreTypesMaxScore[index]" style="width: 50px;height: 20px" size="small"></el-input>分</el-radio>
        </el-radio-group>
        <el-button style="margin-left: 10px" @click="reset">重置所有调整</el-button>
      </div>
      <div>* 分数是整套试卷合并的分数（对于常见的正反面，则会对正反面分数进行合并）</div>
    </el-form-item>
    <div v-if="type in ranges" v-for="(range, index) in ranges[type]" :key="index">
      <div style="display: flex;width: 100%;align-items: center">
        <el-form-item label="等级名称" :prop="'ranges.' + index + '.name'">
          <div style="display: flex;justify-content: space-between;width: 150px">
            <el-input v-model="range.name" placeholder="请输入等级名称" style="width: 100px"/>
            <el-button v-if="ranges[type].length > 1" style="margin-left: 10px" type="danger" @click="removeRange(index)">删除</el-button>
          </div>
        </el-form-item>

        <el-form-item label="分数范围" style="margin-left: -50px">
          <el-input-number v-model="range.maxx" :min="range.minn" :max="1000" label="最大分数" style="width: 130px"/>
          <el-text style="padding: 0 5px">>= 学生分数 >=</el-text>
          <el-input-number v-model="range.minn" :min="0" :max="1000" label="最小分数" style="width: 130px"/>
        </el-form-item>
      </div>

      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="滑动调整">
            <el-slider
                v-model="range.sliderRange"
                style="width: 500px"
                range
                show-stops
                :max="100"
                :format-tooltip="tooltipFormatter"
                @input="changeRangeValue(range, type, index)"
            >
            </el-slider>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider></el-divider>

    </div>

    <div style="display: flex;flex-direction: row-reverse">
      <el-button type="primary" @click="addRange">添加一项等级</el-button>
    </div>
  </el-card>
</template>

<script>
export default {
  props: {
    id: {
      type: String,
      required: true
    },
    ranges: {
      type: Object,
      required: true
    },
    packageId: {
      type: String,
      required: false
    }
  },
  data() {
    return {
      scoreTypes: [],
      scoreTypesMaxScore: [],
      type: '',
    }
  },
  mounted() {
    this.show();
  },
  methods: {
    reset(e) {
      this.$confirm("如有修改，请保存后重置，否则数据会是保存之前的", "提示", {
        confirmButtonText: "继续",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        this.$axios.get(`/api/docCorrectConfigPackage/getAllScoreTypeScore?id=${this.packageId || this.id}`).then(res => {
          this.scoreTypes = res.data.scoreTypes;
          this.scoreTypesMaxScore = res.data.scoreTypesMaxScore;
          this.scoreTypes.forEach((it, idx) => {
            this.ranges[it] = this.initRange(this.scoreTypesMaxScore[idx]);
          })
          this.type = this.scoreTypes[0];
        })
      })

    },
    show(e) {
      if (e) {
        this.ranges = e
      }

      this.$axios.get(`/api/docCorrectConfigPackage/getAllScoreTypeScore?id=${this.packageId || this.id}`).then(res => {
        this.scoreTypes = res.data.scoreTypes;
        this.scoreTypesMaxScore = res.data.scoreTypesMaxScore;

        if (!this.ranges || !Object.keys(this.ranges).length) {
          this.scoreTypes.forEach((it, idx) => {
            this.ranges[it] = this.initRange(this.scoreTypesMaxScore[idx]);
          })
        }

        this.type = this.scoreTypes[0];
      })
    },
    addRange() {
      this.ranges[this.type].push({maxx: 100, minn: 0, name: '无'});
    },
    initRange(maxScore) {
      // 1. 先计算临界分数（数值）
      const A_min = Number((maxScore * 0.85).toFixed(1));
      const B_min = Number((maxScore * 0.60).toFixed(1));
      // 2. 返回连续、不重叠的分段
      return [
        {
          name: 'A',
          minn: A_min,       // [A_min, maxScore]
          maxx: maxScore,
          sliderRange: [0, 15]
        },
        {
          name: 'B',
          minn: B_min,       // [B_min, A_min]
          maxx: Number(A_min - 0.1).toFixed(1),
          sliderRange: [15, 40]
        },
        {
          name: 'C',
          minn: 0,           // [0, B_min]
          maxx: Number(B_min - 0.1).toFixed(1),
          sliderRange: [40, 100]
        }
      ];
    },
    tooltipFormatter(value) {
      return `${value}%`;
    },
    changeRangeValue(range, type, index) {
      let maxScore = parseFloat(
          this.scoreTypesMaxScore[this.scoreTypes.findIndex((it) => it === type)]
      );

      let value = range.sliderRange;
      range.maxx = parseFloat((maxScore - maxScore * value[0] / 100).toFixed(1));
      range.minn = parseFloat((maxScore - maxScore * value[1] / 100).toFixed(1));

      if (index + 1 < this.ranges[type].length) {
        let nextRange = this.ranges[type][index + 1];
        let maxx = nextRange.maxx;

        if (maxx > range.minn) {
          maxx = Math.floor(range.minn);
        } else if (maxx === range.minn) {
          maxx = maxx - 0.5;
        }

        nextRange.maxx = maxx;
        nextRange.sliderRange[0] = ((1 - maxx / maxScore) * 100.0).toFixed(1);
      }
    },
    removeRange(index) {
      this.$emit('remove-range', index);
    }
  }
};
</script>
