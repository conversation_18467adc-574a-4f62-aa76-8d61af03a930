<template>
    <div class="record-container">
        <div class="header-bar">
            <div class="action-bar-label">{{ `${$t('docReviewRec.docName')}：` }}</div>
            <el-input class="action-bar-item" v-model="filter.filename"></el-input>
            <div class="action-bar-label">{{ `${$t('docReview.studentName')}：` }}</div>
            <el-input class="action-bar-item" v-model="filter.identify"></el-input>
            <div class="action-bar-label">{{ `${$t('docReview.docName')}：` }}</div>
            <el-input class="action-bar-item" v-model="filter.docname"></el-input>
            <div class="action-bar-label">{{ `${$t('common.creationTime')}：` }}</div>
            <el-input class="action-bar-item" v-model="filter.creationTime"></el-input>
            <!-- <el-date-picker
                class="action-bar-item"
                style="flex: 0 0 360px"
                v-model="filter.creationTime"
                type="datetimerange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                /> -->
            <el-button class="action-bar-search" type="primary" @click="loadData">{{ $t('common.search') }}</el-button>
        </div>
        <div  class="container-table">
            <el-table :data="tableData" height="100%" :border="true" :empty-text="$t('common.emptyData')">
                <el-table-column type="expand">
                    <template #default="scope">
                        <el-table size="small" :border="true" :data="scope.row.reviewedObj">
                            <el-table-column :label="$t('common.question')" prop="name" width="100px"></el-table-column>
                            <el-table-column :label="$t('common.problem')" prop="question"></el-table-column>
                            <el-table-column :label="$t('common.correctAnswer')" prop="correctAnswer"></el-table-column>
                            <el-table-column :label="$t('common.isTrue')" prop="isTrue">
                                <template v-slot="prop">
                                    <el-tag>{{ prop.row.isTrue }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('common.evaluation')" prop="review"></el-table-column>
                            <el-table-column :label="$t('common.tag')" prop="tag" width="100px"></el-table-column>
                            <el-table-column :label="$t('common.operations')" prop="operation" width="150px">
                                <template v-slot="prop">
                                    <el-button type="primary" text size="small" @click="editQuestion(scope.row, prop.$index)">{{ $t('common.edit') }}</el-button>
                                    <el-button type="primary" text size="small" @click="deleteQuestion(scope.row, prop.$index)">{{ $t('common.delete') }}</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('menu.docReview')" prop="filename">
                    <template v-slot="scope">
                        <el-link type="primary" :underline="false" :href="$fileserver.fileurl(scope.row.fileurl)" target="_blank">
                            {{  scope.row.filename  }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('docReview.studentName')" prop="identify"></el-table-column>
                <el-table-column :label="$t('docReviewRec.docName')" prop="docname"></el-table-column>
                <el-table-column :label="$t('common.creationTime')" prop="createTime"></el-table-column>
                <el-table-column :label="$t('common.operations')" prop="opeeations" align="center">
                    <template v-slot="scope">
                        <el-button size="small" text @click="doDelete(scope.row.id)">{{ $t('common.delete') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="container-bottom">
            <el-pagination
                layout="prev, pager, next"
                :total="totalCount"
                @change="onPageChange"
            />
        </div>
        
        <!-- 题目编辑表单 -->
        <qs-form ref="qsForm" @onClose="(isSubmit) => isSubmit && loadData()"></qs-form>
    </div>
</template>

<script>
import QsForm from "./components/qs-form.vue"

export default {
    components: {
        QsForm
    },
    data() {
        return {
            filter: {
                docname: "",
                filename: "",
                identify: "",
                creationTime: "",
            },
            tableData: [],
            pageNumber: 1,
            pageSize: 10,
            totalCount: 0,
        }
    },
    created() {
        this.loadData()
    },
    methods: {
        loadData() {
            let params = {
                docname: this.filter.docname,
                filename: this.filter.filename,
                identify: this.filter.identify,
                datePrefix: this.filter.creationTime,
                pageSize: this.pageSize,
                pageNumber: this.pageNumber,
            }
/*             if (this.filter.creationTime && this.filter.creationTime.length === 2) {
                params.startTime = this.filter.creationTime[0]
                params.endTime = this.filter.creationTime[1]
            } */
            this.$axios.post("/api/docreviewrec/page", params).then(res => {
                this.tableData = res.data.records.map(item => {
                    let questions = item.questions ? JSON.parse(item.questions) : []
                    let reviewed = item.reviewed ? JSON.parse(item.reviewed) : []
                    item.reviewedObj = reviewed.filter(r => !r.deleted)
                        .map((review, index) => {
                            let q = questions[index] || {}
                            return {
                                name: review.name || q.name,
                                question: review.question || q.question,
                                correctAnswer: review.correctAnswer || q.correctAnswer,
                                isTrue: !review.status || review.review?.isTrue,
                                review: review.review?.review,
                                tag: review.tag
                            }
                        })
                    return item
                })
                this.totalCount = res.data.total
            })
        },
        onPageChange(currentPage, pageSize) {
            this.pageNumber = currentPage
            this.pageSize = pageSize
            this.loadData()
        },
        doDelete(id) {
            this.$axios.post(`/api/docreviewrec/delete/${id}`).then(res => {
                this.$message.success(this.$t('common.deleteSuccess'))
                this.loadData()
            })
        },
        editQuestion(row, qsIdx) {
            this.$refs.qsForm.show({
                row,
                qsIdx,
            })
        },
        deleteQuestion(row, qsIdx) {
            this.$confirm(this.$t('common.confirmDelete', [this.$t('common.question')]), {
                confirmButtonText: this.$t('common.confirm'),
                cancelButtonText: this.$t('common.cancel')
            }).then(res => {
                let reviewed = JSON.parse(row.reviewed)
                reviewed[qsIdx].deleted = true
                let formData = {
                    id: row.docReviewId,
                    reviewed: JSON.stringify(reviewed)
                }
                this.$axios.post("/api/docreview/update", formData).then(res => {
                    this.$message.success(this.$t('common.deleteSuccess'))
                    this.loadData()
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.record-container {
    height: 100%;
    
    .header-bar {
        height: 60px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        row-gap: 10px;

        .action-bar-label {
            margin-right: 10px;
        }

        .action-bar-item {
            margin-right: 30px;
            width: 230px;
        }

        .action-bar-search {
            margin-left: auto;
            margin-right: 50px;
        }
    }

    .container-table {
        height: calc(100% - 120px);
        margin: 20px 0;
    }

    .container-bottom {
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>