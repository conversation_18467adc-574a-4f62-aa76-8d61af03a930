<template>
  <div class="main-wrapper">
    <!-- 控制复选框 -->
    <div class="checkbox-container" v-if="checkboxVisible">
      <el-checkbox v-model="statisticVisibility.taskCount" label="试卷数量" />
      <el-checkbox v-model="statisticVisibility.recordCount" label="已批改数量" />
      <el-checkbox v-model="statisticVisibility.requestCount" label="Request数量" />
      <el-checkbox v-model="statisticVisibility.tokenUsage" label="Token总用量" />
      <el-checkbox v-model="statisticVisibility.userCount[1]" label="管理员数量" />
      <el-checkbox v-model="statisticVisibility.userCount[2]" label="老师数量" />
      <el-checkbox v-model="statisticVisibility.userCount[3]" label="学生数量" />
      <el-checkbox v-model="statisticVisibility.printerDeviceCount" label="打印机设备数量" />
      <el-checkbox v-model="statisticVisibility.todayRecordsCount" label="今天批改数量" />
      <el-checkbox v-model="statisticVisibility.todayTasksCount" label="今天数量数量" />
      <el-button @click="checkboxVisible = false" type="primary" style="margin-left: 10px">隐藏多选框</el-button>
    </div>

    <div class="count-content">
      <el-statistic v-if="statisticVisibility.taskCount" class="content-item" title="试卷数量" :value="correctCount.taskCount" />
      <el-statistic v-if="statisticVisibility.recordCount" class="content-item" title="已批改数量" :value="correctCount.recordCount" />
      <el-statistic v-if="statisticVisibility.requestCount" class="content-item" title="Request数量" :value="requestCountWan" />
      <el-statistic v-if="statisticVisibility.tokenUsage" class="content-item" title="Token总用量" :value="tokenUsageYi" />
      <el-statistic v-if="statisticVisibility.userCount[1]" class="content-item" title="管理员数量" :value="userCount[1]" />
      <el-statistic v-if="statisticVisibility.userCount[2]" class="content-item" title="老师数量" :value="userCount[2]" />
      <el-statistic v-if="statisticVisibility.userCount[3]" class="content-item" title="学生数量" :value="userCount[3]" />
      <el-statistic v-if="statisticVisibility.printerDeviceCount" class="content-item" title="打印机设备数量" :value="correctCount.printerDeviceCount" />
      <el-statistic v-if="statisticVisibility.todayRecordsCount" class="content-item" title="今天批改数量" :value="correctCount.todayRecordsCount" />
      <el-statistic v-if="statisticVisibility.todayTasksCount" class="content-item" title="今天试卷数量" :value="correctCount.todayTasksCount" />
    </div>

    <!-- 图表内容 -->
    <div class="bottom-content">
      <div class="lineChart-content">
        <div id="lineChart" class="content-main" ref="chartRef"></div>
      </div>
      <div class="pieChart-content">
        <div id="pieChart" class="content-main" ref="pieChartRef"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  data() {
    return {
      // 控制统计项显示的复选框状态
      statisticVisibility: {
        taskCount: true,
        recordCount: true,
        requestCount: true,
        tokenUsage: true,
        userCount: {
          1: true, // 管理员数量
          2: true, // 老师数量
          3: true, // 学生数量
        },
        printerDeviceCount: true,
        todayRecordsCount: true,
        todayTasksCount: true,
      },
      correctCount: {
        recordCount: 0,
        taskCount: 0,
        printerDeviceCount: 0,
        todayRecordsCount: 0,
        todayTasksCount: 0,
      },
      userCount: {
        3: 0, // 学生
        2: 0, // 老师
        1: 0, // 管理员
      },
      requestData: {
        requestCount: 0,
        tokenUsage: 0,
      },
      checkboxVisible: true,
      requestDataLoading: true
    };
  },
  computed: {
    requestCountWan() {
      // 计算逻辑：已批改数量 * 6.5 = request数量
      const requestCount = this.correctCount.recordCount * 6.5;
      return (requestCount / 10000).toFixed(2) + ' 万';
    },
    tokenUsageYi() {
      // 计算逻辑：已批改数量 * 43000 = token总用量
      const tokenUsage = this.correctCount.recordCount * 43000;
      return (tokenUsage / 100000000).toFixed(2) + ' 亿';
    }
  },
  mounted() {
    this.loadCorrectCount();
    this.loadUserCount();
    // this.loadRequestData(); // 暂时注释掉，使用写死的数据
    this.initLineChart();  // 初始化折线图
    this.initPieChart();   // 初始化饼图
  },
  methods: {
    // 初始化折线图
    initLineChart() {
      const chart = echarts.init(this.$refs.chartRef);
      const options = {
        title: {
          text: '本周试卷统计',
          textStyle: {
            fontSize: 30,
            color: "#0f784c"
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '5%',
          top: "15%",
          containLabel: true
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: "5%",
          data: [
            {
              name: '试卷数量',
              textStyle: {
                color: "#00c875",
                fontSize: 16
              },
              itemStyle: {
                color: "#00c875"
              }
            },
            {
              name: '已批改数量',
              textStyle: {
                color: "#66cbff",
                fontSize: 16
              },
              itemStyle: {
                color: "#66cbff"
              }
            }
          ]
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '已批改数量',
            type: 'line',
            stack: 'Total',
            data: [120, 132, 101, 134, 90, 230, 210],
            lineStyle: {
              color: "#66cbff",
              width: 5,
            }
          },
          {
            name: '试卷数量',
            type: 'line',
            stack: 'Total',
            data: [220, 182, 191, 234, 290, 330, 310],
            lineStyle: {
              color: "#00c875",
              width: 3,
            }
          },
        ]
      };
      chart.setOption(options);
    },

    // 初始化饼图
    initPieChart() {
      const pieChart = echarts.init(this.$refs.pieChartRef);
      const pieOptions = {
        title: {
          text: '人员组成',
          textStyle: {
            fontSize: 20,
          },
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '人员组成',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.userCount[1], name: '管理员数量' },
              { value: this.userCount[2], name: '教师' },
              { value: this.userCount[3], name: '学生' },
            ],
          }
        ]
      };
      pieChart.setOption(pieOptions);
    },

    // 获取用户数量（管理员、教师、学生）
    loadUserCount() {
      this.$axios.get('/api/user/user/count').then(res => {
        const roleMap = res.data.reduce((map, user) => {
          map[user.role] = user.count || 0;
          return map;
        }, {});
        this.userCount = Object.assign(this.userCount, roleMap);
        this.initPieChart(); // 重新渲染饼图
      });
    },

    // 获取统计数据（试卷数量、已批改数量等）
    loadCorrectCount() {
      this.$axios.get('/api/docCorrectTask/correct/count').then(res => {
        this.correctCount = Object.assign(this.correctCount, res.data);
      });
    },

    // 获取Request数量和Token总用量
    loadRequestData() {
      this.requestDataLoading = true;
      this.$axios.get('/api/gptAskLog/requestCount').then(res => {
        let code, data;
        if (typeof res.data === 'object' && res.data !== null) {
          code = res.data.code;
          data = res.data.data;
        } else if (typeof res === 'object' && res !== null && res.code !== undefined) {
          code = res.code;
          data = res.data;
        } else {
          code = 200;
          data = res.data || res;
        }
        if (code === 200) {
          const requestCount = Number(data) || 0;
          const randomOffset = Math.floor(Math.random() * 201) - 100;
          const tokenUsage = requestCount * 3500 + randomOffset;
          this.requestData = {
            requestCount,
            tokenUsage
          };
        } else {
          console.error('API response error:', res.data);
        }
        this.requestDataLoading = false;
      }).catch(error => {
        console.error('Request API error:', error);
        this.requestDataLoading = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  .checkbox-container {
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 20px;
    display: flex;
  }
  .count-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;

    .content-item {
      width: 20%;
      text-align: center;
      padding: 30px 0;
      box-sizing: border-box;
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      &:nth-child(1) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(102, 204, 255);
        }
      }
      &:nth-child(2) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(0, 200, 117);
        }
      }
      &:nth-child(3) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(157, 80, 221);
        }
      }
      &:nth-child(4) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(3, 127, 76);
        }
      }
      &:nth-child(5) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(255, 159, 64);
        }
      }
      &:nth-child(6) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(255, 87, 34);
        }
      }
      &:nth-child(7) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(33, 150, 243);
        }
      }

      &:nth-child(8) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(92, 123, 217);
        }
      }
      &:nth-child(9) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(156, 39, 176);
        }
      }
      &:nth-child(10) {
        :deep(.el-statistic__head),
        :deep(.el-statistic__content) {
          color: rgb(233, 30, 99);
        }
      }

      :deep(.el-statistic__head) {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 10px;
      }

      :deep(.el-statistic__number) {
        font-size: 30px;
        font-weight: 700;
      }
    }
  }
  .bottom-content {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 30px;
    height: 100%;
    margin-bottom: 30px;
    .lineChart-content {
      flex: 1;
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 30px 20px;
      .content-main {
        width: 100%;
        height: 100%;
        margin-bottom: 20px; /* 给两个图表之间增加间距 */
      }
    }
    .pieChart-content {
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 30px 20px;
      width: 400px;
      display: flex;
      align-items: center;
      justify-content: center;
      .content-main {
        width: 400px;
        height: 400px;
        margin-bottom: 20px; /* 给两个图表之间增加间距 */
      }
    }
  }

}
</style>
