<template>
    <el-dialog title="试卷库" v-model="isShow" width="800px" center :before-close="onClose">
        <div class="dialog-header">
            <el-input v-model="filter.name" placeholder="请输入试卷名称" clearable
                class="filter-item">
            </el-input>
            <el-select v-model="filter.docType" placeholder="请选择试卷类型" clearable 
                class="filter-item">
                <el-option v-for="item in docTypes" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button class="right-action" type="primary" @click="loadData">查询</el-button>
        </div>
        <el-table v-loading="configLoading" class="dialig-main" :data="configs" height="500px" empty-text="无数据">
            <el-table-column v-for="column in columns" :key="column.prop" v-bind="column">
                <template v-if="column.prop === 'operations'" v-slot="scope">
                    <el-button type="primary" text size="small" @click="onSelect(scope.row)">选择</el-button>
                    <el-button type="primary" text size="small" @click="onDelete(scope.row.id)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="dialog-footer">
            <el-pagination background layout="prev, pager, next" 
                v-model:page-size="pageSize" 
                v-model:current-page="pageNumber"
                :total="total"
                @current-change="loadData"
                />
        </div>
    </el-dialog>
</template>
<script>
export default {
    data() {
        return {
            isShow: false,
            filter: {
                name: "",
                docType: "",
            },
            configs: [],
            columns: [
                { prop: "name", label: "名称", align: "center" },
                { prop: "docType", label: "试卷类型", align: "center" },
                { prop: "operations", label: "操作", align: "center" },
            ],
            pageSize: 10,
            pageNumber: 1,
            total: 0,
            docTypes: [
                { label: "A4", value: "A4" },
                { label: "A3", value: "A3" },
            ],
            configLoading: false,
        }
    },
    methods: {
        loadData() {
            this.configLoading = true

            this.$axios.post("/api/docCorrectConfig/page", {
                name: this.filter.name,
                docType: this.filter.docType,
                page: {
                    pageNumber: this.pageNumber,
                    pageSize: this.pageSize
                }
            }).then(res => {
                this.configs = res.data.records;
                this.total = res.data.total
            }).finally(() => {
                this.configLoading = false
            })
        },
        show() {
            this.isShow = true
            this.loadData()
        },
        onClose() {
            this.isShow = false
        },
        onSelect(config) {
            this.$emit("onSelect", config)
            this.onClose()
        },
        onDelete(id) {
            this.$confirm("确认删除该试卷吗？", "", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$axios.post(`/api/docCorrectConfig/delete?id=${id}`).then(res => {
                    this.$message.success("删除成功");
                    this.loadData();
                });
            });
        
        }
    }
}
</script>
<style lang="scss" scoped>
.dialog-header {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .filter-item {
        margin-right: 20px;
        width: 200px;
    }

    .right-action {
        margin-left: auto;
    }
}

.dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}
</style>