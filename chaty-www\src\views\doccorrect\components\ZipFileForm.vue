<template>
    <el-dialog v-model="isShow" :title="title" width="800" :before-close="onClose">
        <el-form :model="formData" label-position="top">
            <el-form-item label="分数合并到第一页" prop="scoreMerge">
                <el-switch v-model="formData.scoreMerge" />
            </el-form-item>
            <el-form-item label="显示扣分" prop="showQsScore">
                <el-switch v-model="formData.showQsScore" />
            </el-form-item>
            <el-form-item label="交换顺序(不含原卷)" prop="isReversed">
                <el-switch v-model="formData.isReversed" />
            </el-form-item>
            <el-form-item label="保存到远程文件夹" prop="save2Remote">
                <el-switch v-model="formData.save2Remote" />
            </el-form-item>
            <el-form-item label="远程打印" prop="printer">
                <el-switch v-model="formData.printerProps.print" active-text="是否远程打印" @change="openPrinterSetting">
                </el-switch>
                <span style="margin-left: 10px">{{printerSettingStr}}</span>
            </el-form-item>
            <el-divider />
            <el-form-item label="字体大小" prop="fontSize">
                <el-input-number v-model="formData.fontSize" :controls="false"></el-input-number>
            </el-form-item>
            <el-form-item label="分数段人数统计" prop="segedScore">
                <el-input-number v-model="formData.segedScore" :controls="false"></el-input-number>
            </el-form-item>
            <el-divider />
            <el-form-item label="合并试卷" prop="tasks">
                <el-select style="margin-bottom: 10px" suffix-icon="Search" placeholder="请输入试卷名称" remote filterable
                    :remote-method="loadTaskOptions" :loading="taskLoading" @change="onTaskSelect"
                    :model-value="taskName">
                    <el-option v-for="item in taskOptions" :key="item.id" :label="item.name" :value="item"
                        :disabled="!!formData.tasks.find(t => t.id === item.id)" />
                </el-select>
                <el-table :data="formData.tasks" border>
                    <el-table-column prop="name" label="名称" align="center" />
                    <el-table-column prop="configName" label="试卷配置" align="center" />
                    <el-table-column prop="operations" label="操作" align="center">
                        <template #default="{ row }">
                            <el-button icon="Delete"
                                @click="formData.tasks.splice(formData.tasks.indexOf(row), 1)" />
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" @click="onClose">取消</el-button>
            <el-button type="primary" @click="onSubmit" :loading="submiting">确认</el-button>
        </template>
        <printer-setting ref="printerSettingRef" @printerSettingsSubmit="printerSettingsSubmit"></printer-setting>
    </el-dialog>
</template>
<script>
import PrinterSetting from "@/views/common/PrinterSetting.vue";

export default {
  components: {PrinterSetting},
    props: {
        submiting: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            title: "打包下载批改结果",
            isShow: false,
            formData: {
                scoreMerge: true,
                showQsScore: true,
                fontSize: 20,
                segedScore: null,
                isReversed: true,
                tasks: [],
                save2Remote: false,
                printerProps: {
                  print: false
                }
            },
            taskLoading: false,
            taskOptions: [],
            taskName: '',
            printerSettingStr: ''
        }
    },
    watch: {
        submiting(val) {
            if (!val) {
                this.onClose()
            }
        }
    },
    methods: {
        printerSettingsSubmit(val, printerSettingStr) {
          this.formData.printerProps = val;
          this.printerSettingStr = printerSettingStr;
        },
        openPrinterSetting() {
          if (this.formData.printerProps.print) {
            this.$refs.printerSettingRef.show();
          }
        },
        show() {
            this.isShow = true
        },
        onClose() {
            this.formData = {
                scoreMerge: true,
                showQsScore: true,
                fontSize: 20,
                segedScore: null,
                isReversed: true,
                tasks: [],
                save2Remote: false,
                printer: null,
            }
            this.taskName = ''
            this.isShow = false
        },
        loadTaskOptions(query) {
            if (!query || query === '') {
                return
            }
            this.taskName = query
            this.taskLoading = true
            let form = {
                name: query,
                status: 3,
                page: {
                    size: -1,
                    searchCount: false,
                }
            }
            this.$axios.post(`/api/docCorrectTask/page`, form).then(res => {
                this.taskOptions = res.data.records
            }).finally(() => {
                this.taskLoading = false
            })
        },
        onTaskSelect(val) {
            this.formData.tasks.push(val)
        },
        onSubmit() {
            if (this.formData.tasks.length === 0) {
                this.$message.error('请添加试卷')
                return
            }
            this.$emit('submit', this.formData)
            this.onClose()
        }
    }
}
</script>
<style lang="scss" scoped></style>