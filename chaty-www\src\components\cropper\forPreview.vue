<template>
  <div class="image-preview-wrapper" ref="imageWrapper">
    <canvas
        ref="canvas"
        class="preview-canvas"
        @mousedown="onMouseDown"
        @wheel="onWheelZoom"
        @contextmenu.prevent="onRightClick"
        @mousedown.right="onRightCropMouseDown"
        @auxclick.middle.prevent="jumpNextPage()"
    ></canvas>
    <div
        v-if="!isEmpty && (!imgUrl || loading)"
        class="empty-tip"
        :class="{ loading: loading }"
    >
      <span class="loading-text">加载中...</span>
    </div>
    <div v-else-if="isEmpty"></div>

    <el-text class="name-number-text" v-if="!isConfig && ( identify || studentNumber)">
      {{ identify ? identify.slice(0, 5) + (identify.length > 5 ? '...' : '') : '' }} -
      {{ studentNumber ? studentNumber.slice(0, 12) + (studentNumber.length > 12 ? '...' : '') : '' }}
    </el-text>

    <!-- 全屏时显示的按钮容器 -->
    <div v-if="isFullScreen" class="fullscreen-controls">
      <el-button @click="goToPreviousPage" class="fullscreen-btn" icon="ArrowLeft">
        上一页
      </el-button>
      <el-button @click="goToNextPage" class="fullscreen-btn" icon="ArrowRight">
        下一页
      </el-button>
    </div>

    <!-- 触发全屏模式的按钮 -->
    <el-tooltip content="全屏模式" placement="bottom">
      <el-button v-if="!isFullScreen" @click="requestFullScreen" class="fullscreen-button" icon="FullScreen">
      </el-button>
    </el-tooltip>
    <el-text class="left-top-text" v-if="leftTopText && !leftTopText.includes('null')">{{ leftTopText }}</el-text>

    <el-text class="center-bottom-text" v-if="bottomCenterText && !bottomCenterText.includes('null')">
      {{ bottomCenterText }}
    </el-text>

    <el-text class="top-tip-text" v-if="topTipText" v-html="topTipText">
    </el-text>

    <el-text class="warning-text" v-if="warningText && isFullScreen">{{ warningText }}</el-text>
    <el-text class="success-text" v-if="successText && isFullScreen">{{ successText }}</el-text>

    <!--    <el-tooltip content="锁定键盘-不能上下左右切换" placement="top">-->
    <!--      <el-button v-if="isFreezeKeyboardWithoutForce || isFreezeKeyboard" @click="changeIsFreezeKeyboard(false)"-->
    <!--                 class="lockIcon-button" icon="lock">-->
    <!--      </el-button>-->
    <!--      <el-button v-else @click="changeIsFreezeKeyboard(true)" class="lockIcon-button" icon="unlock">-->
    <!--      </el-button>-->
    <!--    </el-tooltip>-->

    <el-tooltip content="显示/关闭 框" placement="top">
      <el-button @click="changeShowKuang()" :class="{'active-icon': showKuang}" class="menuIcon-button" icon="menu">
      </el-button>
    </el-tooltip>

    <el-tooltip content="显示/关闭 等级和分数信息" placement="top" v-if="!isConfig">
      <el-button @click="changeScoreDetailShow()" :class="{'active-icon': showScoreDetail}" class="ticketsIcon-button"
                 icon="checked">
      </el-button>
    </el-tooltip>
    <el-tooltip content="显示/关闭 每个小题的分数" placement="top" v-else>
      <el-button @click="changeShowQsScore()" :class="{'active-icon': showQsScore}" class="ticketsIcon-button"
                 icon="checked">
      </el-button>
    </el-tooltip>

    <el-tooltip content="保持/恢复 缩放比例" placement="top">
      <el-button @click="changeMaintainScalingRatio()" :class="{'active-icon': maintainScalingRatio}"
                 class="DCaretIcon-button" icon="DCaret">
      </el-button>
    </el-tooltip>

    <el-tooltip content="显示/关闭 区域和题目序号" placement="top">
      <el-button @click="changeShowDetail()" :class="{'active-icon': showDetail}" class="detail-button" icon="List">
      </el-button>
    </el-tooltip>

    <el-tooltip content="全屏模式" placement="bottom">
      <el-button v-if="!isFullScreen" @click="requestFullScreen" class="fullscreen-button" icon="FullScreen">
      </el-button>
    </el-tooltip>

    <el-tooltip content="旋转-只支持右旋【此项不影响导出】" placement="bottom" v-if="!isConfig">
      <el-button @click="startRation()" class="ration-button" icon="RefreshRight">
      </el-button>
    </el-tooltip>
    <div v-else>
      <el-tooltip content="开始自由画框" placement="bottom">
        <el-button @click="changeFreeDraw()" :class="{'active-icon': isFreeDraw}" class="Briefcase-button"
                   icon="Briefcase">
        </el-button>
      </el-tooltip>

      <!-- <el-tooltip content="开启/关闭 区域自动排序" placement="bottom">
        <el-button @click="changeIsAutoSortArea()" :class="{'active-icon': isAutoSortArea}" class="Briefcase-button"
                   style="right: 90px">
          <i class="el-icon">
            <svg t="1744977204629" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                 p-id="2295" width="200" height="200">
              <path
                  d="M326.08 670.72V64H455.04v768h-0.64l-0.192 128.128L64 599.296l88.064-87.68 174.016 159.104z m380.992-318.272v607.68H577.92V171.648l0.32 0.192L578.816 64 960 414.72l-79.68 94.976-173.248-157.248z"
                  fill="currentColor" p-id="2296"></path>
            </svg>
          </i>

        </el-button>
      </el-tooltip> -->
    </div>


    <div v-if="showInput" class="type-input">
      <el-card style="max-width: 450px">
        <template #header>
          <div class="card-header">
            <span>请输入{{inputType === 'scorePoint' ? '分数' : (inputType === 'studentNumber' ? '学号' : '姓名')}}</span>
          </div>
        </template>
        <el-input ref="myInput" v-model="inputValue" placeholder="请输入！" style="width: 350px"
                  :type=" (inputType === 'scorePoint' || inputType === 'studentNumber') ? 'number' : 'text'"
                  @change="submitValue()" @focus="inputFocus"
                  @blur="inputBlur"></el-input>
        <template #footer>
          <el-button @click="showInput = false, inputBlur()" style="margin-left: 10px">取消</el-button>
          <el-button type="primary" @click="submitValue">确定</el-button>
        </template>
      </el-card>
    </div>

    <div v-if="isAutoCrop">
      <el-button class="ocr-button" @click="unCrop(true, false, true)">
        <el-image src="/icon/ai.svg" class="icon"></el-image>
        <el-text class="text">开始识别</el-text>
      </el-button>
      <el-button class="ocr-noOcr-button" @click="unCrop(true, false, false, true)" type="primary">
        <el-icon>
          <CircleClose/>
        </el-icon>
        <el-text class="text">不识别(分数识别类型)</el-text>
      </el-button>
      <el-button class="ocr-cancel-button" @click="unCrop(true, true)" type="warning">
        <el-icon>
          <CloseBold/>
        </el-icon>
        <el-text class="text">取消画框</el-text>
      </el-button>
    </div>
    <div v-if="isCroping && !isAutoCrop">
      <el-button class="ocr-cancel-button" style="right: calc(25% - 130px);"
                 @click="unCrop(true, false, false, false, true)" type="warning">
        <el-icon>
          <CloseBold/>
        </el-icon>
        <el-text class="text">删除该框</el-text>
      </el-button>
    </div>
    <div v-if="isCroping || isAutoCrop" :class="{'active': isKeyboardForPosition}"
         class="keyboard-position-button"
         @click="changeIsKeyboardForPosition()">
      <i class="el-button__icon">
        <svg t="1744979133774" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
             p-id="2384" width="200" height="200">
          <path
              d="M64 64.96l0 195.136 195.648 0L259.648 64.96 64 64.96zM208.064 208.064 115.52 208.064 115.52 116.992l92.608 0L208.128 208.064z"
              fill="currentColor" p-id="2385"></path>
          <path
              d="M764.352 64.96l0 195.136L960 260.096 960 64.96 764.352 64.96zM908.48 208.064l-92.608 0L815.872 116.992l92.608 0L908.48 208.064z"
              fill="currentColor" p-id="2386"></path>
          <path
              d="M64 763.776l0 195.264 195.648 0 0-195.264L64 763.776zM208.064 907.008 115.52 907.008l0-91.136 92.608 0L208.128 907.008z"
              fill="currentColor" p-id="2387"></path>
          <path
              d="M764.352 763.776l0 195.264L960 959.04l0-195.264L764.352 763.776zM908.48 907.008l-92.608 0 0-91.136 92.608 0L908.48 907.008z"
              fill="currentColor" p-id="2388"></path>
          <path d="M240.576 154.944l560 0 0 50.752-560 0 0-50.752Z" fill="currentColor" p-id="2389"></path>
          <path d="M240.576 820.736l560 0 0 50.88-560 0 0-50.88Z" fill="currentColor" p-id="2390"></path>
          <path d="M154.816 240.832l50.752 0 0 560-50.752 0 0-560Z" fill="currentColor" p-id="2391"></path>
          <path d="M820.608 240.832l50.752 0 0 560-50.752 0 0-560Z" fill="currentColor" p-id="2392"></path>
        </svg>
      </i>
      <el-tooltip placement="top" content="上下左右键用来调整框的位置/大小（按数字0或Z键也可切换）">
        <el-text class="text">{{ isKeyboardForPosition ? '调整大小' : '调整位置' }}</el-text>
      </el-tooltip>
    </div>

    <div v-if="!isKeyboardForPosition" :class="{'active': isZoomIn}"
         class="keyboard-position-button"
         style="bottom: calc(50vh - 220px);"
         @click="isZoomIn = !isZoomIn">
      <i class="el-button__icon">
        <svg t="1745741855098" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
             p-id="2322" width="200" height="200">
          <path
              d="M926.72 829.44q28.672 32.768 31.232 57.344t-18.944 48.128q-24.576 27.648-54.272 26.112t-57.344-24.064l-164.864-158.72q-46.08 30.72-99.84 47.616t-113.152 16.896q-80.896 0-151.552-30.72t-123.392-83.456-82.944-123.392-30.208-151.552q0-79.872 30.208-150.528t82.944-123.392 123.392-83.456 151.552-30.72 151.552 30.72 123.392 83.456 83.456 123.392 30.72 150.528q0 61.44-17.92 116.736t-49.664 101.376q13.312 14.336 37.376 38.4t48.128 48.64 44.544 44.032zM449.536 705.536q53.248 0 100.352-19.968t81.92-54.784 54.784-81.92 19.968-100.352-19.968-100.352-54.784-81.92-81.92-54.784-100.352-19.968-99.84 19.968-81.408 54.784-55.296 81.92-20.48 100.352 20.48 100.352 55.296 81.92 81.408 54.784 99.84 19.968zM512 384l128 0 0 128-128 0 0 128-129.024 0 0-128-126.976 0 0-128 126.976 0 0-128 129.024 0 0 128z"
              p-id="2323" fill="currentColor"></path>
        </svg>
      </i>
      <el-tooltip placement="top" content="上下左右键用来放大/缩小框的大小（按数字Z也可切换）">
        <el-text class="text">{{ isZoomIn ? '放大' : '缩小' }}</el-text>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import {useUserStore} from "@/store";
import {CircleClose, CloseBold} from "@element-plus/icons-vue";


const store = useUserStore();
export default {
  components: {CloseBold, CircleClose},
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    isEmpty: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: ''
    },
    leftTopText: {
      type: String,
      default: ''
    },
    warningText: {
      type: String,
      default: ''
    },
    successText: {
      type: String,
      default: ''
    },
    // 强制，比如输入框等等，强制锁定
    isFreezeKeyboard: {
      type: Boolean,
      default: false
    },
    defaultIsFreezeKeyboardWithoutForce: {
      type: Boolean,
      default: false
    },
    bottomCenterText: {
      type: String,
      default: ''
    },
    offsetX: {
      type: Number,
      default: 0
    },
    offsetY: {
      type: Number,
      default: 0
    },
    isConfig: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isFreezeKeyboardTemp: false,
      imgUrl: null,
      scale: 1,
      translate: {x: 0, y: 0},
      dragging: false,
      dragStart: {x: 0, y: 0},
      image: null,
      canvasContext: null,
      lastMoveTime: 0,
      lastTime: 0,
      answers: null,
      kuangs: null,
      rects: null,
      isFullScreen: false,
      // 不强制，用户可以选择
      isFreezeKeyboardWithoutForce: this.defaultIsFreezeKeyboardWithoutForce,
      isRightRation: false,
      inputValue: '',
      showInput: false,
      inputType: '',
      showKuang: true,
      showScoreDetail: true,
      showQsScore: true,
      rotation: 0,
      identify: '',
      studentNumber: '',
      showDetail: false,
      clickIndexScorePoint: null,
      isClickOrder: false,
      isCroping: false,
      cropStart: null,   // 记录右键按下时的位置（canvas 坐标）
      cropCurrent: null,  // 记录拖拽过程中的当前鼠标位置,
      isAutoCrop: false,
      isClick: false,  // 标记是否处于右键拖拽状态
      rightDragStart: null,    // 记录右键按下时的起始位置
      dragThreshold: 15 * 2.5,
      haveDrag: false,
      passJudge: false,
      topTipText: null,
      maintainScalingRatio: true,
      isFreeDraw: false,
      isAutoSortArea: false,
      isKeyboardForPosition: true,
      rafId: null,
      isZoomIn: false,
      needJudgeBorder: false,
      isImageWidthBigger: true,
      isExtracting: []
    };
  },
  computed: {
    xyOffset() {
      if (this.id) {
        return store.getXYOffsetData(this.id);
      } else {
        return {xOffset: 0, yOffset: 0}
      }
    },
    markZoom() {
      if (this.id) {
        return store.getMarkZoomDataById(this.id) || 100;
      } else {
        return 100
      }
    },
    fontSize() {
      if (this.id) {
        return store.getFontSizeDataById(this.id) || 100;
      } else {
        return 100
      }
    },
    opacity() {
      if (this.id) {
        return store.getOpacityDataById(this.id) || 1.0;
      } else {
        return 1.0;
      }
    }
  },
  watch: {
    xyOffset: {
      handler(newVal, oldVal) {
        this.needJudgeBorder = true;
        this.scheduleRender()
      },
      deep: true
    },
    markZoom: {
      handler(newVal, oldVal) {
        this.scheduleRender()
      },
      deep: true
    },
    fontSize: {
      handler(newVal, oldVal) {
        this.scheduleRender()
      },
      deep: true
    },
  },
  mounted() {
    const canvas = this.$refs.canvas;
    this.canvasContext = canvas.getContext("2d");
    this.setCanvasSize();
    this.isFreeDraw = store.getIsFreeDraw;
    this.isAutoSortArea = store.getIsAutoSortArea;
    window.addEventListener("mouseup", this.onMouseUp);
    window.addEventListener("resize", this.setCanvasSize);
    window.addEventListener("keydown", this.onKeyDown); // 监听键盘事件

    document.addEventListener('fullscreenchange', this.exitFullScreen);
    if (this.isConfig) {
      this.showDetail = true;
    }
  },
  beforeDestroy() {
    window.removeEventListener("mouseup", this.onMouseUp);
    window.removeEventListener("resize", this.setCanvasSize);
    window.removeEventListener("keydown", this.onKeyDown);
    window.removeEventListener("fullscreenchange", this.exitFullScreen);
    if (this.rafId !== null) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
  },
  methods: {
    scheduleRender() {
      this.renderCanvas();
    },
    jumpNextPage() {
      this.$emit("arrow-key-pressed", 1);
    },
    changeIsKeyboardForPosition() {
      this.isKeyboardForPosition = !this.isKeyboardForPosition;
    },
    changeIsAutoSortArea() {
      this.isAutoSortArea = !this.isAutoSortArea;
      store.setIsAutoSortArea(this.isAutoSortArea);
    },
    changeFreeDraw() {
      this.isFreeDraw = !this.isFreeDraw;
      store.setIsFreeDraw(this.isFreeDraw);
    },
    clearCropped() {
      this.cropStart = null;
      this.cropCurrent = null;
      this.scheduleRender();
    },
    doCrop(area) {
      this.isCroping = true;
      if (area) {
        this.cropStart = {x: area.x, y: area.y};
        this.cropCurrent = {x: area.x + area.width, y: area.y + area.height};
      } else {
        this.cropStart = null;
        this.cropCurrent = null;
      }
      this.topTipText = "操作方法：<br> 1.按住鼠标右键拖动选择裁剪区域 <br> 2. 使用键盘上下左右键移动框的位置，右键单击/Enter键结束<br>3.为了框住全部答案，画框时请画大一些，框与框之间可以交叉";
      this.scheduleRender();
    },
    onRightCropMouseDown(event) {
      if (!this.isConfig) return;
      if (!this.isFreeDraw && !this.isCroping) return;
      // 有没有拖动
      this.haveDrag = false;
      // 移动量是否超出来判断是不是click事件
      this.isClick = true;
      // 是否通过判断
      this.passJudge = false;
      this.rightDragStart = {x: event.clientX, y: event.clientY};
      window.addEventListener("mousemove", this.onRightCropMouseMove);
      window.addEventListener("mouseup", this.onRightCropMouseUp);

    },
    startRightCropMouseDown(event) {

      this.cropStart = null;
      this.cropCurrent = null;
      event.preventDefault();
      const canvas = this.$refs.canvas;
      const rect = canvas.getBoundingClientRect();
      const scaleX = rect.width / canvas.width;
      const scaleY = rect.height / canvas.height;

      const startX = (this.rightDragStart.x - rect.left) / scaleX;
      const startY = (this.rightDragStart.y - rect.top) / scaleY;
      const {x, y} = this.transformCanvasToImage(startX, startY)
      this.cropStart = {x, y};
      this.cropCurrent = {x, y};
      this.topTipText = "操作方法：<br> 1.按住鼠标右键拖动选择裁剪区域 <br> 2. 使用键盘上下左右键移动框的位置，右键单击/Enter键结束<br>3.为了框住全部答案，画框时请画大一些，框与框之间可以交叉";
      this.scheduleRender();
    },
    onRightCropMouseMove(event) {
      // 没有通过判断，要判断是否是点击事件
      if (!this.passJudge) {
        this.haveDrag = true;
        const canvas = this.$refs.canvas;
        const rect = canvas.getBoundingClientRect();
        const scaleX = rect.width / canvas.width;
        const scaleY = rect.height / canvas.height;

        const deltaX = (event.clientX - this.rightDragStart.x) / scaleX;
        const deltaY = (event.clientY - this.rightDragStart.y) / scaleY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        // 如果移动距离超过阈值，则认为是拖拽
        if (distance > this.dragThreshold) {
          if (!this.isCroping) this.isAutoCrop = true;
          this.haveDrag = true;
          this.passJudge = true;
          this.startRightCropMouseDown(event);
        }
      }


      if (this.passJudge) {
        const canvas = this.$refs.canvas;
        const rect = canvas.getBoundingClientRect();
        const scaleX = rect.width / canvas.width;
        const scaleY = rect.height / canvas.height;

        const currentX = (event.clientX - rect.left) / scaleX;
        const currentY = (event.clientY - rect.top) / scaleY;
        const {x, y} = this.transformCanvasToImage(currentX, currentY);

        this.cropCurrent = {x, y};
        this.scheduleRender();
      }
    },

    onRightCropMouseUp(event) {
      // 移除 window 上的监听
      window.removeEventListener("mousemove", this.onRightCropMouseMove);
      window.removeEventListener("mouseup", this.onRightCropMouseUp);

      if (!this.passJudge) {
        // 若未触发拖拽，则视为单击
        this.onRightClick(event, true);
      }
    },
    unCrop(clickEnd = false, isCancel = false, isUseOcr = true, isScorePoint = false, isDelete = false) {
      if (isCancel) {
        // 直接结束
        this.isCroping = false;
        this.isAutoCrop = false;
        this.topTipText = null;
        this.isCroping = false;
        this.isAutoCrop = false;
        this.cropStart = null;
        this.cropCurrent = null;
        this.scheduleRender();
        return;
      }
      if (this.cropStart === null || this.cropCurrent === null) {
        this.isCroping = false;
        this.isAutoCrop = false;
        return null;
      }
      const imageStart = this.cropStart;
      const imageEnd = this.cropCurrent;
      const imageX = Math.min(imageStart.x, imageEnd.x);
      const imageY = Math.min(imageStart.y, imageEnd.y);
      const imageWidthCrop = Math.abs(imageEnd.x - imageStart.x);
      const imageHeightCrop = Math.abs(imageEnd.y - imageStart.y);
      // 返回裁剪区域数据：这里减去固定偏移量 60 和 30（可按需调整）

      if (clickEnd) {
        const data = {
          x: Math.round(imageX),
          y: Math.round(imageY),
          width: Math.round(imageWidthCrop),
          height: Math.round(imageHeightCrop),
          rotate: 0,
          scaleX: 1,
          scaleY: 1
        }
        if (isDelete) {
          data.width = 0;
          data.height = 0;
        }
        if (this.isAutoCrop) {
          this.$emit('auto-crop-completed', data, isUseOcr, isScorePoint);
        }
        if (this.isCroping) {
          this.$emit('button-crop-completed', data);
        }
      }
      this.topTipText = null;
      this.isCroping = false;
      this.isAutoCrop = false;
      this.cropStart = null;
      this.cropCurrent = null;
      this.scheduleRender();
      return {
        x: Math.round(imageX),
        y: Math.round(imageY),
        width: Math.round(imageWidthCrop),
        height: Math.round(imageHeightCrop),
        rotate: 0,
        scaleX: 1,
        scaleY: 1
      };
    },
    transformCanvasToImage(canvasX, canvasY) {
      // 将 canvas 坐标转换为图像坐标，参考 onRightClick 中的逻辑
      const imageWidth = this.image.width;
      const imageHeight = this.image.height;
      const canvas = this.$refs.canvas;
      const centerX = this.translate.x + (imageWidth * this.scale) / 2;
      const centerY = this.translate.y + (imageHeight * this.scale) / 2;
      const dx = canvasX - centerX;
      const dy = canvasY - centerY;
      // 逆旋转、逆缩放
      const cos = Math.cos(-this.rotation);
      const sin = Math.sin(-this.rotation);
      const unrotatedX = (dx * cos - dy * sin) / this.scale;
      const unrotatedY = (dx * sin + dy * cos) / this.scale;
      return {
        x: unrotatedX + imageWidth / 2,
        y: unrotatedY + imageHeight / 2
      };
    },
    changeShowDetail(e = null) {
      if (e !== null) {
        this.showDetail = e;
      } else {
        this.showDetail = !this.showDetail;
      }
      this.scheduleRender();
    },
    inputBlur() {
      this.isFreezeKeyboardWithoutForce = this.isFreezeKeyboardTemp;
    },
    inputFocus() {
      this.isFreezeKeyboardTemp = this.isFreezeKeyboardWithoutForce;
      this.isFreezeKeyboardWithoutForce = true;
    },
    changeShowKuang() {
      this.showKuang = !this.showKuang;
      this.scheduleRender();
    },
    changeScoreDetailShow() {
      this.showScoreDetail = !this.showScoreDetail;
      this.scheduleRender();
    },
    changeShowQsScore() {
      this.showQsScore = !this.showQsScore;
      this.scheduleRender();
    },
    changeMaintainScalingRatio() {
      this.maintainScalingRatio = !this.maintainScalingRatio;
      this.scheduleRender();
    },
    submitValue() {
      if (this.inputType === 'scorePoint') {
        this.$emit("rect-right-clicked-input", this.inputValue, this.inputType, this.clickIndexScorePoint);
      } else {
        this.$emit("rect-right-clicked-input", this.inputValue, this.inputType);
      }
      this.showInput = false;
      this.inputValue = '';
      this.inputBlur();
    },
    startRation() {
      this.rotation = (this.rotation + Math.PI / 2) % (2 * Math.PI);
      this.isRightRation = !this.isRightRation;
      this.scheduleRender();
    },
    changeIsFreezeKeyboard(e) {
      this.isFreezeKeyboardWithoutForce = e;
    },
    requestFullScreen() {
      const el = this.$refs.imageWrapper;
      if (!el) return;

      // 浏览器兼容处理
      if (el.requestFullscreen) {
        el.requestFullscreen();
      } else if (el.webkitRequestFullscreen) {
        el.webkitRequestFullscreen();
      } else if (el.mozRequestFullScreen) {
        el.mozRequestFullScreen();
      } else if (el.msRequestFullscreen) {
        el.msRequestFullscreen();
      } else {
        console.warn("当前浏览器不支持全屏 API");
      }
    },
    requestExitFullScreen() {
      const el = this.$refs.imageWrapper;
      if (!el) return;

      // 浏览器兼容处理
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      } else {
        console.warn("当前浏览器不支持退出全屏 API");
      }
    },
    exitFullScreen() {
      this.isFullScreen = !this.isFullScreen;
    },
    goToPreviousPage() {
      this.$emit("arrow-key-pressed", -1);
    },
    goToNextPage() {
      this.$emit("arrow-key-pressed", 1);
    },

    onKeyDown(event) {
      let isFreezeKeyboard = this.isFreezeKeyboard || this.isFreezeKeyboardWithoutForce;
      const isKeyboardForPosition = this.isKeyboardForPosition;
      const isZoomIn = this.isZoomIn;
      if (this.isCroping || this.isAutoCrop) {
        if (event.key === '0') {
          this.isKeyboardForPosition = !isKeyboardForPosition;
        } else if (event.key === 'z' || event.key === 'Z') {
          this.isKeyboardForPosition = false;
          this.isZoomIn = !isZoomIn;
        } else if (event.key === "ArrowLeft" && !isFreezeKeyboard) {
          if (isKeyboardForPosition) {
            this.cropStart.x = this.cropStart.x - 4;
            this.cropCurrent.x = this.cropCurrent.x - 4;
          } else {
            if (isZoomIn) {
              this.cropStart.x = this.cropStart.x - 4;
            } else {
              this.cropStart.x = this.cropStart.x + 4;
            }
          }
        } else if (event.key === "ArrowRight" && !isFreezeKeyboard) {
          if (isKeyboardForPosition) {
            this.cropCurrent.x = this.cropCurrent.x + 4;
            this.cropStart.x = this.cropStart.x + 4;
          } else {
            if (isZoomIn) {
              this.cropCurrent.x = this.cropCurrent.x + 4;
            } else {
              this.cropCurrent.x = this.cropCurrent.x - 4;
            }
          }
        } else if (event.key === "ArrowUp" && !isFreezeKeyboard) {
          if (isKeyboardForPosition) {
            this.cropStart.y = this.cropStart.y - 4;
            this.cropCurrent.y = this.cropCurrent.y - 4;
          } else {
            if (isZoomIn) {
              this.cropStart.y = this.cropStart.y - 4;
            } else {
              this.cropStart.y = this.cropStart.y + 4;
            }
          }
        } else if (event.key === "ArrowDown" && !isFreezeKeyboard) {
          if (isKeyboardForPosition) {
            this.cropStart.y = this.cropStart.y + 4;
            this.cropCurrent.y = this.cropCurrent.y + 4;
          } else {
            if (isZoomIn) {
              this.cropCurrent.y = this.cropCurrent.y + 4;
            } else {
              this.cropCurrent.y = this.cropCurrent.y - 4;
            }
          }
        } else if (event.key === 'Enter') {
          this.unCrop(true, false, true, false);
        } else if (event.key === 'Escape') {
          this.unCrop(true, true);
        }
        this.scheduleRender();
      } else {
        if (event.key === "Escape") {
          if (this.isFullScreen) {
            this.exitFullScreen();
          }
        } else if (event.key === "ArrowLeft" && !isFreezeKeyboard) {
          this.$emit("arrow-key-pressed", -1);
        } else if (event.key === "ArrowRight" && !isFreezeKeyboard) {
          this.$emit("arrow-key-pressed", 1);
        } else if (event.key === "ArrowUp" && !isFreezeKeyboard) {
          this.$emit("arrow-key-pressed", -1);
        } else if (event.key === "ArrowDown" && !isFreezeKeyboard) {
          this.$emit("arrow-key-pressed", 1);
        } else if (event.key.toLowerCase() === "i" && !isFreezeKeyboard) {
          if (this.isFullScreen) {
            this.inputValue = this.identify;
            this.inputType = 'identify';
            this.showInput = true;
            this.$nextTick(() => {
              this.$refs.myInput.focus()
            })

          } else {
            this.$emit("rect-right-clicked-input", null, 'identify');
            return;
          }
        } else if (event.key.toLowerCase() === "n" && !isFreezeKeyboard) {
          if (this.isFullScreen) {
            this.inputValue = this.studentNumber;
            this.inputType = 'studentNumber';
            this.showInput = true;
            this.$nextTick(() => {
              this.$refs.myInput.focus()
            })
          } else {
            this.$emit("rect-right-clicked-input", null, 'studentNumber');
            return;
          }
        }
      }

    },
    errorCorrectionRefresh(rightIdx, data, answer = null) {
      if (rightIdx < this.rects.length && data) {
        this.rects[rightIdx] = data;
        if (answer) {
          this.answers[rightIdx] = answer;
        }
        this.renderCanvas();
      }
    },
    lightUpdate(rects, answers, kuangs) {
      this.rects = rects;
      this.answers = answers;
      this.kuangs = kuangs;
      this.identify = '';
      this.studentNumber = '';
      if (
          this.answers &&
          Array.isArray(this.answers) &&
          this.answers.length !== 0
      ) {
        answers.forEach((item) => {
          if (item.type) {
            if (item.type === 'identify') {
              this.identify = item.text;
            } else if (item.type === 'studentNumber') {
              this.studentNumber = item.text;
            }
          }
        })
      }
      this.scheduleRender();
    },
    setImg(url, rects, answers, kuangs, forceAdjust, requestAdjust) {
      if (!url) return;
      let needAdjust = this.imgUrl === null || forceAdjust || (requestAdjust && !this.maintainScalingRatio);
      this.imgUrl = url;
      this.rects = rects;
      this.answers = answers;
      this.kuangs = kuangs;
      this.identify = '';
      this.studentNumber = '';
      if (
          this.answers &&
          Array.isArray(this.answers) &&
          this.answers.length !== 0
      ) {
        answers.forEach((item) => {
          if (item.type) {
            if (item.type === 'identify') {
              this.identify = item.text;
            } else if (item.type === 'studentNumber') {
              this.studentNumber = item.text;
            }
          }
        })
      }

      const img = new Image();
      img.src = url;
      img.onload = () => {
        this.image = img;

        if (needAdjust) {
          this.adjustImageScale();
        }

        this.scheduleRender();
      };
      img.onerror = () => {
        console.error("图片加载失败");
      };
    },
    setCanvasSize() {
      const canvas = this.$refs.canvas;
      if (canvas) {
        canvas.width = canvas?.parentElement?.offsetWidth ?? 0;
        canvas.height = canvas?.parentElement?.offsetHeight ?? 0;
      }

      this.adjustImageScale();
      this.scheduleRender();
    },
    adjustImageScale() {
      if (!this.image) return;
      const canvas = this.$refs.canvas;
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;

      const imgWidth = this.image.width;
      const imgHeight = this.image.height;

      const scaleX = canvasWidth / imgWidth;
      const scaleY = canvasHeight / imgHeight;

      this.scale = Math.min(scaleX, scaleY);

      this.translate.x = (canvasWidth - imgWidth * this.scale) / 2;
      this.translate.y = (canvasHeight - imgHeight * this.scale) / 2;
    },
    resetTransform() {
      this.scale = 1;
      this.translate = {x: 0, y: 0};
      this.scheduleRender();
    },
    onMouseDown(event) {
      if (event.button !== 0) return;
      this.dragging = true;
      this.dragStart = {x: event.clientX, y: event.clientY};
      window.addEventListener("mousemove", this.onMouseMove);
    },
    onMouseMove(event) {
      if (!this.dragging) return;

      const now = Date.now();
      if (now - this.lastMoveTime < 1000 / 60) {
        return;
      }
      this.lastMoveTime = now;

      const dx = event.clientX - this.dragStart.x;
      const dy = event.clientY - this.dragStart.y;
      this.translate.x += dx;
      this.translate.y += dy;
      this.dragStart = {x: event.clientX, y: event.clientY};
      this.scheduleRender();
    },
    onMouseUp(event) {
      if (event.button === 0) {
        this.dragging = false;
        window.removeEventListener("mousemove", this.onMouseMove);
      }
    },
    onWheelZoom(event) {
      event.preventDefault();

      const now = Date.now();
      if (now - this.lastTime < 1000 / 60) {
        return;
      }
      this.lastTime = now;

      const canvas = this.$refs.canvas;
      const rect = canvas.getBoundingClientRect();
      const scaleX = rect.width / canvas.width;
      const scaleY = rect.height / canvas.height;

      const mouseX = (event.clientX - rect.left) / scaleX;
      const mouseY = (event.clientY - rect.top) / scaleY;

      const zoomFactor = event.deltaY > 0 ? 0.96 : 1.04;
      let newScale = this.scale * zoomFactor;
      newScale = Math.min(Math.max(newScale, 0.1), 10);

      const x = mouseX - this.translate.x;
      const y = mouseY - this.translate.y;

      this.translate.x -= (newScale - this.scale) * (x / this.scale);
      this.translate.y -= (newScale - this.scale) * (y / this.scale);

      this.scale = newScale;

      this.scheduleRender();
    },
    onRightClick(event, isAuto) {
      // 判断是否是拖动还是点击
      if (this.isConfig) {
        if (!isAuto && (this.isAutoCrop || this.isCroping)) {
          return;
        }
        if (this.isAutoCrop || this.isCroping) {
          // 右键结束
          return this.unCrop(true)
        }
        if (this.isFreeDraw && !isAuto) {
          return;
        }
      }

      const canvas = this.$refs.canvas;
      const rect = canvas.getBoundingClientRect();
      const scaleX = rect.width / canvas.width;
      const scaleY = rect.height / canvas.height;

      const mouseX = (event.clientX - rect.left) / scaleX;
      const mouseY = (event.clientY - rect.top) / scaleY;

      const imageWidth = this.image.width;
      const imageHeight = this.image.height;

      // 计算图像在 canvas 上的中心位置，与 scheduleRender 中保持一致
      const centerX = this.translate.x + (imageWidth * this.scale) / 2;
      const centerY = this.translate.y + (imageHeight * this.scale) / 2;

      // 得到点击点相对于图像中心的偏移（canvas 坐标）
      const dx = mouseX - centerX;
      const dy = mouseY - centerY;

      // 逆向变换：先逆旋转，再逆缩放
      const cos = Math.cos(this.rotation);
      const sin = Math.sin(this.rotation);
      const unrotatedX = (dx * cos + dy * sin) / this.scale;
      const unrotatedY = (-dx * sin + dy * cos) / this.scale;

      // 将坐标从以图像中心为原点转换到以图像左上角为原点
      // 如果 scheduleRender 绘图时有固定偏移，则这里也应使用相同偏移；
      // 若绘图时没有应用固定偏移，则可将下面两个数值设置为 0
      const fixedOffsetX = 50;
      const fixedOffsetY = 20;
      const imageX = unrotatedX + imageWidth / 2 - fixedOffsetX;
      const imageY = unrotatedY + imageHeight / 2 - fixedOffsetY;
      if (this.isClickOrder) {
        // 传过去坐标,自动刷新
        this.$emit('clickOrder', {
          x: Math.round(imageX) - 60,
          y: Math.round(imageY) - 30,
          width: 20,
          height: 20,
          rotate: 0,
          scaleX: 1,
          scaleY: 1
        })
        return;
      }

      let clickedRect = null;
      let index = null;
      if (!this.isConfig) {
        if (this.rects && Array.isArray(this.rects)) {
          let xyOffset = this.xyOffset;
          for (let rectItem of this.rects) {
            if (!rectItem) continue;
            const {x, y, width, height} = rectItem;
            if (
                imageX >= x + xyOffset.xOffset - 50 &&
                imageX <= x + 150 + xyOffset.xOffset &&
                imageY >= y + xyOffset.yOffset &&
                imageY <= y + 100 + xyOffset.yOffset
            ) {

              if (this.isFullScreen && 'isScorePoint' in rectItem && rectItem.isScorePoint === 2) {
                this.inputType = 'scorePoint';
                this.showInput = true;
                // this.inputValue= '';
                this.clickIndexScorePoint = this.rects.indexOf(rectItem);
                this.$nextTick(() => {
                  this.$refs.myInput.focus()
                })
                return;
              }
              clickedRect = rectItem;
              index = this.rects.indexOf(rectItem);
              break;
            }
          }
        }
        // 判断学生姓名学号是否 点中
        if (this.answers && Array.isArray(this.answers)) {
          let xyOffset = this.xyOffset;
          for (let answer of this.answers) {
            const {x, y, width, height} = answer.area;
            if (
                'type' in answer && (answer.type === 'identify' || answer.type === 'studentNumber') &&
                imageX >= x + xyOffset.xOffset &&
                imageX <= x + width + xyOffset.xOffset &&
                imageY >= y + xyOffset.yOffset &&
                imageY <= y + height + xyOffset.yOffset
            ) {

              if (this.isFullScreen) {
                if (answer.type === 'identify') {
                  this.inputValue = answer.text;
                  this.inputType = 'identify';
                } else if (answer.type === 'studentNumber') {
                  this.inputValue = answer.text;
                  this.inputType = 'studentNumber';
                }
                this.showInput = true;
                this.$nextTick(() => {
                  this.$refs.myInput.focus()
                })
              } else {
                this.$emit("rect-right-clicked-input", null, answer.type);
                return;
              }
            }
          }
        }

        if (clickedRect) {
          if (this.isFullScreen) {
            // 如果是分数识别区域-使用内置输入框
            this.$emit("rect-right-clicked", index);
          } else {
            this.$emit("rect-right-clicked", index);
          }
        }
      } else {
        // 没有点中
        let areaIdx = this.judgeAreaIdx(imageX, imageY);
        if (areaIdx !== -1) {
          this.$emit('empty-right-clicked', areaIdx, {
            x: Math.round(imageX) - 60,
            y: Math.round(imageY) - 30,
            width: 20,
            height: 20,
            rotate: 0,
            scaleX: 1,
            scaleY: 1
          })
        }
      }
    },
    // 判断点中的那个区域，然后返回idx
    judgeAreaIdx(imageX, imageY) {
      const kuangs = this.kuangs;
      const xyOffset = this.xyOffset;
      for (let i = 0; i < kuangs.length; i++) {
        if (!kuangs[i]?.area) continue;
        const {x, y, width, height} = kuangs[i].area;
        if (
            imageX >= x + xyOffset.xOffset &&
            imageX <= x + xyOffset.xOffset + width &&
            imageY >= y + xyOffset.yOffset &&
            imageY <= y + xyOffset.yOffset + height
        ) {
          return i;
        }
      }
      return -1;
    },
    renderCanvas() {
      const canvas = this.$refs.canvas;
      const ctx = this.canvasContext;
      const markZoom = this.markZoom;
      const fontSize = this.fontSize;
      const opacity = this.opacity;  // 获取全局透明度值
      const needJudgeBorder = this.needJudgeBorder;
      let warnText = '';
      if (!ctx || !this.image) return;

      const isConfig = this.isConfig;
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.save();

      const imageWidth = this.image.width;
      const imageHeight = this.image.height;
      this.isImageWidthBigger = imageWidth > imageHeight;
      // 计算图像中心：translate 已经确保图像初始居中显示
      const centerX = this.translate.x + (imageWidth * this.scale) / 2;
      const centerY = this.translate.y + (imageHeight * this.scale) / 2;

      ctx.translate(centerX, centerY);
      ctx.rotate(this.rotation);
      ctx.scale(this.scale, this.scale);
      ctx.drawImage(this.image, -imageWidth / 2, -imageHeight / 2);

      // 设置字体大小
      ctx.font = `${fontSize}px Arial`;

      const strokeStyle = "blue";
      const lineWidth = 3 / this.scale;
      let xyOffset = this.xyOffset;
      if (this.rects && Array.isArray(this.rects)) {
        this.rects.forEach((rect) => {
          if (!rect) return;

          const {x: rectX, y: rectY, width, height} = rect;

          if (rect.mark) {
            const markWidth = (markZoom * rect.mark.width) / rect.mark.height;
            const markHeight = markZoom;
            ctx.globalAlpha = opacity;  // 使用全局透明度值
            ctx.drawImage(
                rect.mark,
                rectX + 70 + xyOffset.xOffset - imageWidth / 2,
                rectY + xyOffset.yOffset - imageHeight / 2,
                markWidth,
                markHeight
            );
            ctx.globalAlpha = 1.0;  // 重置透明度
            return;
          }

          ctx.strokeStyle = rect.color || strokeStyle;
          ctx.lineWidth = lineWidth;
          ctx.strokeRect(rectX, rectY, width, height);
        });
      }
      let showDetail = this.showDetail;
      let qsIdx = 0;
      let showScoreDetail = this.showScoreDetail;
      let showQsScore = this.showQsScore;
      const isExtracting = this.isExtracting;
      if (
          this.answers &&
          Array.isArray(this.answers) &&
          this.answers.length !== 0
      ) {
        this.answers.forEach((answer) => {
          let isShow = !answer?.dontShowText;
          if (!showScoreDetail && ((answer?.type === 'scoreAreaText') || (answer?.type === 'totalScoreAreaText'))) {
            isShow = false;
          }
          if (isShow) {
            const {x, y, width, height} = answer.area;
            ctx.globalAlpha = opacity;  // 使用全局透明度值
            ctx.font = answer.isEssay ? `${this.fontSize * 0.8}px Arial` : `70px Arial`;
            ctx.fillStyle = answer.fillStyle || "red";
            ctx.textBaseline = "top";

            if (answer.isEssay) {
              ctx.fillText(answer.text, x + (answer.offsetX || 100) + xyOffset.xOffset - imageWidth / 2, y + (answer.offsetY || 100) + xyOffset.yOffset - imageHeight / 2);
            } else if (answer.text) {
              // if (answer.text.length > 5 && answer.text.length <= 10) {
              //   ctx.font = `${this.fontSize * 0.5}px Arial`;
              // } else if (answer.text.length > 10) {
              //   ctx.font = `${this.fontSize * 0.4}px Arial`;
              // } else {
              //   ctx.font = `${this.fontSize}px Arial`;
              // }
              ctx.font = `${this.fontSize}px Arial`;
              if (answer?.font) {
                // 无论是否在config页面，都使用全局字体大小替换
                const customFont = answer.font.replace(/\d+px/, `${this.fontSize}px`);
                ctx.font = customFont;
              }
              let text = answer.text;
              if (showQsScore && answer?.score !== undefined) {
                text = `${answer.text} ${answer.score}分`
              }
              ctx.fillText(text, x + width / 2 + (answer.offsetX || 160) + xyOffset.xOffset - imageWidth / 2, y + height / 2.5 + xyOffset.yOffset - imageHeight / 2);
            }
            ctx.globalAlpha = 1.0;  // 重置透明度
          }

        });
      }
      if (this.showKuang &&
          this.kuangs &&
          Array.isArray(this.kuangs) &&
          this.kuangs.length !== 0) {
        this.kuangs.forEach((rect,areaIdx) => {
          let isShow = true;
          if (!showScoreDetail && ((rect?.type === 'totalScoreArea') || (rect?.type === 'scoreArea'))) {
            isShow = false;
          }
          if (!rect || !rect.area || !('x' in rect.area)) {
            isShow = false;
          }
          if (isShow) {
            let {x, y, width, height} = rect.area
            // 绘制方框
            ctx.strokeStyle = rect.color || '#1677ff'; // 设置边框颜色
            ctx.lineWidth = 2; // 设置边框宽度
            ctx.font = '50px Arial';
            ctx.strokeRect(x - imageWidth / 2 + xyOffset.xOffset, y - imageHeight / 2 + xyOffset.yOffset, width, height); // 绘制方框

            let isFail = rect?.status === '2';
            let isShowDetail = (showDetail && !("type" in rect)) || isFail;
            let isExtract = isExtracting.indexOf(areaIdx) !== -1;
            // if (isFail) [
            if (isShowDetail) {
              ctx.fillText(('区域' + (areaIdx, areaIdx = areaIdx + 1) + (isExtract ? '-正在识别中' : '') + (isFail ? '-批改失败' : '') + (isConfig ? ` -  ${rect.scoreTypes} 共${rect.qsCnt ?? ''}题${rect.qsAllScore ?? ''}分${rect?.commonQuestionType ? '-' : ''}${rect?.commonQuestionType || ''}` : '')), x - imageWidth / 2, y - imageHeight / 2 - 50);
            }
          }
        })
      }

      if (this.needJudgeBorder) {
        this.needJudgeBorder = false;
        const imgW = this.image.width;
        const imgH = this.image.height;
        const xOff = this.xyOffset.xOffset;
        const yOff = this.xyOffset.yOffset;

        // 检查 kuangs 边界
        this.kuangs.forEach((rect, idx) => {
          const {x, y, width, height} = rect.area;
          const realX = x + xOff;
          const realY = y + yOff;
          if (realX < 0 || realY < 0 || realX + width > imgW || realY + height > imgH) {
            this.$message({
              message: `区域${idx + 1}超出图片边界`,
              type: 'error',
              duration: 2000
            });
          }
        });

        // 检查 answers 边界
        this.answers.forEach((ans, idx) => {
          if (!ans.area) return;
          const {x, y, width, height} = ans.area;
          const realX = x + xOff;
          const realY = y + yOff;
          if (realX < 0 || realY < 0 || realX + width > imgW || realY + height > imgH) {
            this.$message({
              message: `提醒：文本区域${idx + 1}超出图片边界，请注意！`,
              type: 'warning',
              duration: 2000
            });
          }
        });
      }

      if (this.isConfig && (this.isCroping || (this.isAutoCrop && this.passJudge))) {
        if (this.cropStart && this.cropCurrent) {
          ctx.strokeStyle = "#f70000";
          ctx.lineWidth = 4;
          const x = Math.min(this.cropStart.x, this.cropCurrent.x);
          const y = Math.min(this.cropStart.y, this.cropCurrent.y);
          const width = Math.abs(this.cropCurrent.x - this.cropStart.x);
          const height = Math.abs(this.cropCurrent.y - this.cropStart.y);
          ctx.strokeRect(x - imageWidth / 2 + xyOffset.xOffset, y - imageHeight / 2 + xyOffset.yOffset, width, height); // 绘制方框
        }

        // 保存当前状态
        ctx.save();
        // 重置变换，让下面操作基于 canvas 原始坐标
        ctx.setTransform(1, 0, 0, 1, 0, 0);

        // 绘制全屏遮罩层（比如深色半透明）
        ctx.fillStyle = "rgba(0, 0, 0, 0.4)";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 根据图像、变换参数计算出裁剪区域在 canvas 坐标下的位置
        const cos = Math.cos(this.rotation);
        const sin = Math.sin(this.rotation);

        if (this.cropStart && this.cropCurrent) {
          // cropStart 和 cropCurrent 是在图像坐标系中（即 transformCanvasToImage 后的值）
          const imgX1 = Math.min(this.cropStart.x, this.cropCurrent.x);
          const imgY1 = Math.min(this.cropStart.y, this.cropCurrent.y);
          const imgX2 = Math.max(this.cropStart.x, this.cropCurrent.x);
          const imgY2 = Math.max(this.cropStart.y, this.cropCurrent.y);

          const canvasCropX1 = centerX + ((imgX1 - imageWidth / 2) * cos - (imgY1 - imageHeight / 2) * sin) * this.scale;
          const canvasCropY1 = centerY + ((imgX1 - imageWidth / 2) * sin + (imgY1 - imageHeight / 2) * cos) * this.scale;

          const canvasCropX2 = centerX + ((imgX2 - imageWidth / 2) * cos - (imgY2 - imageHeight / 2) * sin) * this.scale;
          const canvasCropY2 = centerY + ((imgX2 - imageWidth / 2) * sin + (imgY2 - imageHeight / 2) * cos) * this.scale;

          const cropX = canvasCropX1;
          const cropY = canvasCropY1;
          const cropW = canvasCropX2 - canvasCropX1;
          const cropH = canvasCropY2 - canvasCropY1;

          // 使用全局合成模式为 destination-out 来清除遮罩区间
          ctx.globalCompositeOperation = "destination-out";
          ctx.fillStyle = "rgba(0, 0, 0, 1)";
          // ctx.fillRect(cropX, cropY, cropW, cropH);

          // 恢复默认合成模式
          ctx.globalCompositeOperation = "source-over";

          // 绘制边框突出当前裁剪框（可根据实际需求调整颜色与宽度）
          ctx.strokeStyle = "#f70000";
          ctx.lineWidth = 2;
          ctx.strokeRect(cropX, cropY, cropW, cropH);
        }


        // 恢复之前状态
        ctx.restore();
      }

      ctx.restore();
    },
  },
};
</script>

<style lang="scss" scoped>
.menuIcon-button,
.ticketsIcon-button,
.DCaretIcon-button,
.detail-button {
  :deep(.el-icon) {
    color: #606266; // 默认图标颜色
  }

  &.active-icon {
    background-color: rgba(124, 77, 255, 0.1); // 按钮激活状态下的背景颜色
    :deep(.el-icon) {
      color: #7C4DFF; // 激活状态下图标颜色
      svg {
        fill: currentColor;
      }
    }
  }
}


.image-preview-wrapper {
  width: 100%;
  height: 400px;
  overflow: hidden;
  background: #f5f5f5;
  position: relative;

  .preview-canvas {
    width: 100%;
    height: 100%;
    display: block;
    cursor: grab;
  }

  .preview-canvas:active {
    cursor: grabbing;
  }

  .empty-tip {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 18px;
    transition: opacity 0.3s;

    &.loading {
      font-size: 20px;
      color: #ffcc00;
    }

    .loading-text {
      animation: pulse 1.5s infinite;
    }
  }

  .fullscreen-button {
    position: absolute;
    bottom: 10px;
    right: 10px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    background-color: #007bff;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    border: none;
  }

  .lockIcon-button {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    color: red;
    cursor: pointer;
    border: none;
  }

  .menuIcon-button {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    color: red;
    cursor: pointer;
    border: none;
  }

  .ticketsIcon-button {
    position: absolute;
    top: 10px;
    right: 90px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    color: red;
    cursor: pointer;
    border: none;
  }

  .DCaretIcon-button {
    position: absolute;
    top: 10px;
    right: 130px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    color: red;
    cursor: pointer;
    border: none;
  }

  .detail-button {
    position: absolute;
    top: 10px;
    right: 50px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    color: red;
    cursor: pointer;
    border: none;
  }

  .name-number-text {
    position: absolute;
    top: 6px;
    left: calc(50% - 50px);
    z-index: 9;
    padding: 6px 12px;
    font-size: 25px;
    color: red;
  }

  .ration-button {
    position: absolute;
    bottom: 10px;
    right: 50px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    background-color: #007bff;
    color: #fff;
    border-radius: 5px;
    cursor: pointer;
    border: none;
  }

  .keyboard-position-button {
    position: absolute;
    bottom: calc(50vh - 100px);
    right: 10px;
    z-index: 9;
    padding: 6px 12px;
    cursor: pointer;
    width: 30px;
    height: 110px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border: 1px solid #1456f0;
    border-radius: 30px;
    box-shadow: none !important;
    align-items: center;
    justify-content: center;

    .text {
      writing-mode: vertical-rl;
      text-orientation: upright;
      color: #1456f0;
      font-size: 14px;
      font-weight: bolder;
      letter-spacing: 2px;
    }

    .el-button__icon svg {
      width: 14px;
      height: 14px;
      color: #1456f0;
      /* 如果你想让 icon 有点间距，可以加 margin-bottom */
      margin-bottom: 4px;

      &.active {
        color: #FFF;
      }
    }

    &.active {
      background-color: #1456f0;
      border: none;
      box-shadow: none;

      .el-button__icon svg {
        color: #FFF;
      }

      .text {
        color: #FFF;
      }
    }
  }

  .Briefcase-button {
    position: absolute;
    bottom: 10px;
    right: 50px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    color: red;
    cursor: pointer;
    border: none;

    :deep(.el-icon) {
      color: #007bff;
    }

    &.active-icon {
      background-color: #007bff;

      :deep(.el-icon) {
        color: #FFF;
        fill: #FFF;

        svg {
          fill: currentColor;
        }
      }
    }
  }

  .type-input {
    position: absolute;
    top: 40%;
    left: calc(50% - 200px);
    width: 600px;
    z-index: 9;
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
  }

  .ocr-button {
    position: fixed;
    width: 104px;
    height: 32px;
    background-image: linear-gradient(90deg, #A45DFF 0%, #3772FF 100%);
    border-radius: 6px;
    bottom: 55px;
    right: 25%;
    border: none;
    z-index: 999;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      text-align: center;
      line-height: 22px;
      margin-left: 5px;
      opacity: 0.7;
    }

    .icon {
      width: 16.4px;
      height: 16.4px;
    }
  }

  .ocr-noOcr-button {
    background-image: linear-gradient(90deg, #A45DFF 0%, #3772FF 100%);
    position: fixed;
    width: 180px;
    height: 32px;
    border-radius: 6px;
    bottom: 55px;
    right: calc(25% - 190px);
    border: none;
    z-index: 999;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      text-align: center;
      line-height: 22px;
      margin-left: 5px;
      opacity: 0.7;
    }
  }

  .ocr-cancel-button {
    background-image: linear-gradient(90deg, #A45DFF 0%, #3772FF 100%);
    position: fixed;
    width: 90px;
    height: 32px;
    border-radius: 6px;
    bottom: 55px;
    right: calc(25% - 290px);
    border: none;
    z-index: 999;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      text-align: center;
      line-height: 22px;
      margin-left: 5px;
      opacity: 0.7;
    }
  }

  .fullscreen-controls {
    position: absolute;
    bottom: 22px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
  }

  .fullscreen-btn {
    padding: 10px 20px;
    font-size: 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .left-top-text {
    position: absolute;
    font-size: 25px;
    color: red;
    font-weight: bolder;
    top: 10px;
    left: 10px;
    z-index: 10;
  }

  .top-tip-text {
    position: absolute;
    font-size: 14px;
    color: black;
    font-weight: bolder;
    top: 20px;
    left: 10px;
    z-index: 10;
  }

  .center-bottom-text {
    position: absolute;
    font-size: 16px;
    color: red;
    font-weight: bolder;
    bottom: 0px;
    left: calc(50% - 40px);
    z-index: 10;
  }

  .warning-text {
    position: absolute;
    top: 10px;
    left: calc(50% - 100px);
    display: inline-block;
    padding: 10px 20px;
    background-color: #fdf1d1;
    color: #e6a400;
    border: 1px solid #e6a400;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    margin: 10px 0;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
  }

  .success-text {
    position: absolute;
    top: 10px;
    left: calc(50% - 100px);
    display: inline-block;
    padding: 10px 20px;
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #3c763d;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    margin: 10px 0;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
  }


  .warning-text::before {
    content: '⚠️';
    margin-right: 10px;
    font-size: 18px;
  }
}
</style>
