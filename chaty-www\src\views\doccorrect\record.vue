<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="onBack">
      <template #content>
        <el-row align="middle" :gutter="20" style="width: 600px">
          <el-col :span="4">
            <span class="header-title">试卷列表</span>
          </el-col>
          <el-col :span="7">
            <el-select v-model="aimodel">
              <el-option v-for="item in getAimodelOptions" :key="item.value" :label="item.label"
                         :value="item.value"/>
            </el-select>
          </el-col>
          <el-col :span="6" v-if="getAimodelOptions[aimodel].responseFormat">
            <el-switch v-model="responseFormat" active-text="结构化输出"/>
          </el-col>
          <el-col :span="7" v-if="!getAimodelOptions[aimodel].vision">
            <el-select v-model="ocrType">
              <el-option v-for="item in ocrOptions" :key="item.value" :label="item.label"
                         :value="item.value"/>
            </el-select>
          </el-col>
          <el-col :span="6">
            <!-- <el-switch v-model="showError" active-text="错题展示" @change="onShowErrorChange" /> -->
            <el-switch v-model="colMode" active-text="简单模式"/>
          </el-col>
        </el-row>
      </template>
      <template #extra>
        <div class="header-actions">
          <el-statistic class="header-action header-stats" title="试卷数量" :value="progress.total"/>
          <el-statistic class="header-action header-stats" title="批改中" value-style="color: #67C23A;"
                        :value="progress[3]"/>
          <el-statistic class="header-action header-stats" title="批改成功" value-style="color: #E6A23C;"
                        :value="progress[4]"/>
          <el-statistic class="header-action header-stats" title="批改失败" value-style="color: #F56C6C;"
                        :value="progress[5]"/>
          <el-button class="header-action" type="primary" @click="closeExpand">关闭展开行</el-button>
          <template v-if="progress[3] === 0 && progress[5] === 0">
            <el-button class="header-action" type="primary" icon="Download" :loading="statsLoading"
                       @click="downloadStats">统计结果
            </el-button>
            <el-dropdown class="header-action">
              <el-button type="primary" icon="Download" :loading="previewedPdfLoading"
                         @click="downloadReviewd({ isBatch: true, isPreview: true, showQsScore: previewedPdfOptions.showQsScore }, 'previewedPdfLoading')">
                批改含原卷
              </el-button>
              <template #dropdown>
                <el-form style="margin: 10px">
                  <el-form-item label="显示扣分">
                    <el-switch v-model="previewedPdfOptions.showQsScore" :active-value="true"
                               :inactive-value="false"/>
                  </el-form-item>
                </el-form>
              </template>
            </el-dropdown>
            <el-dropdown class="header-action">
              <el-button class="header-action" type="primary" icon="Download"
                         :loading="reviewedPdfLoading"
                         @click="downloadReviewd({ isBatch: true, showQsScore: reviewedPdfOptions.showQsScore }, 'reviewedPdfLoading')">
                批改不含原卷
              </el-button>
              <template #dropdown>
                <el-form style="margin: 10px">
                  <el-form-item label="显示扣分">
                    <el-switch v-model="reviewedPdfOptions.showQsScore" :active-value="true"
                               :inactive-value="false"/>
                  </el-form-item>
                </el-form>
              </template>
            </el-dropdown>
            <el-button class="header-action" type="primary" icon="Download" :loading="zipDownloading"
                       @click="downloadZip">打包批改结果
            </el-button>
            <!--                        <el-button class="header-action" type="primary" icon="Download" @click="downloadEssayReport">作文批改报告下载</el-button>-->
            <el-button class="header-action" type="primary" icon="FolderOpened"
                       @click="dialogVisible = true, remoteFileSaveCheckList = ['correctionDoesNotIncludeTheOriginalPaper', 'correctionIncludeTheOriginalPaper', 'statisticalResults']">
              保存到远程文件夹
            </el-button>
            <el-button class="header-action" type="primary" icon="Printer"
                       @click="$refs.remoteFilePrintSelectorRef.show()">远程打印
            </el-button>
          </template>
          <el-upload class="header-action" accept=".pdf" multiple :action="$fileserver.uploadUrl"
                     :with-credentials="true" :show-file-list="false" :on-success="uploadDoc">
            <el-button type="primary" icon="Upload">上传</el-button>
          </el-upload>
          <el-button class="header-action" type="primary" @click="doCorrect()">批改</el-button>
        </div>
      </template>
    </el-page-header>
    <div class="main-content">
      <el-table ref="multipleTable" v-loading="recordsLoading" :data="records" style="height: 100%;overflow-y: auto;"
                :border="false" empty-text="无数据"
                @expand-change="onExpandChange" :expand-row-keys="expandRows" row-key="id" class="my-custom-table">
        <el-table-column type="expand" fixed="left">
          <template #default="props">
            <div style="padding: 0 20px;">
              <reviews ref="reviewsComponent" :showError="showError" :record="props.row" :task="task" :config="config"
                       :colMode="colMode"
                       :showAll="allQsIds.includes(props.row.id)" @onUpdate="onQsUpdated" @showJsonDialog="showJsonDialog"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="index" label="序号" width="100" align="center" fixed="left"/>
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center" :fixed="column.prop === 'operations' ? 'right' : ''">
          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-button type="primary" size="small" @click="doEdit(scope.row)">编辑</el-button>
            <el-dropdown style="margin-top: 5px">
                  <span>
                    <el-icon class="el-icon--right">
                      <more/>
                    </el-icon>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="scope.row.status === 3" :loading="resultSaving[scope.$index]"
                                    @click="saveResult(scope.row.id, scope.$index)">保存结果
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status === 4" @click="downloadReviewd({ id: scope.row.id, name: scope.row.docname.replace('.pdf', '') })">下载</el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status === 4" @click="downloadReviewd({ id: scope.row.id, name: scope.row.docname.replace('.pdf', ''), isPreview: true })">预览</el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status === 4" @click="saveResult(scope.row.id)">保存</el-dropdown-item>
                  <el-dropdown-item v-if="[1, 4, 5].includes(scope.row.status)" @click="doCorrect(scope.row.id)" divided>批改</el-dropdown-item>
                  <el-dropdown-item v-if="showError && !allQsIds.includes(scope.row.id)" @click="showAllQs(scope.row.id)">所有题目</el-dropdown-item>
                  <el-dropdown-item v-if="showError && allQsIds.includes(scope.row.id)" @click="showErrorQs(scope.row.id)">所有错题</el-dropdown-item>
                  <el-dropdown-item @click="doDelete(scope.row.id)" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag :type="statusOptions[scope.row.status].type">{{
                statusOptions[scope.row.status].label
              }}
            </el-tag>
          </template>
          <template v-else-if="column.prop === 'docname'" v-slot="scope">
            <el-link type="primary" :href="$fileserver.fileurl(scope.row.docurl)" target="_blank">
              {{ scope.row.docname }}
            </el-link>
          </template>
          <template v-else-if="column.prop === 'progress'" v-slot="scope">
            <el-text v-if="scope.row.status === 3">{{
                `${scope.row.areaCorrected || 0}/${scope.row.areaNum
                || 0}`
              }}
            </el-text>
          </template>
          <template v-else-if="column.prop === 'score'" v-slot="scope">
            <el-text v-if="scope.row.status === 4 && config.configObj.score">{{
                calcScore(scope.row)
              }}
            </el-text>
          </template>
          <template v-else-if="column.prop === 'hasChange'" v-slot="scope">
            <template v-if="[4,5].includes(scope.row.status)">
              <el-tag v-if="scope.row.hasChange === 1" type="success">已修改</el-tag>
              <el-tag v-else>未修改</el-tag>
            </template>
          </template>
          <template v-else-if="column.prop === 'identify'" v-slot="scope">
            <el-tooltip v-if="scope.row?.reviewedObj[0]?.reviewed[0]?.isEssay" placement="top"
                        :content="scope.row?.reviewedObj[0]?.reviewed[0]?.studentName">
              <el-text class="truncate-10">
                {{ scope.row?.reviewedObj[0]?.reviewed[0]?.studentName }}
              </el-text>
            </el-tooltip>
            <el-tooltip v-else placement="top" :content="scope.row?.identify || '无' ">
              <el-text class="truncate-10">{{ scope.row?.identify || '无' }}</el-text>
            </el-tooltip>

          </template>
          <template v-else-if="column.prop === 'studentNumber'" v-slot="scope">
            <el-tooltip placement="top" :content="scope.row?.studentNumber || '无' ">
              <el-text class="truncate-20">{{ scope.row?.studentNumber || '无' }}</el-text>
            </el-tooltip>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <el-dialog
        v-model="dialogVisible"
        title="选择需要保存到远程文件夹的类型"
        width="500"
    >
      <div>
        <el-switch v-model="dontNeedSaveFile.switchChoice" active-text="本地不下载"
                   style="margin-left: 10px"></el-switch>
        <el-form style="margin: 10px">
          <el-form-item label="批改含原卷显示扣分"
                        v-if="remoteFileSaveCheckList.indexOf('correctionIncludeTheOriginalPaper') !== -1">
            <el-switch v-model="previewedPdfOptions.showQsScore" :active-value="true"
                       :inactive-value="false" style="margin-left: 13px"/>
          </el-form-item>

          <el-form-item label="批改不含原卷显示扣分"
                        v-if="remoteFileSaveCheckList.indexOf('correctionDoesNotIncludeTheOriginalPaper') !== -1">
            <el-switch v-model="reviewedPdfOptions.showQsScore" :active-value="true"
                       :inactive-value="false"/>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-checkbox-group v-model="remoteFileSaveCheckList">
          <el-checkbox label="统计结果" value="statisticalResults"></el-checkbox>
          <el-checkbox label="批改含原卷" value="correctionIncludeTheOriginalPaper"></el-checkbox>
          <el-checkbox label="批改不含原卷" value="correctionDoesNotIncludeTheOriginalPaper"></el-checkbox>
        </el-checkbox-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="save2Remote">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <record-form ref="recordForm" @onClose="loadData"/>
    <file-print-selector ref="remoteFilePrintSelectorRef"/>
    <el-dialog v-model="jsonDialogVisible" title="查看 JSON 数据" width="800px" style="z-index: 99999;">
      <json-viewer :value="currentJson"/>
      <template #footer>
        <el-button @click="jsonDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import Reviews from './components/reviews.vue'
import RecordForm from './components/recordForm.vue';
import {ElLoading} from 'element-plus';
import FilePrintSelector from "@/views/common/FilePrintSelector.vue";
import {useUserStore} from "@/store";
import {mapState} from "pinia";
import {JsonViewer} from "vue3-json-viewer";
const store = useUserStore();
import "vue3-json-viewer/dist/index.css";

export default {
  components: {JsonViewer, Reviews, RecordForm, FilePrintSelector},
  data() {
    return {
      records: [],
      columns: [
        {
          prop: "docname",
          label: "试卷名称",
        },
        {
          prop: "score",
          label: "分数",
        },
        {
          prop: "identify",
          label: "姓名",
        },
        {
          prop: "studentNumber",
          label: "学号",
        },
        {
          prop: "status",
          label: "状态",
          width: 200,
        },
        {
          prop: "hasChange",
          label: "是否修改",
        },
        {
          prop: "progress",
          label: "进度",
          width: 200,
        },
        {
          prop: "operations",
          label: "操作",
          width: 120,
        }
      ],
      taskId: this.$route.params.taskId,
      statusOptions: {
        1: {value: 1, label: "未批改",},
        2: {value: 2, label: "待批改", type: "warning"},
        3: {value: 3, label: "批改中", type: "warning"},
        4: {value: 4, label: "已批改", type: "success"},
        5: {value: 5, label: "处理异常", type: "danger"},
      },
      ocrOptions: {
        "1": {value: "1", label: "MathPix OCR"},
        "2": {value: "2", label: "Tencent OCR"},
        "3": {value: "3", label: "gpt-4o"},
      },
      aimodel: store.getDefaultCorrectModal,
      ocrType: "2",
      responseFormat: true,
      timer: null,
      progress: {
        total: 0,
        finished: 0,
      },
      previewedPdfLoading: false,
      reviewedPdfLoading: false,
      task: null,
      config: null,
      expandRows: [],
      loadings: {},
      statsLoading: false,
      showError: false,
      allQsIds: [],
      previewedPdfOptions: {
        showQsScore: true,
      },
      reviewedPdfOptions: {
        showQsScore: true,
      },
      colMode: true,
      taskLength: 0,
      loadingInstance: null,
      zipDownloading: false,
      dialogVisible: false,
      remoteFileSaveCheckList: [],
      dontNeedSaveFile: {
        statisticalResults: false,
        correctionIncludeTheOriginalPaper: false,
        correctionDoesNotIncludeTheOriginalPaper: false,
        switchChoice: true
      },
      jsonDialogVisible: false,
      currentJson: null,
    }
  },
  created() {
    this.loadTask()
        .then(res => this.loadConfig())
        .then(res => this.loadData())
  },
  watch: {
    'records.length': {
      handler(val) {
        const loadings = this.loadings
        const ids = this.records.map(r => r.id)
        for (const id in loadings) {
          if (!ids.includes(id)) {
            delete loadings[id]
          }
        }
        ids.forEach(id => {
          if (!loadings[id]) {
            loadings[id] = {download: false, preview: false}
          }
        })
        this.loadings = loadings
      }
    }
  },
  computed: {
    ...mapState(useUserStore, ['getAimodelOptions']),
  },
  methods: {
    showJsonDialog(data) {
      this.currentJson = data;
      this.jsonDialogVisible = true;
    },
    downloadEssayReport() {
      let loadingMessage = this.$message({
        message: "正在生成批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectRecord/download/essayReport", {
        taskId: this.task.id
      }).then(res => {
        return this.downloadFile(res.data.fileUrl, `${this.task.name} (作文报告).pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
    save2Remote() {
      this.dialogVisible = false;
      this.remoteFileSaveCheckList.forEach(item => {
        if (item === 'statisticalResults') {
          this.dontNeedSaveFile.statisticalResults = this.dontNeedSaveFile.switchChoice;
          this.downloadStats({
            remoteFileProps: {
              save: true
            }
          });
        } else if (item === 'correctionIncludeTheOriginalPaper') {
          this.dontNeedSaveFile.correctionIncludeTheOriginalPaper = this.dontNeedSaveFile.switchChoice;
          this.downloadReviewd({
            remoteFileProps: {
              save: true
            },
            isBatch: true,
            isPreview: true,
            showQsScore: this.previewedPdfOptions.showQsScore
          });
        } else if (item === 'correctionDoesNotIncludeTheOriginalPaper') {
          this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper = this.dontNeedSaveFile.switchChoice;
          this.downloadReviewd({
            isPreview: false,
            remoteFileProps: {
              save: true
            },
            isBatch: true,
            showQsScore: this.reviewedPdfOptions.showQsScore
          });
        }
      })
    },
    onBack() {
      this.$router.back()
    },
    loadData() {
      let expandRows = this.expandRows
      this.recordsLoading = false
      this.$axios.post("/api/docCorrectRecord/page", {
        taskId: this.taskId,
        page: {
          searchCount: false,
          pageSize: -1,
        }
      }).then(res => {
        this.records = res.data.records
        this.calcProgress()
        this.$nextTick(() => {
          this.expandRows = expandRows
        })
      }).finally(() => {
        this.recordsLoading = false;
        this.loadingInstance.close()
      })
    },
    uploadDoc(response, file, fileList) {
      let form = {
        taskId: this.taskId,
        docname: file.name,
        docurl: response.data.url,
        status: 1,
      }
      this.$axios.post("/api/docCorrectRecord/add", form).then(res => {
        this.loadData()
      })
    },
    doDelete(id) {
      this.$confirm("确认删除该记录吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$axios.post(`/api/docCorrectRecord/delete?id=${id}`).then(res => {
          this.$message.success("删除成功")
          this.loadData()
        })
      })
    },
    doCorrect(recordId) {
      if (this.records.length === 0) {
        this.$message.error("请先上传试卷")
        return
      }

      let form = {
        id: this.taskId,
        // 获取其modal
        aimodel: this.aimodel,
        ocrType: this.ocrType,
        responseFormat: this.getAimodelOptions[this.aimodel].responseFormat ? this.responseFormat : false,
      }
      if (recordId) {
        form.recordIds = [recordId]
      }
      this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
        this.$message.success("提交成功")
        this.loadData()
      })
    },
    refreshRecords() {
      if (!this.timer) {
        this.timer = setInterval(() => {
          this.loadData()
        }, 10 * 1000);
      }
    },
    calcProgress() {
      let stats = {
        total: this.records.length,
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0,
      }
      this.records.forEach(record => {
        stats[record.status] += 1
      })
      this.progress = stats
      if (this.progress[3] === 0) {
        clearInterval(this.timer)
        this.timer = null
      } else {
        this.refreshRecords()
      }
    },
    downloadReviewd(options, loading) {
      let form = Object.assign({
        taskId: this.taskId,
        isBatch: false,
        isPreview: false,
      }, options)
      let op = form.isPreview ? 'preview' : 'download'
      let loadingMessage = null
      if (loading) {
        this[loading] = true
      }
      if (!options.isBatch) {
        this.loadings[options.id][op] = true
      } else {
        loadingMessage = this.$message({
          message: "正在生成批改结果...",
          icon: "Loading",
          type: "warning",
          duration: 0,
        })
      }
      this.$axios.post("/api/docCorrectRecord/download/reviewed", form).then(res => {
        if (options && options.isPreview && this.dontNeedSaveFile.correctionIncludeTheOriginalPaper) {
          this.dontNeedSaveFile.correctionIncludeTheOriginalPaper = false;
          this.$message.success("保存成功")
          return;
        } else if (options && !options.isPreview && this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper) {
          this.dontNeedSaveFile.correctionDoesNotIncludeTheOriginalPaper = false;
          this.$message.success("保存成功")
          return;
        }
        this.downloadFile(res.data.fileUrl, `${options.isBatch ? this.task.name : options.name} 批改结果(${form.isPreview ? '含原卷' : '不含原卷'}).pdf`).then(() => {
          this.$message.success("批改结果生成成功!")
        })
      }).finally(() => {
        if (loading) {
          this[loading] = false
        }
        if (!options.isBatch) {
          this.loadings[options.id][op] = false
        } else {
          loadingMessage.close()
        }
      })
    },
    downloadFile(url, name) {
      // 使用fetch获取文件内容
      return fetch(this.$fileserver.fileurl(url))
          .then(response => response.blob())
          .then(blob => {
            // 如果需要下载，可以使用前面提到的下载代码
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = URL.createObjectURL(blob);
            a.download = name;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(a.href);
          })
          .catch(error => {
            console.error('发生错误:', error);
          });
    },
    loadTask() {
      this.loadingInstance = ElLoading.service();
      return this.$axios.get(`/api/docCorrectTask/get/${this.taskId}`).then(res => {
        this.task = res.data
      })
    },
    loadConfig() {
      if (this.task && this.task.configId) {
        return this.$axios.get(`/api/docCorrectConfig/get?id=${this.task.configId}`).then(res => {
          this.config = Object.assign({
            configObj: JSON.parse(res.data.config),
            areasObj: JSON.parse(res.data.areas)
          }, res.data)
          let taskLength = 0;
          if (this.config && this.config.areasObj) {
            this.config.areasObj.forEach((area, areaIdx) => {
              taskLength += area.questions.length;
            })
          } else {
            taskLength = 0;
          }

          this.taskLength = taskLength;
        })
      }
    },
    onExpandChange(row, expandRows) {
      // if (expandRows.length > 1) {
      //   this.$nextTick(() => {
      //     const tableBodyWrapper = this.$refs.multipleTable.$el.querySelector('.my-custom-table .el-table__body-wrapper');
      //     const tableRows = tableBodyWrapper.querySelectorAll('.el-table__body tbody .el-table__row');
      //     let scrollTop = 0;
      //     const records = this.records;
      //     let dataIndex = records.findIndex(record => record.id === row.id);
      //     let rowSum = 0;
      //     let taskLength = this.taskLength;
      //     // 额外所展开的行高度
      //     let offsetHeight = 0;
      //     for(let i = 0; i < expandRows.length; i++) {
      //       let index = records.findIndex(record => record.id === expandRows[i].id);
      //       if (index < dataIndex) {
      //         rowSum += taskLength;
      //         offsetHeight += 41;
      //       }
      //     }
      //     let topIndex = rowSum + dataIndex;
      //     for (let i = 0; i < tableRows.length && i < topIndex; i++) {
      //       scrollTop += tableRows[i].offsetHeight;
      //     }
      //     this.$refs.multipleTable.setScrollTop( scrollTop + offsetHeight)
      //   });
      //   if (expandRows.length > 5)
      //     expandRows.shift();
      // }
      // 更新 `expandRows` 数据以控制展开状态
      this.expandRows = expandRows.map(r => r.id);
    },
    downloadStats(options) {
      this.statsLoading = true
      let loadingMessage = this.$message({
        message: "正在生成统计结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      let form = {
        taskIds: [this.task.id],
      }
      Object.assign(form, options);
      this.$axios.post("/api/docCorrectTask/download/stats", form).then(res => {
        if (this.dontNeedSaveFile.statisticalResults) {
          this.dontNeedSaveFile.statisticalResults = false;
          this.$message.success("统计结果保存成功!");
          return;
        }
        this.downloadFile(res.data.fileUrl, `${this.task.name} 统计结果.pdf`).then(() => {
          this.$message.success("统计结果生成成功!")
        })
      }).finally(() => {
        this.statsLoading = false
        loadingMessage.close()
      })
    },
    onShowErrorChange(val) {
      if (val) {
        this.expandRows = this.records.map(r => r.id)
      } else {
        this.expandRows = []
        this.allQsIds = []
      }
    },
    calcScore(record) {
      let scored = 0
      let total = 0
      let addScored = 0
      let addTotal = 0
      let addName = this.config.configObj.additionalName || '附加'
      this.config.areasObj.forEach((area, areaIdx) => {
        if (area.enabled) {
          area.questions.forEach((qs, qsIdx) => {
            let isAdd = (qs.isAdditional || 1) != 1;
            if (qs.isScorePoint === 2) {
              // 按照得分点给分
              scored += parseInt(record.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.scored || 0)
            } else {
              let isTrue = (record.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.isCorrect || 'Y') === 'Y'
              if (isTrue) {
                scored += isAdd ? 0 : qs.score
                addScored += isAdd ? qs.score : 0
              }
            }
            total += isAdd ? 0 : qs.score
            addTotal += isAdd ? qs.score : 0
          })
        }
      })

      if (addTotal > 0) {
        return `总分: ${scored}/${total}  ${addName}: ${addScored}/${addTotal}`
      } else {
        return `${scored}/${total}`
      }
    },
    closeExpand() {
      this.expandRows = []
    },
    showAllQs(id) {
      this.allQsIds.push(id)
    },
    showErrorQs(id) {
      this.allQsIds = this.allQsIds.filter(i => i !== id)
    },
    onQsUpdated(updated) {
      let index = this.records.findIndex(r => r.id = updated.id)
      this.records[index] = Object.assign(this.records[index], updated)
    },
    doEdit(record) {
      this.$refs.recordForm.show(record)
    },
    saveResult(recordId) {
      this.loadings[recordId].save = true
      this.$axios.post(`/api/docCorrectResult/saveRecord?recordId=${recordId}`).then(res => {
        this.$message.success("保存成功")
      }).finally(() => {
        this.loadings[recordId].save = false
      })
    },
    downloadZip() {
      this.zipDownloading = true
      let loadingMessage = this.$message({
        message: "正在打包下载批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/file/zip", {
        taskIds: [this.taskId],
        name: this.task.name,
        scoreMerge: false,
      }).then(res => {
        this.downloadFile(res.data.fileUrl, `${this.task.name} 批改结果.zip`).then(() => {
          this.$message.success("批改结果生成成功!")
        })
      }).finally(() => {
        this.zipDownloading = false
        loadingMessage.close()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.truncate-10 {
  display: inline-block; /* 或者 inline-flex 根据你的布局 */
  max-width: 10ch; /* 最多显示 10 个 "字符宽度" */
  white-space: nowrap; /* 不自动换行 */
  overflow: hidden; /* 隐藏超出的内容 */
  text-overflow: ellipsis; /* 溢出时显示省略号 */
  vertical-align: bottom; /* 根据需要调整对齐 */
}

.truncate-20 {
  display: inline-block; /* 或者 inline-flex 根据你的布局 */
  max-width: 12ch; /* 最多显示 10 个 "字符宽度" */
  white-space: nowrap; /* 不自动换行 */
  overflow: hidden; /* 隐藏超出的内容 */
  text-overflow: ellipsis; /* 溢出时显示省略号 */
  vertical-align: bottom; /* 根据需要调整对齐 */
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    margin-bottom: 20px;

    .header-title {
      font-weight: 700;
      font-size: 20px;
    }

    .header-actions {
      display: flex;
      align-items: center;

      .header-action {
        margin-right: 10px;

        &.header-stats {
          width: 60px;
          text-align: center;
        }
      }

      .el-button + .el-button {
        margin-left: 0;
      }
    }
  }

  .main-content {
    height: calc(100% - 60px);

    .my-custom-table {
      width: 100%;
    }
  }

}
</style>