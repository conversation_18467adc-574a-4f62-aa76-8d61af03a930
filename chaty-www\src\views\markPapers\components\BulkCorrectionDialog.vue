<template>
  <el-dialog v-model="isShow" :title="title" width="400" :before-close="onClose">
    <el-form :model="formData" label-position="top" ref="bulkCorrectionForm">
      <el-form-item label="题目序号" prop="questionIndex">
        <el-input-number
            v-model="formData.questionIndex"
            :min="1"
            :step="1"
            style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="是否纠错已经修改的" prop="correctModified">
        <el-switch v-model="formData.correctModified" />
      </el-form-item>
      <el-form-item label="都置为正确/错误" prop="setAllCorrect">
        <el-radio-group v-model="formData.setAllCorrect">
          <el-radio :label="true">都置为正确</el-radio>
          <el-radio :label="false">都置为错误</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button type="primary" @click="onSubmit" :loading="submitting">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "BulkCorrectionDialog",
  props: {
    submitting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      title: "批量纠错题目",
      isShow: false,
      formData: {
        questionIndex: 1,
        correctModified: false,
        taskId: "",
        setAllCorrect: true
      }
    }
  },
  methods: {
    show(taskId) {
      this.formData.taskId = taskId
      this.isShow = true
    },
    onClose() {
      if (this.$refs.bulkCorrectionForm) {
        this.$refs.bulkCorrectionForm.resetFields()
      }
      this.isShow = false
    },
    onSubmit() {
      this.$emit("submit", {...this.formData})
      this.onClose()
    }
  }
}
</script>

<style scoped>
</style>
