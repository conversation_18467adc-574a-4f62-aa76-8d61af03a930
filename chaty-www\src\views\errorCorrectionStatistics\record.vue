<template>
  <div class="main-wrapper">
    <div class="main-content">
      <el-table v-loading="loadingRecord || records.length === 0" :key="tableKey" :data="records" style="height: 100%;overflow-y: auto;" border
                empty-text="无数据 | 加载中"
                @expand-change="onExpandChange" :expand-row-keys="expandRows" row-key="id" class="my-custom-table"
                :row-class-name="getRowClassName">
        <el-table-column type="expand">
          <template #default="props">
            <div style="padding: 0 20px;">
              <reviews :ref="props.row.id"
                       :data="props.row.revirewData"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center">
          <template v-if="column.prop === 'status'" v-slot="scope">
            <el-tag v-if="!haveCorrectError(scope.row.reviewedObj, scope.row.status)"
                    :type="statusOptions[scope.row.status].type">{{
                statusOptions[scope.row.status].label
              }}
            </el-tag>
            <el-tag v-else type="danger">批改失败</el-tag>
          </template>

          <template v-else-if="column.prop === 'progress'" v-slot="scope">
            <el-text v-if="scope.row.status === 3">{{
                `${scope.row.areaCorrected || 0}/${scope.row.areaNum
                || 0}`
              }}
            </el-text>
          </template>
          <template v-else-if="column.prop === 'hasChange'" v-slot="scope">
            <template v-if="[4,5].includes(scope.row.status)">
              <el-tag v-if="scope.row.hasChange === 1" type="success">已修改</el-tag>
              <el-tag v-else>未修改</el-tag>
            </template>
          </template>
          <template v-else-if="column.prop === 'identify'" v-slot="scope">
            <el-tooltip v-if="scope.row?.reviewedObj[0]?.reviewed[0]?.isEssay" placement="top"
                        :content="scope.row?.reviewedObj[0]?.reviewed[0]?.studentName">
              <el-text class="truncate-10">
                {{ scope.row?.reviewedObj[0]?.reviewed[0]?.studentName }}
              </el-text>
            </el-tooltip>
            <el-tooltip v-else placement="top" :content="scope.row?.identify || '无' ">
              <el-text class="truncate-10">{{ scope.row?.identify || '无' }}</el-text>
            </el-tooltip>

          </template>
          <template v-else-if="column.prop === 'studentNumber'" v-slot="scope">
            <el-tooltip placement="top" :content="scope.row?.studentNumber || '无' ">
              <el-text class="truncate-20">{{ scope.row?.studentNumber || '无' }}</el-text>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import Reviews from './reviews.vue'

export default {
  components: {Reviews},
  props: {
    records: {
      type: Array,
      default: []
    },
    configs: {
      type: Object,
      default: {}
    },
    loadingRecord: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tableKey: 0,
      columns: [
        {
          prop: "docname",
          label: "试卷名称",
          width: 350
        },
        {
          prop: "hasChangeCount",
          label: "已修改题目数量",
          width: 85,
        },
        {
          prop: "status",
          label: "状态",
          width: 85,
        },
        {
          prop: "hasChange",
          label: "是否修改",
          width: 85
        }
      ],
      statusOptions: {
        1: {value: 1, label: "未批改", type: "primary"},
        2: {value: 2, label: "待批改", type: "warning"},
        3: {value: 3, label: "批改中", type: "warning"},
        4: {value: 4, label: "已批改", type: "success"},
        5: {value: 5, label: "处理异常", type: "danger"},
      },
      ocrOptions: {
        "1": {value: "1", label: "MathPix OCR"},
        "2": {value: "2", label: "Tencent OCR"},
        "3": {value: "3", label: "gpt-4o"},
      },
      ocrType: "2",
      responseFormat: true,
      timer: null,
      progress: {
        total: 0,
        finished: 0,
      },
      previewedPdfLoading: false,
      reviewedPdfLoading: false,
      task: null,
      expandRows: [],
      statsLoading: false,
      previewedPdfOptions: {
        showQsScore: true,
      },
      reviewedPdfOptions: {
        showQsScore: true,
      },
      taskLength: 0,
      zipDownloading: false
    }
  },
  created() {

  },
  watch: {
    'selectRowIndex': {
      handler(val, oldVal) {
        if (this.records.length !== 0) {
          if (val && this.isTaskSelected) {
            let id = this.records[val].id
            if (this.expandRows.indexOf(id) === -1) {
              this.expandRows.push(id)
            }
          }
        }

      }
    }
  },
  methods: {
    haveCorrectError(e, paperStatus) {
      if (paperStatus !== '4') return false;
      for (let i = 0; i < e.length; i++) {
        if (e[i].status === '2') return true;
      }
      return false;
    },
    getRowClassName({row, rowIndex}) {
      return rowIndex === this.selectRowIndex && this.isTaskSelected ? 'highlight-row' : '';
    },
    refresh() {
      this.$forceUpdate()
      this.$emit('refresh')
    },
    uploadDoc(response, file, fileList) {
      let form = {
        taskId: this.taskId,
        docname: file.name,
        docurl: response.data.url,
        status: 1,
      }
      this.$axios.post("/api/docCorrectRecord/add", form).then(res => {
        this.refresh()
      })
    },
    onExpandChange(row, expandRows) {
      // 判断是不是新的
      if (this.expandRows.indexOf(row.id) === -1) {
        this.$emit('show', this.records.indexOf(row));
        // this.downloadReviewd({id: row.id, docurl: row.docurl, taskId: this.taskId, paperIdx: })
      }
      this.expandRows = expandRows.map(r => r.id);
    },
    expandAll() {
      this.expandRows = this.records.map(row => row.id);
    },
    collapseAll() {
      this.expandRows = [];
    }
  }
}
</script>
<style>
.truncate-10 {
  display: inline-block; /* 或者 inline-flex 根据你的布局 */
  max-width: 10ch; /* 最多显示 10 个 “字符宽度” */
  white-space: nowrap; /* 不自动换行 */
  overflow: hidden; /* 隐藏超出的内容 */
  text-overflow: ellipsis; /* 溢出时显示省略号 */
  vertical-align: bottom; /* 根据需要调整对齐 */
}

.truncate-20 {
  display: inline-block; /* 或者 inline-flex 根据你的布局 */
  max-width: 12ch; /* 最多显示 10 个 “字符宽度” */
  white-space: nowrap; /* 不自动换行 */
  overflow: hidden; /* 隐藏超出的内容 */
  text-overflow: ellipsis; /* 溢出时显示省略号 */
  vertical-align: bottom; /* 根据需要调整对齐 */
}


.highlight-row {
  background: #e6f7ff !important;
}
</style>
<style lang="scss" scoped>


.el-table {
  table-layout: fixed;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .main-content {
    height: 100%;
    width: 100%;
    .my-custom-table {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      overflow-x: auto;
    }
  }

}
</style>