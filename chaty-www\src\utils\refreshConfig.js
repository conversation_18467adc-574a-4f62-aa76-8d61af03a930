import axios from '@/http'
import { useUserStore } from '@/store'   // ← your Pinia store

export function refreshConfig() {
    const store = useUserStore()           // ← now this runs at call-time
    return axios.get('/api/defaultConfigs/getAll')
        .then(res => {
            store.setDefaultConfigs(res.data.prompts)
            store.setDefaultCorrectModal(res.data.defaultCorrectModal)
            store.setAimodelOptions(res.data.aimodelOptions)
            console.log('Configs saved')
        })
}
