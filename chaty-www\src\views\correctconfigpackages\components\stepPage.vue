<template>
  <div style="width: 100%;height: 100%">
    <upload-file @finishUpload="finishUpload" ref="uploadFile" v-if="activeStep === 1"></upload-file>
    <file-task @setActiveStep="setActiveStep" v-else></file-task>
  </div>
</template>

<script>
import uploadFile from "./uploadFile.vue";
import FileTask from './filetask.vue'

export default {
  components: {
    uploadFile,
    FileTask
  },
  data() {
    return {
      activeStep: 1,
      id: null,
      isExpand: false
    }
  },
  created() {
    this.id = this.$route.params.id;
    if (this.id && this.id !== '0') {
      this.isExpand = true;
      this.activeStep = 2;
    } else {
      this.activeStep = 1;
    }
  },
  methods: {
    init() {
      this.activeStep = 1;
      this.id = null;
    },
    finishUpload(id) {
      this.activeStep = 2;
      if (id) {
        this.$router.push( '/correctConfigPackages/stepConfigPapers/' + id);
      }
    },
    setActiveStep(step) {
      this.activeStep = step;
    },
    submit() {
      if (this.activeStep === 1) {
        this.$refs.uploadFile.onSubmit();
      } else if (this.activeStep === 2) {
      } else {

      }
    },
    resetFields() {
      if (this.activeStep === 1) {
        this.$refs.uploadFile.onClose();
      }
      this.init();
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small) {
  margin-bottom: 5px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}
:deep(.el-step__title) {
  font-weight: bold;
}
</style>