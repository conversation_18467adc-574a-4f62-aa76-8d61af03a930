<template>
  <el-dialog
      v-model="dialogVisible"
      title="导入学生名单"
      :before-close="handleClose"
      width="700px"
      :close-on-click-modal="false"
  >
    <el-steps :active="activeStep" direction="vertical" finish-status="success" space="100px">
      <el-step
          title="按照模板格式填写需要导入的学生名单"
      >
        <template #description>
          <el-button type="primary" @click="downloadTemplate" icon="Download" class="download-button">
            <el-text class="text">下载Excel模板</el-text>
          </el-button>
        </template>

      </el-step>
      <el-step
          title="选择Excel文件上传"
      >
        <template #description>
          <el-upload
              class="upload-demo"
              :before-upload="beforeUpload"
              :on-change="handleFileChange"
              :show-file-list="false"
              accept=".csv,.xlsx,.xls"
          >
            <el-button type="primary" icon="Upload" style="background: #3981FF;color: #FFFFFF;">
              <el-text style="font-weight: normal;color: #FFFFFF;">上传Excel文件</el-text>
            </el-button>
            <span v-if="form.fileName" class="file-name">{{ form.fileName }}</span>
          </el-upload>
        </template>
      </el-step>
    </el-steps>
    <el-text class="warning-text">
      1、为保证数据顺利导入，推荐使用标准格式规范上传表格文件<br>
      2、支持上传最大10MB以内的Excel表格(xls、xlsx)<br>
      3、导入数据最大不能超过1000行，2列
    </el-text>
    <span slot="footer" class="dialog-footer">
      <el-button class="button" type="primary" :loading="loading" @click="submitForm">
        导入
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {mapState} from "pinia";
import {useUserStore} from "@/store";
import {ElMessage} from "element-plus";

export default {
  name: "ImportStudentListDialog",
  data() {
    return {
      dialogVisible: false,
      form: {
        file: null,
        fileName: ""
      },
      loading: false,
      classId: null,
      activeStep: 1
    };
  },
  methods: {
    openDialog(classId) {
      this.classId = classId;
      this.dialogVisible = true;
      this.resetForm();
      this.activeStep = 0;
    },
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
      this.activeStep = 0;
      this.classId = null;
      this.$emit('cancel')
    },
    resetForm() {
      this.form.file = null;
      this.form.fileName = "";
    },
    downloadTemplate() {
      this.activeStep = 1;
      return fetch(this.$fileserver.fileurl('/static/importStudentsTemplate.xlsx'))
          .then(response => response.blob())
          .then(blob => {
            // 如果需要下载，可以使用前面提到的下载代码
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = URL.createObjectURL(blob);
            a.download = '导入学生名单模板.xlsx';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(a.href);
          })
          .catch(error => {
            console.error('发生错误:', error);
          });
    },
    beforeUpload(file) {
      const isCSV = file.type === "text/csv";
      const isExcel =
          file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
          file.type === "application/vnd.ms-excel";
      if (!isCSV && !isExcel) {
        ElMessage.error("只能上传 CSV 或 Excel 文件");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        ElMessage.error("文件大小必须小于 10MB!");
        return false;
      }
      return true;
    },
    handleFileChange(file, fileList) {
      if (file.raw) {
        this.form.file = file.raw;
        this.form.fileName = file.name;
        this.activeStep = 2;
      }
    },
    submitForm() {
      if (!this.form.file) {
        ElMessage.error("请上传学生名单文件");
        return;
      }
      const formData = new FormData();
      formData.append("file", this.form.file);
      this.loading = true;
      this.$axios
          .post(`/api/user/teacher/import/${this.classId}`, formData, {
            headers: {
              "Content-Type": "multipart/form-data"
            }
          })
          .then((res) => {
            ElMessage.success("学生名单导入成功");
            this.dialogVisible = false;
            this.resetForm();
            this.$emit("fetchStudents");
          })
          .catch((error) => {
            console.error("导入学生名单错误：", error);
            ElMessage.error("网络错误，请稍后再试");
          })
          .finally(() => {
            this.loading = false;
          });
    }
  }
};
</script>

<style scoped>
.download-button {
  background: transparent;
  border: 1px solid #3981FF;
  border-radius: 6px;
  color: #3981FF;
  margin-top: 10px;

  .text {
    font-weight: 400;
    font-size: 14px;
    color: #3981FF;
    text-align: right;
  }
}

.warning-text {
  font-weight: 400;
  font-size: 14px;
  color: #FF3131;
  letter-spacing: 0;
  line-height: 24px;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.button {
  width: 90px;
  height: 32px;
  background: #3981ff;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  margin-right: 10px;
}

.form-container {
  padding: 20px;
}

.el-dialog .el-dialog__footer {
  text-align: center;
}

.el-button[type="primary"] {
  background-color: #3981ff;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
}

.el-upload .el-button {
  margin-top: 10px;
}

.el-input {
  width: 100%;
}

.file-name {
  margin-left: 10px;
  font-size: 14px;
  color: #606266;
}
</style>
