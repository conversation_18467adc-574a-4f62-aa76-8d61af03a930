<template>
  <div>
    <!-- 试卷和试卷包选择 -->
    <el-form-item label="选择试卷" v-if="!isCurrentSet">
      <el-select
          v-model="localSelectedPaperId"
          placeholder="请输入试卷名称 搜索添加"
          clearable
          filterable
          remote
          :remote-method="loadPaperOptions"
          :loading="loadingPaperOption"
          style="width: 100%; margin-bottom: 10px"
      >
        <el-option
            v-for="item in paperOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        />
      </el-select>
      <el-select
          v-model="localSelectedPackageId"
          placeholder="或者输入试卷标准卷名称 搜索添加"
          clearable
          filterable
          remote
          :remote-method="loadPackageOptions"
          :loading="loadingPackageOption"
          style="width: 100%"
      >
        <el-option
            v-for="item in packageOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        />
      </el-select>
    </el-form-item>

    <!-- 已选试卷列表 -->
    <el-form-item label="已选试卷">
      <el-table :data="localFileDetails" border style="width: 100%">
        <el-table-column prop="name" label="试卷名称"/>
        <el-table-column prop="folderName" label="文件夹名称">
          <template #default="{ row }">
            <el-input v-model="row.folderName" :disabled="isCurrentSet"></el-input>
          </template>
        </el-table-column>
<!--        <el-table-column prop="fileId" label="id"/>-->

        <el-table-column label="操作">
          <template #default="{ $index }">
            <el-button type="danger" @click="removePaper($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: "PaperSelector",
  props: {
    initPaper: {
      type: Object,
      default: null
    },
    "showFolderName": {
      type: Boolean,
      default: false
    },
    "isCurrentSet": {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 本组件内部使用的局部变量
      localSelectedPaperId: null,
      localSelectedPackageId: null,
      paperOptions: [],
      packageOptions: [],
      loadingPaperOption: false,
      loadingPackageOption: false,
      // 本地的已选试卷列表，初始值和父组件传入的一致
      localFileDetails: []
    };
  },
  mounted() {
    if (this.initPaper) {
      if (this.isCurrentSet) {
        // 当前套卷模式：只添加当前试卷
        this.addByFileId(this.initPaper.fileId);
      } else {
        // 其他套卷模式：添加所有相关试卷
        this.addByPackageIds(this.initPaper.configIds);
      }
    }
  },
  watch: {
    // 当本地 selectedPaperId 或 selectedPackageId 变化时触发对应操作
    localSelectedPaperId(newVal) {
      if (newVal) {
        this.onPaperSelect();
      }
    },
    localSelectedPackageId(newVal) {
      if (newVal) {
        this.onPackageSelect();
      }
    }
  },
  methods: {
    addByPackageIds(configIds) {
      this.$axios
          .post("/api/docCorrectConfigPackage/page", {
            config: JSON.stringify(configIds),
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            if (res.data.records.length === 0) {
              this.$message.error(`没有找到${this.initPaper.packageName}配置卷`);
            } else if (res.data.records.length === 1) {
              this.localSelectedPackageId = res.data.records[0].id;
            } else {
              this.$message.warning(`找到多个相似名称：${this.initPaper.packageName}配置卷，采用第一个`);
              this.localSelectedPackageId = res.data.records[0].id;
            }
          });
    },
    // 去重并根据 name 排序
    dedupAndSort() {
      const map = new Map();
      this.localFileDetails.forEach(item => {
        map.set(item.fileId, item);
      });
      this.localFileDetails = Array.from(map.values());
      this.localFileDetails.sort((a, b) => a.name.localeCompare(b.name));
    },
    // 加载试卷选项
    loadPaperOptions(query) {
      if (!query) {
        this.paperOptions = [];
        return;
      }
      this.loadingPaperOption = true;
      this.loadPapers(query)
          .then((res) => {
            this.paperOptions = res;
          })
          .finally(() => {
            this.loadingPaperOption = false;
          });
    },

    // 加载试卷包选项
    loadPackageOptions(query) {
      if (!query) {
        this.packageOptions = [];
        return;
      }
      this.loadingPackageOption = true;
      this.loadPackages(query)
          .then((res) => {
            this.packageOptions = res;
          })
          .finally(() => {
            this.loadingPackageOption = false;
          });
    },
    // 接口调用加载试卷
    loadPapers(name) {
      return this.$axios
          .post("/api/docCorrectFile/page", {
            name,
            page: {pageNumber: 1, pageSize: -1}
          })
          .then((res) => res.data.records);
    },
    // 接口调用加载试卷包
    loadPackages(name) {
      return this.$axios
          .post("/api/docCorrectConfigPackage/page", {
            name,
            page: {pageNumber: 1, pageSize: -1}
          })
          .then((res) => res.data.records);
    },
    // 处理试卷选择：将选中试卷添加到已选列表中
    onPaperSelect() {
      const selectedPaper = this.paperOptions.find(
          (paper) => paper.id === this.localSelectedPaperId
      );
      if (selectedPaper) {
        this.localFileDetails.push({
          fileId: selectedPaper.id,
          name: selectedPaper.name,
          folderName: this.getFolderName(selectedPaper.name),
        });
        this.dedupAndSort();
        this.localSelectedPaperId = null;
        this.$message.success("已添加");
      }
    },
    // 处理试卷包选择：获取试卷包中的试卷添加到已选列表中
    onPackageSelect() {
      if (!this.localSelectedPackageId) return;
      this.$axios
          .get(
              `/api/docCorrectFile/getDocCorrectFileListByConfigPackageId?id=${this.localSelectedPackageId}`
          )
          .then((res) => {
            const fileList = res.data;
            if (fileList && fileList.length) {
              fileList.forEach((file) => {
                this.localFileDetails.push({
                  fileId: file.id,
                  name: file.name,
                  folderName: this.getFolderName(file.name),
                });
              });
              this.dedupAndSort();
            }
            this.localSelectedPackageId = null;
          });
    },
    getFolderName(name) {
      if (!name) return '无';
      let nameArr = name.split('_');
      if (nameArr.length === 4) {
        return `${nameArr[0]}_${nameArr[2]}_${nameArr[3]}`;
      }else {
        return name;
      }
    },
    // 删除已选试卷
    removePaper(index) {
      this.localFileDetails.splice(index, 1);
    },
    // 添加通过文件ID加载试卷的方法
    addByFileId(fileId) {
      if (!fileId) return;
      this.$axios
          .get("/api/docCorrectFile/getById?id=" + fileId)
          .then((res) => {
            if (res.data) {
              this.localFileDetails.push({
                fileId: res.data.id,
                name: res.data.name,
                folderName: this.getFolderName(res.data.name)
              });
              this.dedupAndSort();
            }
          });
    }
  }
};
</script>

<style scoped>
/* 根据实际需要添加样式 */
</style>
