<template>
  <div>
    <el-form ref="configForm" :model="config" class="form-config" :rules="configRules" :inline="true">
      <div style="display: flex;justify-content: space-between">
        <div>
          <el-form-item class="form-question" label="已经纠错的试卷" prop="root">
            <el-select style="width: 300px" suffix-icon="Search"
                       placeholder="请输入试卷名称" remote filterable clearable
                       :remote-method="loadRootTaskOptions" :loading="taskRootLoading" @change="onRootTaskSelect"
                       v-model="taskNames">
              <el-option v-for="item in taskRootOptions" :key="item.id" :label="item.name" :value="item"
                         style="width: 350px"/>
            </el-select>
          </el-form-item>

          <el-form-item class="form-question" label="对比试卷" prop="compare" v-if="config.root">
            <el-select style="width: 300px" suffix-icon="Search"
                       placeholder="请输入试卷名称" remote filterable clearable
                       :remote-method="loadCompareTaskOptions" :loading="taskCompareLoading" @change="onCompareTaskSelect"
                       v-model="taskCompareNames">
              <el-option v-for="item in taskCompareOptions" :key="item.id" :label="item.name" :value="item"
                         style="width: 350px"/>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <!-- 新增模型按钮 -->
          <el-button class="button" type="primary" @click="openDialog" style="margin-right: 10px;">新增模型</el-button>
          <el-button class="button" type="primary" @click="startEvaluate" style="margin-right: 10px;">开始评估
          </el-button>
        </div>
      </div>

      <div style="font-size: 12px;align-items: center;display: flex"><el-icon><Warning /></el-icon>默认已经纠错的试卷为根试卷，已经将错题完全纠错，其他对比试卷将以此为正确答案</div>
      <div style="font-size: 12px;align-items: center;display: flex;margin-bottom: 10px"><el-icon><Warning /></el-icon>对比试卷的试卷配置的区域和题目数量需和根试卷的试卷配置需一致</div>
      <!-- 表格内容 -->
      <el-table :data="config.tasks" style="height: 100%" empty-text="无数据" border >
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center">
          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-button v-if="scope.row.status !== 2" type="primary" text size="small"
                       @click="correctTask(scope.row)">批改
            </el-button>
            <el-dropdown style="margin-top: 6px">
                <span>
                  <el-icon class="el-icon--right">
                    <more/>
                  </el-icon>
                </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="scope.row.status === 3" :loading="resultSaving[scope.$index]"
                                    @click="saveResult(scope.row.id, scope.$index)">保存结果
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status !== 2" @click="correctName(scope.row.id)">姓名校正
                  </el-dropdown-item>
                  <el-dropdown-item @click="onEditTask(scope.row)">编辑</el-dropdown-item>
                  <el-dropdown-item @click="deleteTaskFromConfig(scope.row.id)">移除</el-dropdown-item>
                  <el-dropdown-item @click="onDeleteTask(scope.row.id)" divided>彻底删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else-if="column.prop === 'record'" v-slot="scope">
            <el-link type="primary" @click="toRecord(scope.row.id)">查看</el-link>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag :type="statusOptions[scope.row.status].type">{{
                statusOptions[scope.row.status].label
              }}
            </el-tag>
          </template>
          <template v-else-if="column.prop === 'configName'" v-slot="scope">
            <el-link v-if="scope.row.configId" type="primary" @click="toConfig(scope.row.configId)">
              {{ scope.row.configName }}
            </el-link>
          </template>
          <template v-else-if="column.prop === 'progress'" v-slot="scope">
            <el-text v-if="scope.row.status === 2">{{
                `${scope.row.finishedCount || 0}/${scope.row.recordCount || 0}`
              }}
            </el-text>
          </template>
          <template v-else-if="column.prop === 'correctConfig'" v-slot="scope">
            <el-text>{{
                scope.row.correctConfig.aimodel
              }}
            </el-text>
          </template>
          <template v-if="column.prop === 'correctConfigObj.aimodel'" v-slot="scope">
            {{ scope.row.correctConfigObj.aimodel }}
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <!-- 新增模型弹出框 -->
    <el-dialog v-model="dialogVisible" title="模型信息" width="800px">
      <el-form ref="modelForm" :model="form" label-width="100px">
        <!-- 模型选择下拉框 -->
        <el-form-item label="选择模型">
          <el-select v-model="form.model" placeholder="请选择模型">
            <el-option v-for="item in getAimodelOptions" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <!-- 富文本输入框 -->
        <el-form-item label="提示词：">
          <el-input v-model="form.prompt" type="textarea" :autosize="{ minRows: 15, maxRows: 15 }"
                    style="width: 700px"></el-input>
        </el-form-item>
        <el-form-item label="题目识别：">
          <el-input v-model="form.qsOcrPrompt" type="textarea" :autosize="{ minRows: 15, maxRows: 15 }"
                    style="width: 700px"></el-input>
        </el-form-item>
        <el-form-item label="模型对照：">
          <el-select v-model="form.model" placeholder="请选择模型">
            <el-option v-for="item in getAimodelOptions" :label="item.label + ' - ' + item.value"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitModelForm">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {ElLoading} from "element-plus";
import {useUserStore} from "@/store";
import {mapState} from "pinia";

export default {
  props: {
  },
  data() {
    return {
      loadingInstance: null,
      dialogVisible: false,  // 控制弹出框的显示
      form: {               // 表单数据
        model: '',
        prompt: "",
        qsOcrPrompt: ""
      },
      defaultform: {
        model: '',
        prompt: '',
        qsOcrPrompt: '',
      },
      configRules: {
        contrast: [{required: true, message: '请至少选择一个对比试卷', trigger: 'blur'}],
        root: [{required: true, message: '请选择已经纠错的试卷', trigger: 'blur'}],
      },
      statusOptions: {
        1: {value: 1, label: "未批改"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      taskLoading: false,
      taskOptions: [],
      taskNames: '',
      taskCompareNames: '',
      config: {
        root: '',
        tasks: []
      },
      columns: [
        {label: "名称", prop: "name"},
        {label: "试卷配置", prop: "configName"},
        {label: "模型名称", prop: "correctConfigObj.aimodel"},
        {label: "试卷", prop: "record"},
        {label: "状态", prop: "status", width: 200},
        {label: "进度", prop: "progress", width: 100},
        {label: "操作", prop: "operations", width: 100},
      ],
      taskRootLoading: false,
      taskCompareLoading: false,
      taskRootOptions: [],
      taskCompareOptions: [],
      resultSaving: {},
      questionDynamicColumns: [
        {prop: "gpt4o", label: "gpt-4o"},
        {prop: "gpt4o20240806", label: "gpt-4o-2024-08-06"},
        {prop: "gpt4o202408063", label: "gpt-4o-2024-08-06(三倍批改)"},
        {prop: "gpt4oMini", label: "gpt-4o-mini"},
        {prop: "gpt4VisionPreview", label: "gpt-4-vision-preview"},
        {prop: "claude3Opus", label: "Claude 3 Opus"},
        {prop: "claude3Sonnet", label: "Claude 3 Sonnet"},
        {prop: "gpt4Turbo", label: "gpt-4-turbo"},
        {prop: "gpt4", label: "gpt-4"},
        {prop: "gpt35Turbo", label: "gpt-3.5-turbo"},
        {prop: "ernieBot", label: "ERNIE-Bot"},
        {prop: "qwenTurbo", label: "qwen-turbo"},
        {prop: "qwenPlus", label: "qwen-plus"},
        {prop: "qwenMax", label: "qwen-max"},
      ],
      submitLoading: false,
      evaluateData: null
    }
  },
  computed: {
    ...mapState(useUserStore, ['getAimodelOptions']),
  },
  mounted() {
    const store = useUserStore();
    this.defaultform.prompt = JSON.parse(JSON.stringify(store.getDefaultConfigs['DOC_CORRECT_RESPONSE_FORMAT']));
    this.defaultform.qsOcrPrompt = JSON.parse(JSON.stringify(store.getDefaultConfigs['EXTRA_QS_RESPFORMAT_TEMPLETE']));
  },
  methods: {
    closeLoading() {
      if (this.loadingInstance) {
        this.$nextTick(() => {
          this.loadingInstance.close();
        });
      }
    },
    startEvaluate() {

      const task = this.config.tasks;
      const rootPaper = this.config.root;
      if (!task || task.length === 0) {
        this.$message.warning("请先选择对比试卷");
        return;
      }
      if (!rootPaper || !rootPaper.id) {
        this.$message.warning("请先选择已经纠错的试卷");
        return;
      }
      this.loadingInstance = ElLoading.service();
      let noExecute = 0;
      task.forEach((item) => {
        if (item.status !== 3) noExecute++;
        // 获取每个试卷的详细题目信息，计算模型正确率，对错题进行纠正
      });
      if (noExecute) {
        this.$message.warning("还有" + noExecute + "个任务未批改完成或开始，当前开始评估" + (task.length - noExecute) + "个试卷");
      }
      let selectForm = {
        taskId: rootPaper.id,
        page: {
          searchCount: false,
          pageSize: -1,
        }
      }
      let rootPaperRecords = [];


      this.$axios.post("/api/docCorrectRecord/page", selectForm).then(rootPaperRes => {
        rootPaperRecords = rootPaperRes.data.records;
        // 统计root的正确答案
        let rootAnswers = [];
        rootPaperRecords.forEach((record) => {
          let reviewedObjs = record.reviewedObj;
          reviewedObjs.forEach((reviewedObj) => {
            let reviewed = reviewedObj.reviewed;
            reviewed.forEach((q) => {
              rootAnswers.push(q.isCorrect);
            });
          });
        });

        let allWrongQuestionsByModal = [];
        const allPromises = [];
        task.forEach((item) => {
          if (item.status === 3) {
            let form = {
              taskId: item.id,
              page: {
                searchCount: false,
                pageSize: -1,
              }
            };
            let wrongQuestionsByModals = [];

            const promise = this.$axios.post("/api/docCorrectRecord/page", form).then(questionRes => {
              // 计算错题，进行纠正
              let questionRecords = questionRes.data.records;
              let cnt = 0;
              let wrongCnt = 0;
              let questionCnt = 0;
              questionRecords.forEach((question, index) => {
                // 每个学生的一份试卷
                let rootPaperRecord = rootPaperRecords[index];
                let reviewedObjs = question.reviewedObj;
                questionCnt = 0;
                let wrongQuestionsByModal = [];
                reviewedObjs.forEach((reviewedObj, idx) => {
                  // 每个区域
                  let reviewed = reviewedObj.reviewed;
                  reviewed.forEach((q, i) => {
                    // 每个题目
                    if (q.isCorrect !== rootAnswers[questionCnt]) {
                      // 需要纠错
                      wrongQuestionsByModal.push(questionCnt);
                      wrongCnt++;
                      // console.log(questionRes.data.records[0].id);
                      // console.log('第几份试卷，第几个区域，第几个题目', index, idx, i);
                    }
                    questionCnt++;
                    cnt++;

                  })
                });
                wrongQuestionsByModals.push(wrongQuestionsByModal);
              });

              return {
                taskId: item.id,
                wrongQuestionsByModals: wrongQuestionsByModals,
                cnt: cnt,
                paperCnt: questionRecords.length,
                wrongCnt: wrongCnt,
                questionCnt: questionCnt,
                accuracy: (cnt - wrongCnt) / cnt,
                errorRate: wrongCnt / cnt,
                ...item
              };
            });
            allPromises.push(promise);
          }
        });
        const rootPromise = this.$axios.post("/api/docCorrectRecord/page", {
          taskId: this.config.root.id,
          page: {
            searchCount: false,
            pageSize: -1,
          }
        }).then(questionRes => {
          // 计算错题，进行纠正
          let wrongQuestionsByModals = [];
          let item = this.config.root;
          let questionRecords = questionRes.data.records;
          let cnt = 0;
          let wrongCnt = 0;
          let questionCnt = 0;
          questionRecords.forEach((question, index) => {
            // 每个学生的一份试卷
            let rootPaperRecord = rootPaperRecords[index];
            let reviewedObjs = question.reviewedObj;
            questionCnt = 0;
            let wrongQuestionsByModal = [];
            reviewedObjs.forEach((reviewedObj, idx) => {
              // 每个区域
              let reviewed = reviewedObj.reviewed;
              reviewed.forEach((q, i) => {
                // 每个题目
                if (q.hasChange) {
                  wrongQuestionsByModal.push(questionCnt);
                  wrongCnt++;
                }
                questionCnt++;
                cnt++;

              })
            });
            wrongQuestionsByModals.push(wrongQuestionsByModal);
          });

          return {
            taskId: item.id,
            wrongQuestionsByModals: wrongQuestionsByModals,
            cnt: cnt,
            paperCnt: questionRecords.length,
            wrongCnt: wrongCnt,
            questionCnt: questionCnt,
            accuracy: (cnt - wrongCnt) / cnt,
            errorRate: wrongCnt / cnt,
            ...item
          };
        });

        allPromises.push(rootPromise);

        const promiseGetConfig = this.$axios.get("/api/docCorrectConfig/get?id=" + this.config.root.configId).then(getModalRes => {
          let areas = JSON.parse(getModalRes.data.areas);
          let questionAndAnswers = [];
          areas.forEach((area) => {
            area.questions.forEach((question) => {
              questionAndAnswers.push({
                question: question.question,
                answer: question.answer
              })
            });
          });
          return questionAndAnswers;
        });
        allPromises.push(promiseGetConfig);
        // 等待所有 Promise 完成
        Promise.all(allPromises).then((res) => {
          console.log('res', res);
          // 所有请求处理完成后发出事件
          allWrongQuestionsByModal = res;
          this.$emit('startEvaluate', {
            data: allWrongQuestionsByModal.splice(0, allWrongQuestionsByModal.length - 1),
            questionAndAnswers: allWrongQuestionsByModal[allWrongQuestionsByModal.length - 1]
          });
        }).catch(error => {
          this.$message.error("处理过程中发生错误，请重试。");
        });
      });


    },
    openDialog() {
      if (this.config.root != null && this.config.root.id) {
        this.form = JSON.parse(JSON.stringify(this.defaultform));
        this.dialogVisible = true;
      } else {
        this.$message.warning("请先选择已经纠错的试卷");
      }
    },
    closeDialog() {
      this.dialogVisible = false;
      this.form = {model: '', prompt: ''};  // 重置表单
    },
    submitModelForm() {
      let loadingInstance = ElLoading.service();
      let date = new Date();
      let formattedDate = date.getFullYear() + "-" +
          String(date.getMonth() + 1).padStart(2, '0') + "-" +
          String(date.getDate()).padStart(2, '0') + "-" +
          String(date.getHours()).padStart(2, '0') + ":" +
          String(date.getMinutes()).padStart(2, '0') + ":" +
          String(date.getSeconds()).padStart(2, '0');
      let name = this.config.root.name + "_模型评估_" + this.form.model + "_" + formattedDate;

      this.submitLoading = true;
      // 先查询试卷docUrl，先用/add/file创建,再创建config，再改名
      let docCorrectRecordPageForm = {
        taskId: this.config.root.id,
        page: {
          searchCount: false,
          pageSize: -1,
        }
      }
      this.$axios.post("/api/docCorrectRecord/page", docCorrectRecordPageForm).then(docCorrectRecordPageRes => {
        let records = [];
        docCorrectRecordPageRes.data.records.forEach((it, index) => {
          records.push({url: it.docurl});
        })
        let addFileForm = {
          filename: name,
          name,
          docList: [records],
          isCreateConfig: false
        };
        // 通过file创建试卷
        this.$axios.post("/api/docCorrectTask/add/file", addFileForm).then(addFileRes => {
          // 获取模型
          this.$axios.get("/api/docCorrectConfig/get?id=" + this.config.root.configId).then(getModalRes => {
            // 新增模型
            let addModalForm = getModalRes.data;
            delete addModalForm.id;
            addModalForm.name = name;
            let addModalFormConfig = JSON.parse(addModalForm.config);
            addModalFormConfig.prompt = this.form.prompt;
            addModalFormConfig.qsOcrPrompt = this.form.qsOcrPrompt;
            addModalForm.config = JSON.stringify(addModalFormConfig);
            this.$axios.post("/api/docCorrectConfig/update", addModalForm).then(addModalRes => {
              let configId = addModalRes.data.id;
              // 查询TaskId
              let docCorrectTaskPageForm = {
                name,
                page: {size: -1, searchCount: false}
              };
              this.$axios.post(`/api/docCorrectTask/page`, docCorrectTaskPageForm).then(docCorrectTaskPageRes => {
                let taskId = docCorrectTaskPageRes.data.records[0].id;
                // 修改试卷名,模型名称
                let updateTaskForm = JSON.parse(JSON.stringify(this.config.root));
                updateTaskForm.name = name;
                updateTaskForm.configId = configId;
                updateTaskForm.aimodel = this.form.model;
                updateTaskForm.id = taskId;
                updateTaskForm.similarPaperId = this.config.root.id;
                updateTaskForm.status = 1;
                updateTaskForm.correctConfig = JSON.stringify({
                  aimodel: this.form.model
                });
                updateTaskForm.correctConfigObj.aimodel = this.form.model;
                updateTaskForm.fileId = '';
                this.$axios.post("/api/docCorrectTask/update", updateTaskForm).then(updateTaskRes => {
                  this.$message.success("新的模型已添加！");
                  this.loadTaskOptions();
                }).finally(() => {
                  this.closeDialog();
                  this.submitLoading = false;
                  this.$nextTick(() => {
                    loadingInstance.close();
                  });
                });
              })


            })

          })
        })
      })
      this.submitLoading = false;
      // this.closeDialog();
    },
    correctName(id) {
      this.$axios.post("/api/docCorrectTask/correctName", {
        id,
      }).then(res => {
        this.$message.success("校正中，请稍后查看...");
      });
    },
    saveResult(taskId, rowIdx) {
      this.resultSaving[rowIdx] = true;
      this.$axios.post(`/api/docCorrectResult/saveTask?taskId=${taskId}`).then(res => {
        this.$message.success("保存成功");
      }).finally(() => {
        this.resultSaving[rowIdx] = false;
      });
    },
    toRecord(id) {
      this.$router.push(`/docrecord/${id}`);
    },
    correctTask(item) {
      let form = {
        id: item.id,
        aimodel: item.correctConfigObj.aimodel,
        ocrType: '2',
        responseFormat: true
      };
      this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
        this.$message.success("提交成功");
        this.loadData(item.name);
        this.loadStatusStats();
      });
    },
    loadData(name) {
      let form = {
        name: name,
        page: {size: 1, searchCount: 999}
      };
      this.$axios.post(`/api/docCorrectTask/page`, form).then(res => {
        let tasks = this.config.tasks;
        let idx = tasks.findIndex(it => it.name === name);
        tasks[idx] = res.data.records[0];
        this.config.tasks = tasks;
      });
    },
    doCorrectName(command, id) {
      if (command === 'sync') {
        this.$refs.taskSelector.show({
          title: "同步试卷姓名", data: {id},
        });
      }
    },
    toConfig(configId) {
      let query = {};
      if (configId) {
        query.id = configId;
      }
      this.$router.push({
        name: "docconfig",
        query,
      });
    },
    onEditTask(data) {
      this.$refs.taskForm.show({title: "编辑", data});
    },
    deleteTaskFromConfig(id) {
      this.config.tasks = this.config.tasks.filter(item => item.id !== id);
    },
    onDeleteTask(id) {
      this.$confirm("确认删除该任务吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$axios.post(`/api/docCorrectTask/delete?id=${id}`).then(res => {
          this.$message.success("删除成功");
          this.deleteTaskFromConfig(id);
        });
      });
    },
    loadRootTaskOptions(query) {
      if (!query || query === '') return;
      this.taskRootLoading = true;
      let form = {
        name: query,
        status: 3,
        page: {
          searchCount: false,
          pageSize: -1,
        }
      };
      this.$axios.post(`/api/docCorrectTask/page`, form).then(res => {
        let records = [];
        res.data.records.forEach(it => {
          if (!it.similarPaperId)
            records.push(it);
        });
        this.taskRootOptions = records;
      }).finally(() => {
        this.taskRootLoading = false;
      });
    },
    loadTaskOptions(query) {
      if (this.config.root == null || this.config.root === '') {
        this.$message.warning("请先选择已经纠错的试卷");
        return;
      }
      this.taskLoading = true;
      let form = {
        name: query,
        page: {size: -1, searchCount: false},
        similarPaperId: this.config.root.id,
      };
      this.$axios.post(`/api/docCorrectTask/page`, form).then(res => {
        this.taskOptions = res.data.records;
        this.taskOptions.forEach((taskOption)=>{
          if (this.config.tasks.findIndex(it => it.id === taskOption.id) === -1) {
            this.config.tasks.push(taskOption);
          }
        });
        if (this.taskOptions.length === 0) {
          this.$message.warning("没有相似试卷，建议先新增模型");
        }
      }).finally(() => {
        this.taskLoading = false;
      });
    },
    loadCompareTaskOptions(query) {
      if (this.config.root == null || this.config.root === '') {
        this.$message.warning("请先选择已经纠错的试卷");
        return;
      }
      this.taskCompareLoading = true;
      let form = {
        name: query,
        page: {size: -1, searchCount: false}
      };
      this.$axios.post(`/api/docCorrectTask/page`, form).then(res => {
        this.taskCompareOptions = res.data.records;
        // this.config.tasks = this.taskOptions;
        // if (this.taskOptions.length === 0) {
        //   this.$message.warning("没有相似试卷，建议先新增模型");
        // }
      }).finally(() => {
        this.taskCompareLoading = false;
      });
    },
    onRootTaskSelect(vals) {
      this.config.root = vals;
      this.taskNames = vals.name;
      // this.config.tasks.push(vals)
      this.loadTaskOptions();
    },
    onTaskSelect(vals) {
      this.config.tasks.push(vals);
    },
    onCompareTaskSelect(vals) {
      this.config.tasks.push(vals);
      this.taskCompareNames = '';
      this.$message.success('添加成功');
    },
    onSubmit() {
      if (this.formData.tasks.length === 0) {
        this.$message.error('请添加试卷');
        return;
      }
      this.$emit('submit', this.formData);
      this.onClose();
    },
  }
}
</script>

<style lang="scss" scoped>
.button {
  background-color: rgb(147, 84, 214);
  border: none;
}

.dialog-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
