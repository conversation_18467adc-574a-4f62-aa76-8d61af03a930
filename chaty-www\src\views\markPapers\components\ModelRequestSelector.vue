<template>
  <el-dialog
      title="选择模型请求"
      v-model="visible"
      width="60%"
      @open="loadData"
      :close-on-click-modal="false"
  >
    <el-table
        :data="list"
        border
        style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="名称" width="100" />
      <el-table-column prop="content" label="参数" />
      <el-table-column prop="remark" label="备注" width="100" />
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="selectRow(row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="margin-top: 16px; text-align: right;">
      <el-pagination
          :current-page="pageNumber"
          :page-size="pageSize"
          :total="total"
          @current-change="handlePageChange"
          layout="prev, pager, next, jumper"
      />
    </div>

  </el-dialog>
</template>

<script>
import { ElMessage } from 'element-plus';

export default {
  name: 'ModelRequestSelector',
  emits: ['select'],
  data() {
    return {
      visible: false,
      list: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    show() {
      this.visible = true;
    },
    loadData() {
      try {
        const params = {
          page: {
            pageNumber: this.pageNumber,
            pageSize: this.pageSize,
          },
        };
        this.$axios.post('/api/model-request/selectPage', params).then((res)=>{
          const data = res.data;
          this.list = data.records;
          this.total = data.total;
        })
      } catch (error) {
        ElMessage.error('加载数据失败');
      }
    },
    handlePageChange(newPage) {
      this.pageNumber = newPage;
      this.loadData();
    },
    selectRow(row) {
      this.$emit('select', row.id);
      this.visible = false;
    },
  },
};
</script>

<style scoped>
/* 可根据需要添加样式 */
</style>
