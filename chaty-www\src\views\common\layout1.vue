<template>
    <div class="container-wrapper">
        <el-container style="padding: 0 50px;">
            <el-header>
                <nav-bar :menu-items="menuItems"></nav-bar>
            </el-header>
            <el-main class="container-main">
                <router-view v-slot="{ Component }">
                    <keep-alive>
                        <component :is="Component" :key="$route.name" v-if="$route.meta.keepAlive" />
                    </keep-alive>
                    <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive" />
                </router-view>
            </el-main>
        </el-container>

        <login-dialog />
    </div>
</template>

<script>
import NavBar from '../../components/layout/nav-bar.vue'
import LoginDialog from '../login/login-dialog.vue'
export default {
    components: {
        NavBar,
        LoginDialog,
    },
    data() {
        return {
            menuItems: [
                { index: '/doc/generate', label: '生成试卷', t: "menu.createDoc" },
                { index: '/doc/review', label: '试卷批改', t: "menu.docReview" },
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.container-wrapper {
    // width: 1280px;
    background: #fff;

    .container-main {
        height: calc(100vh - 60px);
        position: relative;

        &::before {
            content: "";
            background: url("/cat.png") center no-repeat;
            background-size: 500px;
            opacity: 0.1; /* 将opacity设置为0到1之间的值来调整透明度 */
            position: absolute; 
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            pointer-events: none;
        }
    }
}
</style>