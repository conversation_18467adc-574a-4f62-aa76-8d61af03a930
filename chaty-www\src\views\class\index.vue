<template>
  <div class="container">
    <div class="top-area">
      <el-menu :default-active="activeSchoolIndex" class="top-menu" mode="horizontal" :ellipsis="false"
               active-text-color="#333333" text-color="#999999" style="display: flex">
        <el-scrollbar style="width: 100%">
          <div style="display: flex;width: fit-content;">
            <el-menu-item v-for="(item, index) in schools" v-bind="item" :index="index" :key="item.schoolId"
                          @click="handleSchoolSelect(index)" @dblclick="handleSchoolDoubleClick($event, index)">
              <template #title>
                <el-tooltip placement="top" content="双击修改学校名称">
                  <el-text>{{ item.schoolName }}</el-text>
                </el-tooltip>
              </template>
            </el-menu-item>
          </div>
        </el-scrollbar>

        <div class="add-school-area">
          <el-button class="button" icon="Plus" @click="$refs.schoolDialog.openDialog(null)">新增</el-button>
        </div>
      </el-menu>
      <el-button class="add-button"
                 @click="$refs.schoolDialog.openDialog(activeSchoolIndex !== -1 ? schools[activeSchoolIndex] : null)">
        学校管理
      </el-button>
    </div>
    <div class="bottom-area">
      <el-menu class="left-menu" :default-active="activeClassIndex" mode="vertical">
        <el-menu-item v-for="(item, index) in classList" :index="index" :key="item.classId"
                      @click="handleClassSelect(index)"  @dblclick="handleClassDoubleClick($event, index)" class="el-menu-item">
          <template #title>
            <el-tooltip placement="top" content="双击修改班级名称">
              <el-text>{{ item.className }}</el-text>
            </el-tooltip>
          </template>
        </el-menu-item>
        <div class="add-class-area">
          <el-button class="button" icon="Plus" @click="$refs.addClassDialog.openDialog()">新增</el-button>
        </div>
        <el-icon class="operator-icon" @click="$refs.editClassDialog.openDialog(activeSchoolIndex !== -1 ? schools[activeSchoolIndex].schoolId : null)">
          <Operation/>
        </el-icon>
      </el-menu>
      <div v-if="!schools.length" class="student">
        <el-image src="/icon/class1.png" class="top-image"></el-image>
        <div class="middle-area">
          <div class="left-image" style="background: url('/icon/classNumber.svg')">1</div>
          <el-text class="right-text">请添加您的学校</el-text>
        </div>
        <el-button class="bottom-button" @click="$refs.schoolDialog.openDialog(null)">
          <el-text class="text">添加您的学校</el-text>
        </el-button>
      </div>
      <div v-else-if="!classList.length" class="student">
        <el-image src="/icon/class2.png" class="top-image"></el-image>
        <div class="middle-area">
          <div class="left-image" style="background: url('/icon/classNumber.svg')">2</div>
          <el-text class="right-text">请添加您的班级</el-text>
        </div>
        <el-button class="bottom-button" @click="$refs.addClassDialog.openDialog()">
          <el-text class="text">新增班级</el-text>
        </el-button>
      </div>
      <div v-else-if="!students.length" class="student">
        <el-image src="/icon/class3.png" class="top-image" style="width: 86.19px;height: 90.2px;"></el-image>
        <div class="middle-area">
          <div class="left-image" style="background: url('/icon/classNumber.svg');">3</div>
          <el-text class="right-text">请导入该班级的学生名单</el-text>
        </div>
        <el-button class="bottom-button"
                   @click="$refs.importStudentListDialog.openDialog(activeClassIndex !== -1 ? classList[activeClassIndex].classId : null)">
          <el-text class="text">导入学生名单</el-text>
        </el-button>
      </div>
      <div v-else class="class-area">
        <!-- 搜索区域 -->
        <div class="search-area">
          <div class="search-header">
            <el-text class="search-title">学生搜索</el-text>
          </div>
          <div class="search-input-area">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入学生姓名进行搜索"
              class="search-input"
              clearable
              @keyup.enter="handleSearch"
              @clear="clearSearch"
            >
              <template #append>
                <el-button @click="handleSearch" :loading="searchLoading">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          
          <!-- 搜索结果列表 -->
          <div v-if="searchResults.length > 0" class="search-results">
            <el-table :data="searchResults" class="search-table" stripe>
              <el-table-column prop="nickname" label="学生姓名" width="120"></el-table-column>
              <el-table-column prop="className" label="班级" width="120"></el-table-column>
              <el-table-column prop="schoolName" label="学校"></el-table-column>
            </el-table>
          </div>
        </div>

        <div class="top-header">
          <div class="left">
            <el-image class="icon" src="/icon/studentManager.png"></el-image>
            <el-text class="title">学生管理</el-text>
            <el-text class="number">{{ students.length }}人</el-text>
          </div>
          <div class="right">
            <el-tooltip content="添加学生" placement="top">
              <el-icon
                  :color="activeAction === 'addStudent' ? '#3981FF' : '#aeaeaf'"
                  size="15px"
                  @click="openAddStudentDialog">
                <Plus/>
              </el-icon>
            </el-tooltip>

            <div class="line"></div>
            <el-tooltip content="批量上传" placement="top">
              <el-icon
                  :color="activeAction === 'batchUpload' ? '#3981FF' : '#aeaeaf'"
                  size="15px"
                  @click="activeAction = 'batchUpload', $refs.importStudentListDialog.openDialog(activeClassIndex !== -1 ? classList[activeClassIndex].classId : null)">
                <Upload/>
              </el-icon>
            </el-tooltip>

            <div class="line"></div>
            <el-tooltip placement="top" content="删除所有学生">
              <el-icon :color="activeAction === 'deleteAll' ? '#3981FF' : '#aeaeaf'" size="15px" @click="deleteAllStudent">
                <Delete/>
              </el-icon>
            </el-tooltip>

          </div>
        </div>
        <el-table :data="students" class="table" stripe fit>
          <el-table-column prop="nickname" label="学生姓名"></el-table-column>
          <el-table-column prop="studentId" label="学号"></el-table-column>
          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button
                  @click="$refs.editStudentDialog.openDialog(scope.row)"
                  style="font-weight: 400; font-size: 14px; color: #3981FF; line-height: 22px; background: transparent; border: none; padding: 0; cursor: pointer;"
              >
                编辑
              </el-button>

              <el-button
                  @click="deleteStudent(scope.row)"
                  style="font-weight: 400; font-size: 14px; color: #999999; line-height: 22px; background: transparent; border: none; padding: 0; cursor: pointer;"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="delete-button" @click="deleteClass">删除班级</div>
      </div>
    </div>

    <add-class-dialog ref="addClassDialog" @fetchClasses="fetchClasses" @cancel="activeAction = ''"
                      :school-id="activeSchoolIndex !== -1 ? schools[activeSchoolIndex].schoolId : ''"></add-class-dialog>

    <school-management-dialog
        ref="schoolDialog"
        @cancel="activeAction = ''"
        @fetchSchools="fetchSchools"
    />

    <import-student-list-dialog
        @cancel="activeAction = ''"
        ref="importStudentListDialog"
        @fetchStudents="fetchStudents"
    />

    <edit-student-dialog
        ref="editStudentDialog"
        @cancel="activeAction = ''"
        @fetchStudents="fetchStudents"
    />
    <edit-class-dialog
        ref="editClassDialog"
        @cancel="activeAction = ''"
        @fetchClasses="fetchClasses">
    </edit-class-dialog>
  </div>
</template>

<script>
import AddClassDialog from './components/AddClassDialog.vue'
import EditClassDialog from './components/EditClassDialog.vue'
import SchoolManagementDialog from "./components/SchoolManagementDialog.vue";
import ImportStudentListDialog from "./components/ImportStudentListDialog.vue";
import EditStudentDialog from "./components/EditStudentDialog.vue";
import {Upload, Delete, Search} from '@element-plus/icons-vue';
import {ElMessage, ElMessageBox} from "element-plus";
import {mapState} from "pinia";
import {useUserStore} from "@/store";
import {getSchools} from "@/api/auth";

export default {
  components: {
    AddClassDialog,
    SchoolManagementDialog,
    ImportStudentListDialog,
    Upload,
    Delete,
    EditStudentDialog,
    EditClassDialog
  },
  data() {
    return {
      schools: [],
      activeSchoolIndex: -1,
      classList: [],
      activeClassIndex: -1,
      students: [],
      loadingClasses: false,
      activeAction: '',
      searchKeyword: '',
      searchResults: [],
      searchLoading: false,
    }
  },
  watch: {
    activeSchoolIndex: {
      handler: 'fetchClasses',
      immediate: true
    },
    activeClassIndex: {
      handler: 'fetchStudents',
      immediate: true
    }
  },
  created() {
    this.fetchSchools();
  },
  methods: {
    ...mapState(useUserStore, ['getSchool', 'getUser']),
    handleSchoolDoubleClick(event, index) {
      this.$refs.schoolDialog.openDialog(this.schools[index])
    },
    handleClassDoubleClick(event, index) {
      this.$prompt('请输入新的班级名称', '修改班级名称', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        // 用户确认输入
        console.log(value)
        if (value !== '') {
          this.$axios.put('/api/class/update/' + this.classList[index].classId, {
            className: value
          }).then(() => {
            this.$message.success("班级名称修改成功");
            this.fetchClasses();
          }).catch((error) => {
            console.error("修改班级名称错误：", error);
            this.$message.error("班级名称修改失败，请稍后再试");
          });
        } else {
          this.$message.error("不能为空")
        }
      }).catch((e) => {
        this.$message.info("已取消修改");
      });
      // Add your logic here, for example, opening an edit dialog or navigating to another page
    },
    openAddStudentDialog() {
      this.activeAction = 'addStudent';
      this.$refs.editStudentDialog.openDialog({classId: this.activeClassIndex !== -1 ? this.classList[this.activeClassIndex].classId : null});
    },
    deleteClass() {
      ElMessageBox.confirm('确定删除该班级吗').then(() => {
        this.$axios.delete(`/api/class/delete/${this.classList[this.activeClassIndex].classId}`).then(() => {
          ElMessage.success("班级删除成功");
          this.fetchClasses();
        }).catch((error) => {
          console.error("删除班级错误：", error);
          ElMessage.error("删除班级失败，请稍后再试");
        });
      });
    },
    fetchStudents() {
      this.activeAction = '';
      if (this.activeClassIndex === -1 || !this.classList.length) {
        return;
      }
      console.log('fetchStudents', this.classList[this.activeClassIndex].classId);
      this.$axios.get(`/api/user/student/list/${this.classList[this.activeClassIndex].classId}`).then((res) => {
        this.students = res.data.sort((a, b) => a.studentId - b.studentId);
        // 按照studentId排序
      }).finally(() => {
      });
    },
    handleClassSelect(index) {
      this.activeClassIndex = index;
    },
    handleSchoolSelect(index) {
      this.activeSchoolIndex = index;
    },
    fetchSchools() {
      this.$axios.get('/api/school/all').then(res => {
        this.schools = res.data
        if (this.schools.length && this.activeSchoolIndex === -1) {
          this.activeSchoolIndex = 0;
        }
      }).finally(() => {
      })
    },
    fetchClasses(refreshStudents = false) {
      if (this.activeSchoolIndex === -1 || !this.schools.length || this.activeSchoolIndex >= this.schools.length || !this.schools[this.activeSchoolIndex]?.schoolId) {
        return;
      }
      this.loadingClasses = true;
      this.$axios.get(`/api/class/by-school/${this.schools[this.activeSchoolIndex].schoolId}`).then((res) => {
        this.classList = res.data.sort((a, b) => a.className - b.className);;
        if (this.classList.length && this.activeClassIndex === -1) {
          this.activeClassIndex = 0;
        }
        this.fetchStudents();
      }).finally(() => {
        this.loadingClasses = false;
      });
    },
    editStudent(student) {
      ElMessage.info(`编辑学生: ${student.nickName}`);
    },
    deleteAllStudent() {
      this.activeAction = 'deleteAll';
      ElMessageBox.confirm('确定删除所有学生吗').then(() => {
        this.students.forEach((student) => {
          this.deleteStudent(student);
        });
      }).finally(() => {
        this.activeAction = '';
      });
    },
    deleteStudent(student) {
      // 这里添加删除学生的逻辑，例如弹出确认对话框
      this.$axios.get(`/api/user/delete?id=${student.id}`).then(() => {
        ElMessage.success("学生删除成功");
        this.fetchStudents();
      }).catch((error) => {
        console.error("删除学生错误：", error);
        ElMessage.error("删除学生失败，请稍后再试");
      });
    },
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning('请输入搜索关键词');
        return;
      }
      this.searchLoading = true;
      this.$axios.post('/api/school/user/selectClassByNickname', {
        nickname: this.searchKeyword.trim()
      }).then((res) => {
        // 日志输出返回体结构
        console.log('接口原始返回：', res.data);
        if (Array.isArray(res.data)) {
          if (res.data.length > 0) {
            this.searchResults = res.data;
          } else {
            this.searchResults = [];
            this.$message.info('未找到相应的学生信息');
          }
        } else {
          this.searchResults = [];
          this.$message.error('搜索失败，返回数据格式异常');
        }
      }).catch((error) => {
        console.error('搜索学生错误：', error);
        this.$message.error('搜索失败，请稍后再试');
        this.searchResults = [];
      }).finally(() => {
        this.searchLoading = false;
      });
    },
    clearSearch() {
      this.searchKeyword = '';
      this.searchResults = [];
    }
  }
}
</script>

<style lang="scss" scoped>

.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .top-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 25px 27.5px 15px 0px;
    height: 60px;
    border-bottom: 1px solid #e1e1e1;

    .top-menu {
      display: flex;
      width: 90%;
      flex-shrink: 1;
      gap: 20px;
      align-items: center;

      .el-menu-item {
        font-weight: 400;
        font-size: 15px;
        color: #999999;
        background: transparent;
      }

      .el-menu-item:focus {
        font-weight: 500;
        font-size: 15px;
        color: #333333;
        border-bottom: none !important;
      }

      .el-menu-item.is-active {
        font-weight: 500;
        color: #333333;
        background: #e6f0ff;
        border-top: 2px solid #2E6EE0;
      }

      .el-menu-item:hover {
        color: #333333;
      }

      .add-school-area {
        display: flex;
        border-radius: 4px;
        align-items: flex-start;

        .button {
          width: 60px;
          font-weight: bold;
          font-size: 15px;
          color: #3981FF;
          border: none;
          background: transparent;
        }
      }
    }

    .add-button {
      width: 93px;
      height: 32px;
      background: #3981ff21;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      color: #3981FF;
      letter-spacing: 0;
      text-align: center;
    }
  }

  .bottom-area {
    display: flex;
    height: 100%;

    .left-menu {
      display: flex;
      flex-direction: column;
      width: 176px;
      height: 100%;
      background: #F4F6FA;

      .operator-icon {
        position: absolute;
        bottom: 20px;
        right: 20px;
      }

      :deep(.el-menu-item) {
        width: 175px;
        height: 42px;
        font-weight: 400;
        font-size: 15px;
        color: #999999;
      }

      .el-menu-item.is-active {
        width: 175px;
        height: 42px;
        background: #1677ff1a;
        font-weight: 500;
        font-size: 15px;
        color: #333333;
      }

      .el-menu-item:hover {
        color: #333333;
      }

      .add-class-area {
        display: flex;
        width: 100%;
        height: 40px;
        border-radius: 4px;
        margin-top: 10px;
        align-items: flex-start;

        .button {
          width: 60px;
          font-weight: bold;
          font-size: 15px;
          color: #3981FF;
          border: none;
          margin-left: 15px;
          background: transparent;
        }
      }

    }

    .student {
      display: flex;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .top-image {
        width: 154.69px;
        height: 96px;
        background-size: 100% 100%;
      }

      .middle-area {
        display: flex;
        align-items: center;
        margin-top: 22px;

        .left-image {
          width: 33.74px;
          height: 36.79px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bolder;
          font-size: 18px;
          color: #FFFFFF;
          letter-spacing: 0;
          padding: 0 0 0 5px;
        }

        .right-text {
          font-weight: 500;
          font-size: 16px;
          color: #333333;
          letter-spacing: 0;
          margin-left: 10px;
        }
      }

      .bottom-button {
        margin-top: 60px;
        width: 121px;
        height: 32px;
        background: #3981FF;
        border-radius: 4px;

        .text {
          font-size: 14px;
          color: #FFFFFF;
          letter-spacing: 0;
          text-align: center;
          font-weight: normal;
        }
      }
    }

    .class-area {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 20px 25px;
      align-items: center;

      .search-area {
        width: 100%;
        margin-bottom: 20px;

        .search-header {
          font-weight: bolder;
          font-size: 16px;
          color: #333333;
          letter-spacing: 0;
          margin-bottom: 10px;
        }

        .search-input-area {
          width: 100%;
          display: flex;
          align-items: center;

          .search-input {
            width: 100%;
          }
        }

        .search-results {
          margin-top: 10px;
          
          .search-table {
            max-height: 120px; /* 限制高度，大约3行 */
            overflow-y: auto;
            
            :deep(.el-table__body-wrapper) {
              max-height: 120px;
            }
          }
        }
      }

      .top-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        align-items: center;

        .left {
          display: flex;
          flex-direction: row;
          align-items: center;

          .icon {
            width: 23px;
            height: 23px;
          }

          .title {
            font-weight: bolder;
            font-size: 16px;
            color: #333333;
            letter-spacing: 0;
            margin-left: 5px;
          }

          .number {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            letter-spacing: 0;
            margin-left: 5px;
          }
        }

        .right {
          display: flex;
          align-items: center;

          .line {
            width: 1px;
            height: 18px;
            border: 1px solid #DBDBDB;
            margin: 0 17px;
          }
        }
      }

      .table {
        min-height: 455px;
        margin-top: 20px;
      }

      .table .el-table__header {
        background-color: #fafafa;
      }

      .delete-button {
        width: 93px;
        height: 32px;
        background: #FF413F;
        border-radius: 4px;
        color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        font-weight: normal;
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        bottom: 26px;
        right: 24px;
      }
    }

    .el-table {
      width: 100%;
    }

    .el-table .el-table__column {
      text-align: center;
    }


  }
}
</style>
