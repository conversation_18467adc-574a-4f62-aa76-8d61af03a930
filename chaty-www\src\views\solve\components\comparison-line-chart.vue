<template>
  <div>
    <div ref="chart" style="width: 400px; height: 300px;"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "ComparisonChart",
  mounted() {
    this.initChart();
  },
  props: {
    // 可根据需要添加 props
    data: {
      type: Array,
      default: () => [],
    }
  },
  methods: {
    initChart(input) {
      let data = [];
      if (input && input.length) {
        data = input;
      } else if (this.data && this.data.length) {
        data = this.data;
      } else {
        data = [];
      }

      if (!data || data.length === 0) {
        return;
      }

      // 按正确率从低到高排序
      const sortedData = data.sort((a, b) => a.accuracy - b.accuracy);
      const xAxisData = sortedData.map(item => item.name);
      const seriesData = sortedData.map(item => item.accuracy);

      const chart = echarts.init(this.$refs.chart);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        title: {
          text: "模型正确率走势图",
          left: "center",
          bottom: 0
        },
        xAxis: {
          type: "category",
          data: xAxisData,
          axisLabel: {
            interval: 0, // 显示所有标签
            rotate: 0, // 旋转标签以避免重叠
            formatter: (value) => {
              return value; // 可以根据需要调整
            }
          }
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: seriesData,
            type: "bar",
            smooth: true,
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style scoped>
/* 可根据需要添加样式 */
</style>
