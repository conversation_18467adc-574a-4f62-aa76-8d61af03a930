<template>
  <div class="main-wrapper">
    <div class="main-content">
      <el-table v-loading="recordsLoading" :key="tableKey" :data="records" style="height: 100%;overflow-y: auto;" border
                empty-text="无数据 | 加载中"
                @expand-change="onExpandChange" :expand-row-keys="expandRows" row-key="id" class="my-custom-table"
                :row-class-name="getRowClassName">
        <el-table-column type="expand">
          <template #default="props">
            <div style="padding: 0 20px;">
              <reviews :ref="props.row.id" :showError="showError" :record="props.row" :task="task" :config="config"
                       :colMode="colMode"
                       :showAll="allQsIds.includes(props.row.id)"
                       @onUpdate="onQsUpdated"
                       @errorCorrection="errorCorrection({ id: props.row.id, docurl: props.row.docurl, taskId: taskId, paperIdx: props.$index}, $event)"
              />
            </div>
          </template>
        </el-table-column>
        <!--        <el-table-column type="index" label="序号" width="60" align="center"/>-->
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center"
                         :width="isEssay ? '' : (column.width || '')">

          <template v-if="column.prop === 'operations'" v-slot="scope">
            <template v-if="scope.row.status === 4">
              <el-button type="primary" size="small" :loading="loadings[scope.row.id].preview"
                         @click="downloadReviewd({ id: scope.row.id, docurl: scope.row.docurl, taskId: taskId, paperIdx: scope.$index})">
                预览
              </el-button>
            </template>
            <el-dropdown>
                  <span>
                    <el-icon class="el-icon--right" style="margin-top: 5px">
                      <more/>
                    </el-icon>
                  </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="[1, 4, 5].includes(scope.row.status)" @click="doCorrect(scope.row.id)">批改
                  </el-dropdown-item>
                  <el-dropdown-item v-if="showError && !allQsIds.includes(scope.row.id)"
                                    @click="showAllQs(scope.row.id)">所有题目
                  </el-dropdown-item>
                  <el-dropdown-item v-if="showError && allQsIds.includes(scope.row.id)"
                                    @click="showErrorQs(scope.row.id)">所有错题
                  </el-dropdown-item>
                  <el-dropdown-item  @click="doCorrect(scope.row.id)">重新批改
                  </el-dropdown-item>
                  <el-dropdown-item @click="doEdit(scope.row)">编辑</el-dropdown-item>
                  <el-dropdown-item v-if="isEssay" @click="doEditName(scope.row)">姓名纠正</el-dropdown-item>
                  <el-dropdown-item @click="doDelete(scope.row.id)" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag v-if="!haveCorrectError(scope.row.reviewedObj, scope.row.status)"
                    :type="statusOptions[scope.row.status].type">{{
                statusOptions[scope.row.status].label
              }}
            </el-tag>
            <el-tag v-else type="danger">批改失败</el-tag>
          </template>
          <template v-else-if="column.prop === 'docname'" v-slot="scope">
            <el-link type="primary" :href="$fileserver.fileurl(scope.row.docurl)" target="_blank">
              {{ scope.row.docname.split('_').pop() }}
            </el-link>
          </template>
          <template v-else-if="column.prop === 'progress'" v-slot="scope">
            <el-text v-if="scope.row.status === 3">{{
                `${scope.row.areaCorrected || 0}/${scope.row.areaNum
                || 0}`
              }}
            </el-text>
          </template>
          <template v-else-if="column.prop === 'score'" v-slot="scope">
            <el-text v-if="scope.row.status === 4 && config.configObj.score">{{
                calcScore(scope.row)
              }}
            </el-text>
          </template>
          <template v-else-if="column.prop === 'hasChange'" v-slot="scope">
            <template v-if="[4,5].includes(scope.row.status)">
              <el-tag v-if="scope.row.hasChange === 1" type="success">已修改</el-tag>
              <el-tag v-else>未修改</el-tag>
            </template>
          </template>
          <template v-else-if="column.prop === 'identify'" v-slot="scope">
            <el-tooltip v-if="scope.row?.reviewedObj[0]?.reviewed[0]?.isEssay" placement="top" :content="scope.row?.reviewedObj[0]?.reviewed[0]?.studentName">
              <el-text class="truncate-10">
                {{ scope.row?.reviewedObj[0]?.reviewed[0]?.studentName }}
              </el-text>
            </el-tooltip>
            <el-tooltip v-else placement="top" :content="scope.row?.identify || '无' ">
              <el-text class="truncate-10">{{ scope.row?.identify || '无' }}</el-text>
            </el-tooltip>

          </template>
          <template v-else-if="column.prop === 'studentNumber'" v-slot="scope">
            <el-tooltip placement="top" :content="scope.row?.studentNumber || '无' ">
              <el-text class="truncate-20">{{ scope.row?.studentNumber || '无' }}</el-text>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <record-form ref="recordForm" @onClose="refreshRecordNameOrStudentNumber"/>
  </div>
</template>
<script>
import Reviews from '@/views/doccorrect/components/reviews.vue'
import RecordForm from '@/views/doccorrect/components/recordForm.vue';
import {useUserStore} from "@/store";

export default {
  components: {Reviews, RecordForm},
  props: {
    taskId: String,
    isCorrected: {
      type: Boolean,
      default: false
    },
    selectRowIndex: {
      type: Number,
      default: null
    },
    isTaskSelected: {
      type: Boolean,
      default: true
    },
    showStudentName: {
      type: Boolean,
      default: false
    },
    showStudentNumber: {
      type: Boolean,
      default: false
    },
    isEssay: {
      type: Boolean,
      default: false
    },
    // 是否需要加载数据
    parentLoadedData: {
      type: Object,
      default: () => {
        return null;
      }
    },
    students: {
      type: Array,
      default: () => {
        return [];
      }
    },
  },
  data() {
    return {
      tableKey: 0,
      records: [],
      columns: [
        {
          prop: "docname",
          label: "试卷名称",
          width: 100
        },
        {
          prop: "score",
          label: "分数",
          width: 100,
        },
        {
          prop: "status",
          label: "状态",
          width: 85,
        },
        {
          prop: "hasChange",
          label: "是否修改",
          width: 85
        },
        {
          prop: "progress",
          label: "进度",
          width: 90,
        },
        {
          prop: "operations",
          label: "操作",
          width: 100,
        }
      ],
      statusOptions: {
        1: {value: 1, label: "未批改",},
        2: {value: 2, label: "待批改", type: "warning"},
        3: {value: 3, label: "批改中", type: "warning"},
        4: {value: 4, label: "已批改", type: "success"},
        5: {value: 5, label: "处理异常", type: "danger"},
      },
      ocrOptions: {
        "1": {value: "1", label: "MathPix OCR"},
        "2": {value: "2", label: "Tencent OCR"},
        "3": {value: "3", label: "gpt-4o"},
      },
      ocrType: "2",
      responseFormat: true,
      timer: null,
      progress: {
        total: 0,
        finished: 0,
      },
      previewedPdfLoading: false,
      reviewedPdfLoading: false,
      task: null,
      config: null,
      expandRows: [],
      loadings: {},
      statsLoading: false,
      showError: false,
      allQsIds: [],
      previewedPdfOptions: {
        showQsScore: true,
      },
      reviewedPdfOptions: {
        showQsScore: true,
      },
      colMode: true,
      taskLength: 0,
      zipDownloading: false,
      recordsLoading: false,
    }
  },
  created() {
    const parentLoadedData = this.parentLoadedData;
    this.task = parentLoadedData.task;

    // loadConfig
    this.config = parentLoadedData.config;


    let taskLength = 0;
    if (this.config && this.config.areasObj) {
      this.config.areasObj.forEach((area, areaIdx) => {
        taskLength += area.questions.length;
      })
    } else {
      taskLength = 0;
    }
    this.taskLength = taskLength;

    // refresh
    this.records = parentLoadedData.records
    this.judgeAddStudentName(this.config.configObj?.nameArea != null);
    this.judgeAddStudentNumber(this.config.configObj?.studentNumberArea != null);
    this.calcProgress()
    if (this.selectRowIndex && this.expandRows.indexOf(this.records[this.selectRowIndex].id) === -1) {
      this.expandRows.push(this.records[this.selectRowIndex].id)
    }
  },
  watch: {
    parentLoadedData: {
      handler(val) {
        this.records = this.parentLoadedData.records
      }
    },
    'records.length': {
      handler(val) {
        const loadings = this.loadings
        const ids = this.records.map(r => r.id)
        for (const id in loadings) {
          if (!ids.includes(id)) {
            delete loadings[id]
          }
        }
        ids.forEach(id => {
          if (!loadings[id]) {
            loadings[id] = {download: false, preview: false}
          }
        })
        this.loadings = loadings
      },
    },
    'selectRowIndex': {
      handler(val, oldVal) {
        if (this.records.length !== 0) {
          if (val && this.isTaskSelected) {
            let id = this.records[val].id
            if (this.expandRows.indexOf(id) === -1) {
              this.expandRows.push(id)
            }
          }
        }

      }
    },
    isCorrected: {
      handler(val) {
        if (val) {
          // Remove the column with prop "progress"
          const progressIndex = this.columns.findIndex(col => col.prop === 'progress');
          if (progressIndex !== -1) {
            this.columns.splice(progressIndex, 1);
          }
        }
      },
      immediate: true,
    }
  },
  methods: {
    refreshRecordNameOrStudentNumber(e) {
      this.records.forEach(record => {
        if (record.id === e.id) {
          record.identify = e.identify;
          record.studentNumber = e.studentNumber;
        }
      })
      // 刷新预览
      this.$emit('refreshRecordNameOrStudentNumber', e)
    },
    judgeAddStudentName(haveStudentNameData) {
      // if (this.showStudentName && haveStudentNameData) {
      // 以前是根据是否有姓名数据和是否要求显示姓名来判断的，现在要求常驻
      if (true) {
        // 插入在第三个之后
        this.columns.splice(2, 0, {
          prop: "identify",
          label: "姓名",
          width: 80
        });
      }
      if (this.isEssay) {
        // 去掉是否修改 hasChange/进度 progress
        this.columns = this.columns.filter(it => it.prop !== 'hasChange' && it.prop !== 'progress')
      }
    },
    judgeAddStudentNumber(haveStudentNumberData) {
      // 有数据就展示
      let mustShow = false;
      this.records.forEach(record => {
        if (record.studentNumber) {
          mustShow = true;
        }
      })
      if ((this.showStudentNumber && haveStudentNumberData) || mustShow) {
        // 插入在第三个之后
        this.columns.splice(3, 0, {
          prop: "studentNumber",
          label: "学号",
          width: 110
        });
      }
      // this.columns = JSON.parse(JSON.stringify(this.columns));
      this.tableKey++;
    },
    errorCorrectionFromCropper(val) {
        // 展开val.docUrl
      if (this.records && this.records.length) {
        let id = this.records.find(r => r.docurl === val.docUrl).id;
        if (this.expandRows.indexOf(id) === -1) {
          this.expandRows.push(id);
          this.$nextTick(() => {
            this.$refs[id].errorCorrectionFromCropper(val);
          })
        } else {
          this.$refs[id].errorCorrectionFromCropper(val);
        }
      }
    },
    haveCorrectError(e, paperStatus) {
      if (paperStatus !== '4') return false;
      for (let i = 0; i < e.length; i++) {
        if (e[i].status === '2') return true;
      }
      return false;
    },
    getRowClassName({row, rowIndex}) {
      return rowIndex === this.selectRowIndex && this.isTaskSelected ? 'highlight-row' : '';
    },
    onBack() {
      this.$router.back()
    },
    refresh() {
      this.$forceUpdate()
      this.$emit('refresh')
    },
    uploadDoc(response, file, fileList) {
      let form = {
        taskId: this.taskId,
        docname: file.name,
        docurl: response.data.url,
        status: 1,
      }
      this.$axios.post("/api/docCorrectRecord/add", form).then(res => {
        this.refresh()
      })
    },
    doDelete(id) {
      this.$confirm("确认删除该记录吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$axios.post(`/api/docCorrectRecord/delete?id=${id}`).then(res => {
          this.$message.success("删除成功")
          this.refresh()
        })
      })
    },
    doCorrect(recordId) {
      if (this.records.length === 0) {
        this.$message.error("请先上传试卷")
        return
      }
      const store = useUserStore();
      let form = {
        id: this.taskId,
        aimodel: store.getDefaultCorrectModal,
        ocrType: this.ocrType,
        responseFormat: this.responseFormat,
      }
      if (recordId) {
        form.recordIds = [recordId]
      }
      this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
        this.$message.success("提交成功")
        this.refresh()
      })
    },
    refreshRecords() {
      if (!this.timer) {
        this.timer = setInterval(() => {
          this.refresh()
        }, 10 * 1000);
      }
    },
    calcProgress() {
      let stats = {
        total: this.records.length,
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0,
      }
      this.records.forEach(record => {
        stats[record.status] += 1
      })
      this.progress = stats
      if (this.progress[3] === 0) {
        clearInterval(this.timer)
        this.timer = null
      } else {
        this.refreshRecords()
      }
    },
    downloadReviewd(options) {
      this.$emit('review', options)
    },
    errorCorrection(options, $event) {
      let record = $event;
      let detail = [];
      for (let i = 0; i < record.length; i++) {
        let areaIdx = record[i].areaIdx;
        for (let qsIdx = 0; qsIdx < record[i].reviewed.length; qsIdx++) {
          if (
              this.config?.areasObj?.[areaIdx]?.questions?.[qsIdx]?.flagArea !== undefined &&
              record?.[areaIdx]?.reviewed?.[qsIdx]?.isCorrect !== undefined
          ) {
            detail.push({
              areaIdx: areaIdx,
              qsIdx: qsIdx,
              isRight: record[areaIdx].reviewed[qsIdx].isCorrect === 'Y',
              flagArea: this.config.areasObj[areaIdx].questions[qsIdx].flagArea,
              answer: this.config.areasObj[areaIdx].questions[qsIdx].answer,
              scored: record[areaIdx].reviewed[qsIdx].scored,
              isScorePoint: record[areaIdx].reviewed[qsIdx].isScorePoint,
              dontShowText: this.config.areasObj[areaIdx].questions[qsIdx]?.dontShowText ?? false,
              scoreType: this.config.areasObj[areaIdx].questions[qsIdx]?.scoreType ?? '总分',
              fullMarks: this.config.areasObj[areaIdx].questions[qsIdx]?.score ?? 0,
            })
          }
        }
      }
      this.$emit('errorCorrection', {options, detail: detail})
    },
    downloadFile(url, name) {
      // 使用fetch获取文件内容
      return fetch(this.$fileserver.fileurl(url))
          .then(response => response.blob())
          .then(blob => {
            // 如果需要下载，可以使用前面提到的下载代码
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = URL.createObjectURL(blob);
            a.download = name;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(a.href);
          })
          .catch(error => {
            console.error('发生错误:', error);
          });
    },
    onExpandChange(row, expandRows) {
      // 判断是不是新的
      if (this.expandRows.indexOf(row.id) === -1) {
        this.downloadReviewd({id: row.id, docurl: row.docurl, taskId: this.taskId, paperIdx: this.records.indexOf(row)})
      }
      this.expandRows = expandRows.map(r => r.id);
    },
    calcScore(record) {
      let scored = 0
      let total = 0
      let addScored = 0
      let addTotal = 0
      let addName = this.config.configObj.additionalName || '附加'
      this.config.areasObj.forEach((area, areaIdx) => {
        // 手写识别
        let areaType = area.areaType
        if (area.enabled) {
          area.questions.forEach((qs, qsIdx) => {
            let isAdd = (qs.isAdditional || 1) != 1;
            if (qs.isScorePoint === 2 || areaType === 4) {
              // 按照得分点给分
              scored += parseInt(record.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.scored || 0)
            } else {
              let isTrue = (record.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.isCorrect || 'Y') === 'Y'
              if (record.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.isEssay) {
                scored += isAdd ? 0 : parseFloat(record.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.scored)
              } else if (isTrue) {
                scored += isAdd ? 0 : qs.score
                addScored += isAdd ? qs.score : 0
              }
            }
            total += isAdd ? 0 : qs.score
            addTotal += isAdd ? qs.score : 0
          })
        }
      })

      if (addTotal > 0) {
        return `总分: ${scored}/${total}  ${addName}: ${addScored}/${addTotal}`
      } else {
        return `${scored}/${total}`
      }
    },
    showAllQs(id) {
      this.allQsIds.push(id)
    },
    showErrorQs(id) {
      this.allQsIds = this.allQsIds.filter(i => i !== id)
    },
    onQsUpdated(updated) {
      let index = this.records.findIndex(r => r.id = updated.id)
      this.records[index] = Object.assign(this.records[index], updated)
    },
    doEdit(record) {
      this.$refs.recordForm.show(record, this.students)
      this.$emit("freezeKeyboard", true);
    },
    doEditName(record) {
      let oldName = record?.reviewedObj[0]?.reviewed[0]?.studentName;
      this.$emit('freezeKeyboard', true);
      this.$prompt('请输入新的名称', '修改名称', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: oldName || '',
        inputPlaceholder: '请输入新的名称'
      }).then(({value}) => {
        // 获取用户输入的名称
        if (value && value.trim() !== '') {
          record.reviewedObj[0].reviewed[0].studentName = value.trim();
          record.reviewedObj[0].reviewed[0].review['学生姓名'] = value.trim();
          record.reviewed = JSON.stringify(record.reviewedObj);
          this.$axios.post("/api/docCorrectRecord/update", record).then(res => {
            this.$message.success("更新成功");
          }).catch(err => {
            this.$message.error("更新失败");
          });
          this.$forceUpdate();
        } else {
          this.$message.error("名称不能为空");
        }
        this.$emit('freezeKeyboard', false);
      }).catch(() => {
        // 用户取消操作
        this.$message.info("操作已取消");
        this.$emit('freezeKeyboard', false);
      });
    }


  }
}
</script>
<style>
.truncate-10 {
  display: inline-block;       /* 或者 inline-flex 根据你的布局 */
  max-width: 10ch;             /* 最多显示 10 个 “字符宽度” */
  white-space: nowrap;         /* 不自动换行 */
  overflow: hidden;            /* 隐藏超出的内容 */
  text-overflow: ellipsis;     /* 溢出时显示省略号 */
  vertical-align: bottom;      /* 根据需要调整对齐 */
}
.truncate-20 {
  display: inline-block;       /* 或者 inline-flex 根据你的布局 */
  max-width: 12ch;             /* 最多显示 10 个 “字符宽度” */
  white-space: nowrap;         /* 不自动换行 */
  overflow: hidden;            /* 隐藏超出的内容 */
  text-overflow: ellipsis;     /* 溢出时显示省略号 */
  vertical-align: bottom;      /* 根据需要调整对齐 */
}


.highlight-row {
  background: #e6f7ff !important;
}
</style>
<style lang="scss" scoped>


.el-table {
  table-layout: fixed;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    margin-bottom: 20px;

    .header-title {
      font-weight: 700;
      font-size: 20px;
    }

    .header-actions {
      display: flex;
      align-items: center;

      .header-action {
        margin-right: 10px;

        &.header-stats {
          width: 60px;
          text-align: center;
        }
      }

      .el-button + .el-button {
        margin-left: 0;
      }
    }
  }

  .main-content {
    height: calc(100% - 60px);

    .my-custom-table {
      width: 100%;
    }
  }

}
</style>