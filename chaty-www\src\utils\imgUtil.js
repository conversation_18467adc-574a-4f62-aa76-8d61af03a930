// Description: 图片处理工具

import {useUserStore} from "@/store";

export function drawRect(img, rects, answers, canvas, lineWidth = 5, strokeStyle = 'black') {
    const ctx = canvas.getContext('2d')
    if (!img?.width) {
        return;
    }

    // 设置canvas大小
    canvas.width = img.width;
    canvas.height = img.height;
    // 绘制图片
    ctx.drawImage(img, 0, 0);

    let rectWidth = 0 , rectHeight = 0;
    rects.forEach(rect => {
        if (!rect) {
            return
        }
        let { x, y, width, height } = rect
        // 绘制图片
        if (rect.mark) {
            rectWidth = rect.mark.width
            rectHeight = rect.mark.height
            ctx.drawImage(rect.mark, x + 70, y, 100 * rect.mark.width / rect.mark.height, 100)
            return
        }
        // 绘制方框
        ctx.strokeStyle = rect.color || strokeStyle; // 设置边框颜色
        ctx.lineWidth = lineWidth; // 设置边框宽度
        ctx.strokeRect(x, y, width, height); // 绘制方框
    })
    // 绘制答案
    if (answers && answers.length !== 0 ) {
        answers.forEach(answer => {
            if (!answer.dontShowText) {
                if (answer.isEssay) {
                    ctx.font = '90px Arial';
                    ctx.fillStyle = 'red';
                    let { x, y, width, height } = answer.area;
                    ctx.fillText(answer.text, x + 100, y + 100);
                } else {
                    ctx.font = '80px Arial';
                    ctx.fillStyle = 'red';
                    let { x, y, width, height } = answer.area;
                    ctx.fillText(answer.text, x + rectWidth / 2 + 80, y + rectHeight / 2.5);
                }
            }


        })
    }

    return canvas.toDataURL('image/jpeg');
}


export function img2base64(imgUrl) {
    const store = useUserStore();
    const cachedUrl = store.getImageDataById(imgUrl);

    // 如果 store 中有缓存，就先验证它是否还能用
    if (cachedUrl) {
        // 一定要 return 下面这个 Promise 链，否则外层不会得到返回值
        return fetch(cachedUrl)
            .then(res => {
                if (res.ok) {
                    // 缓存有效，直接返回这个 Blob URL
                    return cachedUrl;
                }
                // 如果 HEAD 返回非 2xx，就视作失效，抛到 catch 里去重新下载
                throw new Error('cachedUrl 已失效');
            })
            .catch(() => {
                // 缓存不存在或无效，重新从 imgUrl 下载
                return fetch(imgUrl)
                    .then(res => {
                        if (!res.ok) throw new Error('从远端下载失败');
                        return res.blob();
                    })
                    .then(blob => {
                        // 新生成一个 Blob URL
                        const newBlobUrl = URL.createObjectURL(blob);
                        // 把它存回 store，以便下次复用
                        store.setImageDataById(imgUrl, newBlobUrl);
                        return newBlobUrl;
                    });
            });
    }

    // 如果根本没缓存，就直接从远端下载
    return fetch(imgUrl)
        .then(res => {
            if (!res.ok) throw new Error('从远端下载失败');
            return res.blob();
        })
        .then(blob => {
            const newBlobUrl = URL.createObjectURL(blob);
            store.setImageDataById(imgUrl, newBlobUrl);
            return newBlobUrl;
        });
}


export function extraImg(img, area) {
    const canvas = document.getElementById('correct-canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0)
    const imageData = ctx.getImageData(area.x, area.y, area.width, area.height);
    
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = imageData.width;
    tempCanvas.height = imageData.height;
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.putImageData(imageData, 0, 0);

    // 获取 base64 格式的图像数据
    return tempCanvas.toDataURL('image/jpeg');
}

/**
 * 判断图片是否高度大于宽度
 */
export function isRotate(img) {
    return new Promise((resolve, reject) => {
        const image = new Image();
        image.onload = () => {
            console.log(image.width, image.height);
            resolve(image.width > image.height);
        };
        image.onerror = reject;
        image.src = img;
    });
}