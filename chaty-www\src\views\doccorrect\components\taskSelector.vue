<template>
    <el-dialog v-model="isShow" :title="title" :before-close="onClose" width="500">
        <el-form ref="formRef" :model="formData" label-position="top">
            <el-form-item label="同步试卷" prop="taskId" 
                :rules="[{ required: true, message: '请选择同步试卷', trigger: 'blur' }]">
                <el-select style="margin-bottom: 10px" suffix-icon="Search" placeholder="请输入试卷名称" remote filterable
                    v-model="formData.taskId"
                    :remote-method="loadTaskOptions" :loading="taskLoading" no-data-text="无数据">
                    <el-option v-for="item in taskOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" @click="onClose">取消</el-button>
            <el-button type="primary" @click="onSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script>
    export default {
        data() {
            return {
                isShow: false,
                title: "",
                formData: {
                    taskId: ""
                },
                taskOptions: [],
                taskLoading: false
            }
        },
        methods: {
            show({ title, data }) {
                this.formData = Object.assign(this.formData, data)
                this.title = title
                this.isShow = true
            },
            onClose() {
                this.formData = {
                    taskId: ""
                }
                this.isShow = false
            },
            onSubmit() {
                this.$refs.formRef.validate().then(() => {
                    this.$emit("submit", this.formData)
                    this.onClose()
                })
            },
            loadTaskOptions(query) {
                if (!query || query === '') {
                    return
                }
                this.taskName = query
                this.taskLoading = true
                let form = {
                    name: query,
                    status: 3,
                    page: {
                        size: -1,
                        searchCount: false,
                    }
                }
                this.$axios.post(`/api/docCorrectTask/page`, form).then(res => {
                    this.taskOptions = res.data.records
                }).finally(() => {
                    this.taskLoading = false
                })
            },
        }
    }
</script>

<style lang="scss" scoped>

</style>