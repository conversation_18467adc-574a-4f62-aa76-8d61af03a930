<template>
  <div class="container-wrapper">
    <el-card class="top-bar">
      <template #header>
        <div class="card-header">
          <span>{{ $t('common.settings') }}</span>
        </div>
      </template>
      <select-document ref="selectDocument" @startEvaluate="startEvaluate" ></select-document>
    </el-card>
    <el-card class="middle-card" style="margin-left: 0;width: 100%;flex-shrink: 1">
      <template #header>
        <div class="card-header">
          <span>模型对比结果</span>
        </div>
      </template>
      <contrast-table :data="contrastData" ></contrast-table>
    </el-card>
    <div style="display: flex;justify-content: space-between">
      <el-card class="middle-card" style="width: 450px;flex-shrink: 0">
        <template #header>
          <div class="card-header">
            <span>模型正确率对比</span>
          </div>
        </template>
        <comparison-line-chart ref="comparisonLineChart" :data="comparisonLineChartData" />
      </el-card>
      <el-card class="middle-card" style="width: 100%;margin-right: 0;flex-shrink: 1">
        <template #header>
          <div class="card-header">
            <span>模型错题分布</span>
          </div>
        </template>
        <mistake-distribution-chart ref="mistakeDistributionChart" />
      </el-card>
    </div>
    <div>
      <el-card class="bottom-content" style="margin-left: 0">
        <template #header>
          <div class="card-header">
            <span>模型错题正确率对比</span>
          </div>
        </template>
        <question-table :tableData="questionTableData" :dynamicColumns="questionDynamicColumns" />
      </el-card>
    </div>
  </div>
</template>

<script>
import CustomInput from '@/components/form/custom-input.vue'
import SelectDocument from './components/select-document.vue'
import contrastTable from './components/contrast-table.vue'
import ComparisonLineChart from './components/comparison-line-chart.vue'
import MistakeDistributionChart from './components/mistake-distribution-chart.vue'
import QuestionTable from './components/question-table.vue'
import comparisonLineChart from "@/views/solve/components/comparison-line-chart.vue";

export default {
  computed: {
    comparisonLineChart() {
      return comparisonLineChart
    }
  },
  components: {
    CustomInput,
    SelectDocument,
    contrastTable,
    ComparisonLineChart,
    MistakeDistributionChart,
    QuestionTable
  },
  data() {
    return {
      models: [
        {
          index: 0,
          model: 'gpt-4-turbo',
          answer: '',
          definition: '',
          theorem: '',
          loading: false,
          status: 0,
          disabled: false
        },
        {
          index: 0,
          model: 'gpt-4',
          answer: '',
          definition: '',
          theorem: '',
          loading: false,
          status: 0,
          disabled: false
        },
        {
          index: 1,
          model: 'gpt-3.5-turbo',
          answer: '',
          definition: '',
          theorem: '',
          loading: false,
          status: 0,
          disabled: false
        },
        {
          index: 2,
          model: 'ERNIE-Bot',
          answer: '',
          definition: '',
          theorem: '',
          loading: false,
          status: 0,
          disabled: false
        },
        /* {
            model: 'ERNIE-Bot-turbo',
            answer: '',
            definition: '',
            theorem: '',
            loading: false,
            status: 0,
            disabled: false
        },
        {
            model: 'CPM hackthon',
            answer: '',
            definition: '',
            theorem: '',
            loading: false,
            status: 0,
            disabled: false,
        }, */
        /* {
            model: 'BLOOMZ-7B',
            answer: '',
            definition: '',
            theorem: '',
            loading: false,
            status: 0,
            disabled: false
        }, */
      ],
      pdfUrl: '',
      solved: false,
      loading: false,
      accuracy: '',
      finalAnswer: '',
      finalAnswerIndex: 0,
      loadingPDF: false,
      loadingOfLibrary: false,
      loadingocr: false,
      contrastData: [],
      comparisonLineChartData: [],
      mistakeDistributionChartData: {
        questionIdx: [],
        modals: [],
        data: []
      },
      questionTableData: [
      ],
      questionDynamicColumns: [
        {prop: "gpt4o", label: "gpt-4o"},
        {prop: "gpt4o20240806", label: "gpt-4o-2024-08-06"},
        {prop: "gpt4o202408063", label: "gpt-4o-2024-08-06(三倍批改)"},
        {prop: "gpt4oMini", label: "gpt-4o-mini"},
        {prop: "gpt4VisionPreview", label: "gpt-4-vision-preview"},
        {prop: "claude3Opus", label: "Claude 3 Opus"},
        {prop: "claude3Sonnet", label: "Claude 3 Sonnet"},
        {prop: "gpt4Turbo", label: "gpt-4-turbo"},
        {prop: "gpt4", label: "gpt-4"},
        {prop: "gpt35Turbo", label: "gpt-3.5-turbo"},
        {prop: "ernieBot", label: "ERNIE-Bot"},
        {prop: "qwenTurbo", label: "qwen-turbo"},
        {prop: "qwenPlus", label: "qwen-plus"},
        {prop: "qwenMax", label: "qwen-max"},
      ],


    }
  },
  methods: {
    startEvaluate(input) {
      let data = input.data;
      let questionAndAnswers = input.questionAndAnswers;
      let contrastData = []
      let comparisonLineChartData = []
      let mistakeDistributionChartData = {
        questions: {
          errorCnt: {},
          cntByPaper: [],
        },
        questionIdx: [],
        modals: [],
        data: []
      }
      let maxValue = 0;
      let questionDynamicColumns = [];
      let questionTableData = [];
      data.forEach((item, index) => {
        contrastData.push({
          name: item.name,
          modelName: item.aimodel,
          errorCorrectionNumber: item.wrongCnt,
          accuracy: item.accuracy,
          questionNumber: item.cnt
        })
        comparisonLineChartData.push({
          name: item.aimodel,
          accuracy: item.accuracy
        })
        // 统计总的错题分布，和每一套试卷的错题分布
        mistakeDistributionChartData.questions.cntByPaper.push({});
        item.wrongQuestionsByModals.forEach(wrongQuestionsByModal => {
          wrongQuestionsByModal.forEach(questionIdx => {
            if (questionIdx in mistakeDistributionChartData.questions.errorCnt) {
              mistakeDistributionChartData.questions.errorCnt[questionIdx]++;
              maxValue = maxValue > mistakeDistributionChartData.questions.errorCnt[questionIdx] ? maxValue : mistakeDistributionChartData.questions.errorCnt[questionIdx];
            } else {
              mistakeDistributionChartData.questions.errorCnt[questionIdx] = 1;
            }
            if (questionIdx in mistakeDistributionChartData.questions.cntByPaper[index]) {
              mistakeDistributionChartData.questions.cntByPaper[index][questionIdx]++;
            } else {
              mistakeDistributionChartData.questions.cntByPaper[index][questionIdx] = 1;
            }
          })
        })
        mistakeDistributionChartData.modals.push(item.aimodel);
        // 底部table数据
        questionDynamicColumns.push({prop: item.aimodel, label: item.aimodel});

      })
      mistakeDistributionChartData.questionIdx = Object.keys(mistakeDistributionChartData.questions.errorCnt)
          .map(Number) // 将键转换为数字
          .sort((a, b) => a - b); // 按数字排序

      data.forEach((item, modalIdx) => {
        let data = []
        mistakeDistributionChartData.questionIdx.forEach((item, idx) => {
          data.push([item, mistakeDistributionChartData.questions.cntByPaper[modalIdx][item] || 0])
        })
        mistakeDistributionChartData.data.push(data)
      })

      mistakeDistributionChartData.questionIdx.forEach((item, index) => {
        questionTableData.push({
          ...questionAndAnswers[item],
          idx:item,
          correctAnswer: questionAndAnswers[item].answer
        });
      })
      let paperCnt = data[0].paperCnt;
      data.forEach((item, index) => {
        questionTableData.forEach((question, idx) => {
          question[item.aimodel] = ((1 - ((mistakeDistributionChartData.questions.cntByPaper[index][question.idx] || 0) / paperCnt)) * 100).toFixed(2) + '%';
          // + " " + (mistakeDistributionChartData.questions.cntByPaper[index][question.idx] || 0 ) + "/" + paperCnt;
          question.accuracy = Math.min((question.accuracy || 10000),((1 - ((mistakeDistributionChartData.questions.cntByPaper[index][question.idx] || 0) / paperCnt)) * 100).toFixed(2));
        })
      })
      questionTableData = questionTableData.sort((a, b) => a.accuracy - b.accuracy);
      console.log(questionTableData)
      console.log(mistakeDistributionChartData)
      this.contrastData = contrastData;
      this.comparisonLineChartData = comparisonLineChartData;
      this.$refs.comparisonLineChart.initChart(comparisonLineChartData);
      this.mistakeDistributionChartData = mistakeDistributionChartData;
      this.$refs.mistakeDistributionChart.updateChart(mistakeDistributionChartData);

      this.questionDynamicColumns = questionDynamicColumns;
      this.questionTableData = questionTableData;

      this.$refs.selectDocument.closeLoading();
    },
    normalizeValue(value, max, min = 0, newMin = 0, newMax = 10) {
      if (value < min) return newMin;
      if (value > max) return newMax;

      return ((value - min) / (max - min)) * (newMax - newMin) + newMin;
    },
  }
}
</script>

<style lang="scss" scoped>
.container-wrapper {
  width: auto !important;
  display: flex;
  flex-direction: column;
  column-gap: 20px;

  .top-bar {
    width: 100%;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .form-config {

      .form-question {
        :deep(.el-form-item__label) {
          padding-right: 0;

          .pic-uploader {
            float: right;
            height: 28px;
          }
        }
      }
    }
  }

  .bottom-content {
    margin: 5px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-button {
        background-color: rgb(147, 84, 214);
        border: none;
      }
    }

    :deep(.el-scrollbar) {
      height: calc(100vh - 210px);
    }

    .result-wrapper {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;

      :deep(.el-scrollbar) {
        height: 300px;
      }

      .multi-answer {
        grid-column: 1 / span 2;
      }
    }
  }

}

.content-text {
  white-space: pre-wrap;
  word-break: break-all;
}

.middle-card {
  margin: 5px;
}
</style>