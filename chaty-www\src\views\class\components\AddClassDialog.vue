<template>
  <el-dialog v-model="dialogVisible" title="添加班级" :before-close="handleClose" :width="'680px'" :close-on-click-modal="false">
    <el-form :model="form" ref="formRef" label-width="100px" class="form-container">
      <el-form-item label="选择学校" prop="schoolId"
                    :rules="[{ required: true, message: '请选择学校', trigger: 'change' }]">
        <el-select v-model="form.schoolId" placeholder="请选择学校" filterable>
          <el-option v-for="school in schoolList" :key="school.schoolId" :label="school.schoolName"
                     :value="school.schoolId"/>
        </el-select>
      </el-form-item>
      <el-form-item label="选择班级" prop="classNames"
                    :rules="[{ required: true, message: '请选择班级', trigger: 'change' }]">
        <el-select
            v-model="form.classNames"
            multiple
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择班级"
            :loading="loadingClasses"
        >
          <el-option v-for="schoolClass in classList" :key="schoolClass.classId" :label="schoolClass.className"
                     :value="schoolClass.classId"/>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="button" type="primary" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {mapState} from "pinia";
import {useUserStore} from "@/store";
import { ElMessageBox } from 'element-plus'
import {getSchools} from "@/api/auth";
export default {
  props: {
    schoolId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        schoolId: '',
        classNames: [],
      },
      schoolList: [],
      classList: [],
      loadingSchools: false,
      loadingClasses: false,
    };
  },
  mounted() {
  },
  watch: {
    'form.schoolId': {
      handler: 'fetchClasses',
      immediate: true,
    },
  },
  computed: {
    ...mapState(useUserStore, ['getUser']),
  },
  methods: {
    openDialog() {
      this.dialogVisible = true;
      this.fetchSchools()
    },
    handleClose() {
      this.dialogVisible = false;
      this.$refs.formRef.resetFields();
    },
    fetchSchools() {
      this.loadingSchools = true;
      getSchools().then(res => {
        this.schoolList = res.data;
        if (!this.form.schoolId && this.schoolId) {
          this.form.schoolId = this.schoolId;
        }
        this.fetchClasses();
      }).finally(() => {
        this.loadingSchools = false;
      })
    },
    fetchClasses() {
      if (!this.form.schoolId) {
        return;
      }
      this.loadingClasses = true;
      this.$axios.get(`/api/class/by-school/${this.form.schoolId}`).then((res) => {
        this.classList = res.data;
        this.form.classNames = [];
        this.classList.forEach((it)=>{
          this.form.classNames.push(it.className);
        })
      }).finally(() => {
        this.loadingClasses = false;
      });
    },
    submitForm() {
        let data = [];
        this.form.classNames.forEach((className) => {
          let index = this.classList.findIndex((it) => it.className === className);
          if (index === -1) {
            data.push({
              schoolId: this.form.schoolId,
              className,
              teacherUserId: this.getUser.id
            });
          }
        });
        let deleteDataIds = [];
        this.classList.forEach((it) => {
          if (this.form.classNames.indexOf(it.className) === -1) {
            deleteDataIds.push(it.classId);
          }
        });
        console.log('deleteDataIds', deleteDataIds)
        if (deleteDataIds.length) {
          // 提醒用户确认删除
          ElMessageBox.alert('删除班级会删除班级下的所有学生，是否确认删除？', '删除班级', {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
          }).then((msg) => {
            console.log(msg)
            this.$axios.post('/api/class/delete', deleteDataIds).then((res) => {
              this.$axios.post('/api/class/addByBatch', data).then((res)=>{

              }).finally(()=>{
                this.dialogVisible = false;
                this.$message.success('班级添加成功');
                this.$refs.formRef.resetFields();
                this.$emit('fetchClasses')
              });
            });
          });
        } else {
          this.$axios.post('/api/class/addByBatch', data).then((res)=>{

          }).finally(()=>{
            this.dialogVisible = false;
            this.$message.success('班级添加成功');
            this.$refs.formRef.resetFields();
            this.$emit('fetchClasses')
          });
        }

    },
  },
};
</script>

<style scoped>

.dialog-footer {
  display: flex;
  justify-content: center;

  .button {
    width: 90px;
    height: 32px;
    background: #3981FF;
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
  }
}

.form-container {
  padding: 20px;
}

.el-dialog .el-dialog__footer {
  text-align: center;
}

.el-button[type="primary"] {
  background-color: #3981FF;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
}

.el-select {
  width: 100%;
}
</style>
