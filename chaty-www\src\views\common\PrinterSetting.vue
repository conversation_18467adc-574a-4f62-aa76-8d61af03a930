<template>
  <el-dialog v-model="dialogVisible" title="打印机设置" width="50%" append-to-body>
    <el-form :model="printerSettings" label-width="150px">
      <!-- 打印机选择 -->
      <el-form-item label="选择打印机" prop="device.deviceId" required>
        <el-select v-model="printerSettings.device.deviceId" placeholder="选择打印机">
          <el-option
              v-for="device in printerList"
              :key="device.deviceId"
              :label="getLabelName(device)"
              :value="device.deviceId"
          >
          </el-option>
        </el-select>
        <el-button type="primary" :loading="loading" style="margin-top: 10px;" @click="refreshPinterDevices">刷新打印机列表</el-button>
        <el-button type="primary" :loading="loading" style="margin-top: 10px;margin-left: 10px" @click="refreshPinterParams">刷新该打印机参数</el-button>
      </el-form-item>
      <el-form-item label="纸盒" prop="dmDefaultSource">
        <el-select v-model="printerSettings.dmDefaultSource" placeholder="自动">
          <el-option
              v-if="params"
              v-for="(value, key) in params.Capabilities.Bins"
              :key="value"
              :label="key"
              :value="value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="打印纸张尺寸" prop="dmPaperSize">
        <el-select v-model="printerSettings.dmPaperSize" placeholder="自动">
          <el-option
              v-if="params"
              v-for="(value, key) in params.Capabilities.Papers"
              :key="value"
              :label="key"
              :value="value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="showAdvance" label="打印纸张类型" prop="dmMediaType" >
        <el-select v-model="printerSettings.dmMediaType" placeholder="选择纸张类型">
          <el-option
              v-if="params"
              v-for="(value, key) in params.Capabilities.MediaTypes"
              :key="value"
              :label="key"
              :value="value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="showAdvance" label="打印纸张方向" prop="dmOrientation">
        <el-select v-model="printerSettings.dmOrientation" placeholder="选择纸张方向">
          <el-option label="竖向" :value="1"></el-option>
          <el-option label="横向" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="showAdvance" label="打印份数" prop="dmCopies">
        <el-input-number v-model="printerSettings.dmCopies" :min="1" label="打印份数" />
      </el-form-item>
      <el-form-item v-if="showAdvance" label="打印颜色" prop="dmColor">
        <el-select v-model="printerSettings.dmColor" placeholder="选择打印颜色">
          <el-option label="黑白" :value="1"></el-option>
          <el-option label="彩色" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="showAdvance" label="双面打印" prop="dmDuplex">
        <el-select v-model="printerSettings.dmDuplex" placeholder="选择双面打印选项">
          <el-option label="关闭" :value="1"></el-option>
          <el-option label="长边" :value="2"></el-option>
          <el-option label="短边" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="showAdvance" label="自动旋转功能" prop="jpAutoRotate">
        <el-switch v-model="printerSettings.jpAutoRotate" :active-value="1" :inactive-value="0" />
      </el-form-item>
      <el-form-item v-if="showAdvance" label="文档逆序功能" prop="pdfRev">
        <el-switch v-model="printerSettings.pdfRev" :active-value="1" :inactive-value="0" />
      </el-form-item>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" text icon="Setting" @click="showAdvance = !showAdvance">{{showAdvance ? '隐藏高级设置':'高级设置'}}</el-button>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script>
import {ElLoading, ElMessageBox} from 'element-plus'
export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      printerList: [], // 用于存储从接口获取的打印机设备列表
      printerSettings: {
        dmDefaultSource: null,
        print: true,
        device: {
          deviceId: null
        }, // 用于存储选中的打印机ID
        dmPaperSize: null, // 默认 A4
        dmOrientation: 1, // 默认竖向
        dmCopies: 1, // 默认打印份数 1
        dmColor: 1, // 默认黑白
        dmDuplex: 1, // 默认关闭双面打印
        jpAutoRotate: 0, // 默认关闭自动旋转
        pdfRev: null, // 默认文档逆序功能为 null
        dmMediaType: null
      },
      params: null,
      loadingInstance: null,
      showAdvance: false
    };
  },
  watch: {
    // 监视 printerSettings.device.deviceId 的变化
    'printerSettings.device.deviceId': {
      handler(newVal, oldVal) {
        let params = null;
        for (let printer of this.printerList) {
          if(newVal === printer.deviceId) {
            params = printer.params;
          }
        }
        this.params = params;
      },
      immediate: true, // 组件创建时立即调用 handler
      deep: false // 不是深度监视
    }
  },
  methods: {
    refreshPinterParams() {
      this.$axios.get('/api/remote/file/refreshPinterParams?deviceId=' + this.printerSettings.device.deviceId).then(() => {
        this.$message.success("刷新成功")
        this.fetchPrinters()
      })
    },
    async checkNeedResetName() {
      for (const printer of this.printerList) {
        if (printer.needResetName === 1) {
          try {
            // 弹出输入框让用户输入新的名称
            const { value: newName } = await ElMessageBox.prompt(
                `请输入打印机 "${printer.deviceId}" 的新名称:`,
                '重置打印机名称',
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  inputPlaceholder: '请输入新名称',
                  inputValidator: (value) => {
                    if (!value) {
                      return '名称不能为空';
                    }
                    return true;
                  },
                  // 可以根据需要调整图标
                  // showInput: true, // 已经通过 prompt 自动包含输入框
                }
            );

            if (newName) {
              this.loading = true;
              // 调用API接口更新打印机名称
              const res = await this.$axios.get('/api/remote/file/changePinterDevicesName', {
                params: {
                  deviceId: printer.deviceId,
                  newName: newName,
                },
              });
              this.fetchPrinters()
            }
          } catch (error) {
            if (error !== 'cancel') {
              this.$message.error('发生错误: ' + (error.message || error));
            }
            // 用户取消输入时不做任何处理
          } finally {
            this.loading = false;
          }

          // 处理完一个需要重置名称的打印机后退出循环
          break;
        }
      }
    },
    getLabelName(device) {
      var status = device.online === 1 ? '在线' : '离线';
      return device.deviceName + "-" + status;
    },
    refreshPinterDevices() {
      this.loadingInstance = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      this.loading = true;
      this.$axios.get('/api/remote/file/refreshPinterDevices')
          .then((res) => {
            this.printerList = [];
            if (res.data && Array.isArray(res.data)) {
              this.printerList = res.data;
              this.checkNeedResetName()
            }
            this.$message.success("刷新成功")
          })
          .finally(() => {
            this.loading = false;
            this.loadingInstance.close();
          });
    },
    fetchPrinters() {
      this.loadingInstance = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      this.loading = true;
      this.$axios.get('/api/remote/file/getPrintDevices')
          .then((res) => {
            if (res.data && Array.isArray(res.data)) {
              this.printerList = res.data;
              if (res.data.length > 0 && !this.printerSettings?.device.deviceId) {
                this.printerSettings.device.deviceId = res.data[0].deviceId;
              }
              this.checkNeedResetName()
            }
          })
          .finally(() => {
            this.loading = false;
            this.loadingInstance.close();
          });
    },
    show() {
      this.dialogVisible = true;
      this.fetchPrinters();
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    submitForm() {
      this.$emit('printerSettingsSubmit', this.printerSettings, this.toString());
      this.closeDialog();
    },
    toString() {
      const { device, dmPaperSize, dmOrientation, dmCopies, dmColor, dmDuplex, jpAutoRotate, pdfRev } = this.printerSettings;

      // 条件拼接值，忽略"关闭"和"未知"的值
      const paperSize = dmPaperSize === 9 ? 'A4' : dmPaperSize === 11 ? 'A5' : null;
      const orientation = dmOrientation === 1 ? '竖向' : dmOrientation === 2 ? '横向' : null;
      const color = dmColor === 1 ? '黑白' : dmColor === 2 ? '彩色' : null;
      const duplex = dmDuplex === 1 ? null : dmDuplex === 2 ? '双面打印-长边' : dmDuplex === 3 ? '双面打印-短边' : null;
      const autoRotate = jpAutoRotate === 1 ? '自动旋转' : null;
      const rev = pdfRev === 1 ? '文档逆序' : null;

      // 只拼接非 null 和非"关闭"的值
      const settings = [];
      if (device.deviceId) {
        const selectPrinter = this.printerList.find((item) => item.deviceId === device.deviceId);
        if (selectPrinter) {
          settings.push(this.$t('printList.' + selectPrinter.deviceName));
        }
      }
      if (paperSize) settings.push(paperSize);       // 纸张尺寸
      if (orientation) settings.push(orientation);   // 纸张方向
      if (dmCopies) settings.push(`${dmCopies}份`);  // 打印份数
      if (color) settings.push(color);               // 打印颜色
      if (duplex) settings.push(duplex);             // 双面打印
      if (autoRotate) settings.push(autoRotate);     // 自动旋转
      if (rev) settings.push(rev);                   // 文档逆序功能

      // 拼接并返回字符串
      return settings.join(', ');
    },
  },
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
