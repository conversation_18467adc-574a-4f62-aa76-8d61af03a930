<template>
    <div class="main-wrapper">
        <div class="header-bar">
            <el-select style="width: 150px;" v-model="aimodel">
                <el-option label="gpt-4-vision" value="gpt-4-vision-preview" />
                <el-option label="Claude 3 Opus" value="Claude 3 Opus" />
                <el-option label="Claude 3 Sonnet" value="Claude 3 Sonnet" />
                <el-option label="gpt-4-turbo" value="gpt-4-turbo" />
                <el-option label="gpt-4" value="gpt-4" />
                <el-option label="gpt-3.5-turbo" value="gpt-3.5-turbo" />
                <el-option label="ERNIE-Bot" value="ERNIE-Bot" />
                <el-option label="qwen-turbo" value="qwen-turbo" />
                <el-option label="qwen-plus" value="qwen-plus" />
                <el-option label="qwen-max" value="qwen-max" />
                <el-option label="gemma:2b" value="gemma:2b" />
                <el-option label="llama3" value="llama3" />
            </el-select>
            <el-select v-show="showOCR" style="width: 150px; margin-left: 5px;" v-model="ocrType">
                <el-option label="MathPix OCR" value="1" />
                <el-option label="Tencent OCR" value="2" />
                <el-option label="gpt-4-vision" value="3" />
                <el-option label="PaddleOCR" value="4" />
            </el-select>
            <div class="header-bar-action">
                <div class="review-stat">
                    <div class="review-stat-card">
                        <el-text class="value" type="success">{{ reviewStats.progress || '-' }}</el-text>
                        <el-text class="label">{{ $t('common.progress') }}</el-text>
                    </div>
                    <div class="review-stat-card">
                        <el-text class="value" type="danger">{{ reviewStats.errorDoc || '-' }}</el-text>
                        <el-text class="label">{{ $t('common.error') }}</el-text>
                    </div>
                </div>
                <el-button type="primary" icon="Setting" @click="$router.push('/docreview')">{{
                    $t('docReview.docConfig') }}</el-button>
                <el-upload class="header-action-upload" :action="$fileserver.multipdfUrl" accept=".pdf" multiple
                    :with-credentials="true" :show-file-list="false" :on-success="uploadDoc">
                    <el-button type="primary" icon="Plus">{{ $t('docReview.docAdd') }}</el-button>
                </el-upload>
                <el-dropdown @command="downloadReviewed">
                    <el-button type="primary" :icon="reviewDownloading ? 'Loading' : 'Download'"
                        @click="downloadReviewed('1')">
                        {{ $t('docReview.reviewWithoutDoc') }}
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="1">{{ $t('common.ascOrder') }}</el-dropdown-item>
                            <el-dropdown-item command="2">{{ $t('common.descOrder') }}</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-dropdown @command="downloadPreview">
                    <el-button type="primary" :icon="previewDownloading ? 'Loading' : 'Download'"
                        @click="downloadReviewed('1', { isPreview: true, filename: '预览文件.pdf', loading: 'previewDownloading' })">
                        {{ $t('docReview.reviewWithDoc') }}
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="single">{{ $t('common.singlePage') }}</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-button type="primary" :icon="statsDownloading ? 'Loading' : 'Download'"
                    @click="downloadReviewStats">
                    {{ $t('docReview.statsRes') }}
                </el-button>
                <el-button type="primary" @click="doBatchSave">{{ $t('common.save') }}</el-button>
                <el-button type="primary" @click="batchReview">{{ batchId ? `${$t('common.reviewing')}...` :
                    $t('common.review')
                    }}</el-button>
            </div>
        </div>
        <div class="main-content">
            <el-table :data="docs" :border="true" height="100%" :empty-text="$t('docReview.pleaseAddDoc')"
                style="background: transparent">
                <el-table-column v-for="column in columns" v-bind="column" :key="column.prop" :label="$t(column.t)">
                    <template v-if="column.prop === 'doc'" v-slot="scope">
                        <el-link type="primary" :underline="false" :href="$fileserver.fileurl(scope.row.fileurl)"
                            target="_blank">{{ scope.row.filename }}</el-link>
                    </template>
                    <template v-else-if="column.prop === 'operations'" v-slot="scope">
                        <el-button text v-if="!reviewIds.has(scope.row.id)" size="small"
                            @click="review(scope.row.id)">{{ $t('common.review') }}</el-button>
                        <el-button text v-else size="small">{{ `${$t('common.reviewing')}...` }}</el-button>
                        <el-button text size="small" @click="doDelete(scope.row.id)">{{ $t('common.delete')
                            }}</el-button>
                        <!-- <el-link style="margin-left: 10px" type="primary" :underline="false" v-if="scope.row.status === 3"
                            :href="$fileserver.fileurl(scope.row.reviewDoc)" target="_blank">下载</el-link> -->
                        <el-button text size="small" v-if="scope.row.status === 3" style="margin-left: 10px"
                            @click="docReviewed(scope.row)">{{ $t('common.download') }}</el-button>
                        <el-button text size="small" v-if="scope.row.status === 3" style="margin-left: 10px"
                            @click="docPreview(scope.row)">{{ $t('common.preview') }}</el-button>
                        <el-button text size="small" v-if="scope.row.status === 3" style="margin-left: 10px"
                            @click="doSave(scope.row)">{{ $t('common.save') }}</el-button>
                    </template>
                    <template v-else-if="column.prop === 'progress'" v-slot="scope">
                        <span>{{ progress(scope.row) }}</span>
                    </template>
                    <template v-else-if="column.prop === 'correctRate'" v-slot="scope">
                        <span>{{ correctRate(scope.row) }}</span>
                    </template>
                    <template v-else-if="column.type === 'dic'" v-slot="scope">
                        <el-tag :type="column.options[scope.row[column.prop]].type">
                            {{ $t(column.options[scope.row[column.prop]].t) }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import {useUserStore} from "@/store";

const store = useUserStore();
export default {
    props: {},
    data() {
        return {
            questions: [],
            docIdArea: null,
            docname: '',
            fontSize: 15,
            signSize: 40,
            docType: store.getDefaultDocType,
            docurl: '',
            libraryId: '',
            columns: [
                { label: '试卷', prop: 'doc', align: 'center', t: 'common.document' },
                {
                    label: '状态', prop: 'status', align: 'center', type: 'dic', t: 'common.status', options: {
                        1: { label: '未批改', t: 'common.unReview' },
                        2: { label: '批改中', type: 'warning', t: 'common.reviewing' },
                        3: { label: '已批改', type: 'success', t: 'common.reviewed' },
                        4: { label: '批改失败', type: 'danger', t: 'common.reviewFail' },
                    }
                },
                { label: '学生姓名', prop: 'identify', align: 'center', t: 'docReview.studentName' },
                { label: '进度', prop: 'progress', align: 'center', width: "100px", t: 'common.progress' },
                { label: '正确率', prop: 'correctRate', align: 'center', width: "150px", t: 'common.correctRate' },
                { label: '操作', prop: 'operations', align: 'center', width: "300px", t: 'common.operations' },
            ],
            docs: [],
            reviewIds: new Set(),
            timer: null,
            batchId: null,
            aimodel: 'gpt-4-vision-preview',
            ocrType: '4',
            reviewDownloading: false,
            previewDownloading: false,
            statsDownloading: false,
            isScore: false,
            scoreArea: null,
        }
    },
    beforeRouteEnter(to, from, next) {
        if (to.meta.docContext) {
            next(vm => {
                vm.questions = to.meta.docContext.questions
                vm.docIdArea = to.meta.docContext.docIdArea
                vm.docname = to.meta.docContext.docname
                vm.fontSize = to.meta.docContext.fontSize
                vm.signSize = to.meta.docContext.signSize
                vm.docType = to.meta.docContext.docType
                vm.docurl = to.meta.docContext.docurl
                vm.libraryId = to.meta.docContext.libraryId
                vm.isScore = to.meta.docContext.isScore
                vm.scoreArea = to.meta.docContext.scoreArea
            })
        } else {
            next()
        }
    },
    computed: {
        reviewStats() {
            let total = this.docs?.length || 0
            let reviewed = 0
            let errored = 0
            this.docs.forEach(doc => {
                if (doc.status === 3 || doc.status === 4) {
                    reviewed++
                }
                if (doc.status === 4) {
                    errored++
                }
            })
            return {
                progress: `${reviewed}/${total}`,
                errorDoc: errored
            }
        },
        showOCR() {
            return !['gpt-4-vision-preview', 'Claude 3 Opus', 'Claude 3 Sonnet'].includes(this.aimodel)
        }
    },
    methods: {
        uploadDoc(response, file) {
            let form = response.data.map((resp, index) => {
                let filename = response.data.length > 1 ? `${file.name.substring(0, file.name.length - 4)}_${index + 1}.pdf` : file.name
                return {
                    filename,
                    fileurl: resp.url,
                    status: 1,
                }
            })
            this.$axios.post("/api/docreview/addBatch", form).then(res => {
                this.docs = this.docs.concat(res.data)
            })
        },
        doDelete(id) {
            this.$axios.get(`/api/docreview/delete?id=${id}`).then(res => {
                this.docs = this.docs.filter(doc => doc.id !== id)
            })
        },
        review(id) {
            if (!this.questions || this.questions.length === 0) {
                this.$message.warning('请添加题目')
                return
            }
            if (this.batchId) {
                this.$message.warning('批改中，请稍后再试')
                return
            }
            this.reviewIds.add(id)
            this.doReview({
                ids: [id]
            }).then(res => {
                this.refreshRes()
            })
        },
        batchReview() {
            if (!this.questions || this.questions.length === 0) {
                return this.$message.warning('请添加题目')
            }
            if (this.reviewIds && this.reviewIds.size > 0) {
                return this.$message.warning('批改中，请稍后再试')
            }
            if (this.docs.length === 0) {
                return this.$message.warning('请添加试卷')
            }
            this.doReview({
                ids: this.docs.map(doc => doc.id)
            }).then(res => {
                this.batchId = res.data.batchId
                this.refreshRes()
            })
        },
        doReview(formData) {
            let form = {
                questions: this.questions,
                model: this.aimodel,
                docIdArea: this.docIdArea,
                ocrType: this.ocrType,
                fontSize: this.fontSize,
                signSize: this.signSize,
                docType: this.docType,
                isScore: this.isScore,
                scoreArea: this.scoreArea,
            }
            form = Object.assign(form, formData)
            return this.$axios.post('/api/docreview/review', form)
        },
        refreshRes() {
            if (!this.timer) {
                this.timer = setInterval(() => {
                    let params = {
                        setProgress: true
                    }
                    if (this.reviewIds && this.reviewIds.size > 0) {
                        params.incluedIds = [...this.reviewIds]
                    } else {
                        params.batchId = this.batchId
                    }
                    this.$axios.post("/api/docreview/list", params).then(res => {
                        let finished = true
                        let idMap = {}
                        res.data.forEach(r => {
                            if (r.status <= 2) {
                                finished = false
                            }
                            if (r.status > 2 && this.reviewIds.has(r.id)) {
                                this.reviewIds.delete(r.id)
                            }
                            idMap[r.id] = r
                        })
                        this.docs = this.docs.map(doc => {
                            if (idMap[doc.id]) {
                                doc = Object.assign(doc, idMap[doc.id])
                                if (doc.reviewed && doc.reviewed !== '') {
                                    doc.reviewedObj = JSON.parse(doc.reviewed)
                                }
                            }
                            return doc
                        })
                        if (this.batchId && finished) {
                            this.batchId = null
                        }
                        if (!this.batchId && this.reviewIds.size == 0) {
                            clearInterval(this.timer)
                            this.timer = null
                            this.$message.success('批改完成')
                        }
                    })
                }, 3 * 1000)
            }
        },
        downloadReviewed(sortOrder, options) {
            let defaultOptions = {
                isPreview: false,
                filename: '批改文件.pdf',
                loading: 'reviewDownloading'
            }
            options = Object.assign(defaultOptions, options);

            if (this.reviewIds && this.reviewIds.size > 0) {
                return this.$message.warning('批改中，请稍后再试')
            }
            if (this.batchId) {
                this.$message.warning('批改中，请稍后再试')
                return
            }
            if (this.docs.length === 0) {
                this.$message.warning('请添加试卷')
                return
            }
            let hasFinished = false
            let ids = this.docs.map(doc => {
                if (doc.status !== 3) {
                    hasFinished = true
                }
                return doc.id
            })
            if (hasFinished) {
                this.$message.warning('存在未批改成功的试卷')
                return
            }

            // 反序
            if (sortOrder === '2') {
                ids = ids.reverse()
            }

            this[options.loading] = true
            this.$axios.post("/api/docreview/batchReviewDoc", {
                ids,
                fontSize: this.fontSize,
                signSize: this.signSize,
                isPreview: options.isPreview,
                docType: this.docType,
                isScore: this.isScore,
                scoreArea: this.scoreArea,
            }).then(res => {
                this.downloadFile(this.$fileserver.fileurl(res.data.fileUrl), options.filename)
            }).finally(() => this[options.loading] = false)
        },
        downloadFile(url, name) {
            // 使用fetch获取文件内容
            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    // 如果需要下载，可以使用前面提到的下载代码
                    const a = document.createElement("a");
                    a.style.display = "none";
                    a.href = URL.createObjectURL(blob);
                    a.download = name;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                })
                .catch(error => {
                    console.error('发生错误:', error);
                });
        },
        downloadReviewStats() {
            if (this.reviewIds && this.reviewIds.size > 0) {
                return this.$message.warning('批改中，请稍后再试')
            }
            if (this.batchId) {
                this.$message.warning('批改中，请稍后再试')
                return
            }
            if (this.docs.length === 0) {
                this.$message.warning('请添加试卷')
                return
            }
            let hasFinished = false
            let ids = this.docs.map(doc => {
                if (doc.status !== 3) {
                    hasFinished = true
                }
                return doc.id
            })
            if (hasFinished) {
                this.$message.warning('存在未批改成功的试卷')
                return
            }
            this.statsDownloading = true
            this.$axios.post("/api/docreview/reviewStatsDoc", {
                docname: (this.docname && this.docname !== '') ? this.docname : '试卷',
                questions: this.questions,
                ids,
                signSize: this.signSize,
                docType: this.docType,
                docurl: this.docurl,
            }).then(res => {
                this.downloadFile(this.$fileserver.fileurl(res.data.fileUrl), "试卷报告.pdf")
            }).finally(() => this.statsDownloading = false)
        },
        docPreview(row) {
            let form = {
                ids: [row.id],
                fontSize: this.fontSize,
                signSize: this.signSize,
                isPreview: true,
                docType: this.docType,
                isScore: this.isScore,
                scoreArea: this.scoreArea,
            }
            return this.$axios.post("/api/docreview/batchReviewDoc", form).then(res => {
                this.downloadFile(this.$fileserver.fileurl(res.data.fileUrl), `${row.filename.substring(0, row.filename.lastIndexOf('.'))}_预览.pdf`)
            })
        },
        docReviewed(row) {
            this.downloadFile(this.$fileserver.fileurl(row.reviewDoc), `${row.filename.substring(0, row.filename.lastIndexOf('.'))}_批改.pdf`)
        },
        downloadPreview(type) {
            let docs = this.docs.filter(doc => doc.status === 3)
            if (docs.length === 0) {
                this.$message.warning('没有已批改的试卷')
                return
            }
            let count = 0
            let activeCalls = 0
            let maxConcurrentCalls = 5
            let timer = setInterval(() => {
                if (count >= docs.length) {
                    if (activeCalls === 0) {
                        clearInterval(timer)
                    }
                    return
                }
                if (activeCalls < maxConcurrentCalls) {
                    let doc = docs[count]
                    activeCalls++
                    this.docPreview(doc).finally(() => {
                        activeCalls--
                    })
                    count++
                }
            }, 1000)
        },
        progress(row) {
            let progress = row.progress
            if (!progress) {
                return
            }
            return `${progress.reviewedNum}/${progress.questionNum}`
        },
        correctRate(row) {
            if (row.status !== 3 || !row.reviewedObj) {
                return ""
            }
            let questionNum = row.reviewedObj.length
            let correct = 0;
            row.reviewedObj.forEach((item) => {
                if (!item.status || item.review.isTrue) {
                    correct++
                }
            })
            return `${(correct / questionNum * 100).toFixed(2)}%`
        },
        doSave(row) {
            this.save2Library({
                reviewIds: [row.id]
            })
        },
        doBatchSave() {
            let reviewIds = this.docs.map(doc => doc.id)
            this.save2Library({
                reviewIds,
            })
        },
        save2Library(params) {
            let form = Object.assign({
                libraryId: this.libraryId
            }, params)
            this.$axios.post("/api/docreviewrec/batch/add", form).then(res => {
                this.$message.success("保存成功")
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.main-wrapper {
    height: 100%;

    .header-bar {
        display: flex;
        align-items: center;

        .header-bar-action {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 10px;

            .el-button+.el-button {
                margin-left: 0;
            }

            .review-stat {
                display: flex;
                column-gap: 5px;
                margin-right: 5px;

                .review-stat-card {
                    display: flex;
                    flex-direction: column;
                    text-align: center;
                    padding: 0 10px;

                    .label {
                        font-size: 12px;
                        color: var(--el-text-color-secondary);
                    }

                    .value {
                        font-size: 20px;
                        font-weight: bold;
                    }
                }
            }
        }
    }

    .main-content {
        height: calc(100% - 60px);
    }
}
</style>