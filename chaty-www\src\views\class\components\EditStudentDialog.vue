<template>
  <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑学生信息' : '新增学生信息'"
      :before-close="handleClose"
      width="400px"
      :close-on-click-modal="false"
  >
    <el-form
        :model="form"
        ref="formRef"
        label-width="80px"
        class="form-container"
    >
      <el-form-item
          label="学生姓名"
          prop="nickname"
          :rules="[
          { required: true, message: '请输入学生姓名', trigger: 'blur' },
          { min: 2, max: 30, message: '姓名长度在 2 到 30 个字符之间', trigger: 'blur' }
        ]"
      >
        <el-input
            v-model="form.nickname"
            placeholder="请输入学生姓名"
            clearable
        />
      </el-form-item>

      <el-form-item
          label="学号"
          prop="studentId"
          :rules="[
          { required: true, message: '请输入学号', trigger: 'blur' },
          { pattern: /^[A-Za-z0-9]+$/, message: '学号只能包含字母和数字', trigger: 'blur' }
        ]"
      >
        <el-input
            v-model="form.studentId"
            placeholder="请输入学号"
            clearable
        />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button class="button" type="primary" :loading="loading" @click="submitForm">
        保存
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { ElMessage } from "element-plus";

export default {
  name: "EditStudentDialog",
  data() {
    return {
      dialogVisible: false,
      form: {
        nickname: "",
        studentId: "",
        classId: ""
      },
      loading: false,
      studentIdOriginal: null, // 用于存储原始学生ID，以便后端识别
      isEdit: false // 标识当前操作是编辑还是新增
    };
  },
  methods: {
    /**
     * 打开对话框并填充学生数据
     * @param {Object|null} student - 要编辑的学生对象，新增时传入 null 或者没有 id
     */
    openDialog(student) {
      console
      if (student && student.id) {
        // 编辑操作
        this.isEdit = true;
        this.form.nickname = student.nickname || "";
        this.form.studentId = student.studentId || "";
        this.form.classId = student.classId || "";
        this.studentIdOriginal = student.id;
      } else {
        // 新增操作
        this.isEdit = false;
        this.form.classId = student.classId || "";
        this.resetForm();
      }
      this.dialogVisible = true;
    },

    /**
     * 关闭对话框并重置表单
     */
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
      this.$emit('cancel')
    },

    /**
     * 重置表单字段
     */
    resetForm() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
      this.form.nickname = "";
      this.form.studentId = "";
      this.studentIdOriginal = null;
      this.isEdit = false;

      this.$emit('cancel')
    },

    /**
     * 提交编辑或新增表单
     */
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.isEdit) {
            // 编辑操作
            const payload = {
              nickname: this.form.nickname,
              studentId: this.form.studentId,
              id: this.studentIdOriginal // 传递原始学生ID用于后端识别
            };
            this.$axios
                .post(`/api/user/update/nicknameAndStudentId`, payload)
                .then(() => {
                  ElMessage.success("学生信息更新成功");
                  this.dialogVisible = false;
                  this.resetForm();
                  this.$emit("fetchStudents");
                })
                .catch((error) => {
                  console.error("更新学生信息错误：", error);
                  ElMessage.error("更新学生信息失败，请稍后再试");
                })
                .finally(() => {
                  this.loading = false;
                });
          } else {
            // 新增操作
            const payload = [{
              nickname: this.form.nickname,
              studentId: this.form.studentId,
              classId: this.form.classId
            }];
            this.$axios
                .post(`/api/user/teacher/add`, payload) // 假设新增接口为 /api/user/create
                .then(() => {
                  ElMessage.success("学生信息新增成功");
                  this.dialogVisible = false;
                  this.resetForm();
                  this.$emit("fetchStudents");
                })
                .catch((error) => {
                  console.error("新增学生信息错误：", error);
                  ElMessage.error("新增学生信息失败，请稍后再试");
                })
                .finally(() => {
                  this.loading = false;
                });
          }
        } else {
          ElMessage.error("请检查表单填写是否正确");
        }
      });
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.button {
  width: 90px;
  height: 32px;
  background: #3981ff;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  margin-right: 10px;
}

.form-container {
  padding: 20px;
}

.el-dialog .el-dialog__footer {
  text-align: center;
}

.el-button[type="primary"] {
  background-color: #3981ff;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
}

.el-input {
  width: 100%;
}
</style>
