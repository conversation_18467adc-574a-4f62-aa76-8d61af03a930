<template>
  <div>
    <el-table :data="questions" size="small" border>
      <el-table-column type="index" label="序号" width="50" align="center"/>
      <el-table-column v-for="column in columns.filter(col => col.simpleMode || !colMode)" :key="column.prop"
                       v-bind="column" align="center" :width="column.width || ''">
        <template v-if="column.prop === 'operations'" v-slot="scope">
          <el-button v-if="!colMode" text size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button text style="background: #f56c6c;color: white;font-weight: normal;font-size: 13px"
                     size="small" @click="onCorrect(scope.row, scope.row.isEssay)">纠错
          </el-button>
          <el-button type="primary"
                     style="background: #2e9efc;color: white;font-weight: normal;font-size: 13px"
                     size="small">编辑
          </el-button>
        </template>
        <template v-else-if="column.prop === 'area'" v-slot="scope">
          <el-link v-if="areas[scope.row.areaIdx].areaImg" type="primary"
                   :href="$fileserver.fileurl(areas[scope.row.areaIdx].areaImg)" target="_blank">
            {{ `区域${scope.row.areaIdx + 1}` }}
          </el-link>
          <el-text v-else>{{ `区域${scope.row.areaIdx + 1}` }}</el-text>
        </template>
        <template v-else-if="column.prop === 'areaEnabled'" v-slot="scope">
          <el-tag :type="areas[scope.row.areaIdx].enabled === false ? 'warning' : 'primary'">
            {{ areas[scope.row.areaIdx].enabled === false ? '否' : '是' }}
          </el-tag>
        </template>
        <template v-else-if="column.prop === 'isCorrect'" v-slot="scope">
          <el-text v-if="scope.row.isScorePoint === 2 || scope.row.isEssay">{{
              `${scope.row.scored} /
                        ${scope.row.score}`
            }}
          </el-text>
          <el-tag v-else-if="scope.row.isCorrect" :type="scope.row.isCorrect === 'Y' ? 'primary' : 'error'">
            {{ scope.row.isCorrect === 'Y' ? '正确' : '错误' }}
          </el-tag>
        </template>
        <template v-else-if="column.prop === 'status'" v-slot="scope">
          <el-tag v-if="areas[scope.row.areaIdx].status"
                  :type="areas[scope.row.areaIdx].status === '1' ? 'primary' : 'error'">
            <el-text type="primary" v-if="areas[scope.row.areaIdx].status === '1'" size="small">正常</el-text>
            <el-tooltip v-else effect="dark" :content="areas[scope.row.areaIdx].error" placement="top-start"
                        size="small">
              <el-text type="error">异常</el-text>
            </el-tooltip>
          </el-tag>
        </template>
        <template v-else-if="column.prop === 'hasChange'" v-slot="scope">
          <el-tag :type="scope.row.hasChange === 1 ? 'success' : 'primary'">{{
              scope.row.hasChange === 1 ?
                  '已修改' : '未修改'
            }}
          </el-tag>
        </template>
        <template v-else-if="column.prop === 'score'" v-slot="scope">
          <el-text v-if="scope.row.isScorePoint === 2">{{
              `${scope.row.scored} / ${scope.row.score}`
            }}
          </el-text>
          <el-text v-else>{{
              `${scope.row.isCorrect === 'Y' ? scope.row.score : 0} / ${scope.row.score}`
            }}
          </el-text>
        </template>
        <template v-else-if="column.prop === 'answer'" v-slot="scope">
          <el-text v-if="Array.isArray(scope.row.answer) ">{{ scope.row.answer?.join(",") }}</el-text>
          <el-text v-else>{{ scope.row.answer }}</el-text>
        </template>
        <template v-else-if="column.prop === 'studentAnswer'" v-slot="scope">
          <el-text v-if="Array.isArray(scope.row.studentAnswer) ">{{
              scope.row.studentAnswer?.join(",")
            }}
          </el-text>
          <el-text v-else>{{ scope.row.studentAnswer }}</el-text>
        </template>
        <template v-else-if="column.prop === 'prompt'" v-slot="scope">
          <el-button v-if="!areas[scope.row.areaIdx].areaType || areas[scope.row.areaIdx].areaType === 1"
                     text size="small" @click="copyPrompt(areas[scope.row.areaIdx])">复制
          </el-button>
        </template>
        <template v-else-if="column.prop === 'recordId'" v-slot="scope">
          <el-button v-if="!areas[scope.row.areaIdx].areaType || areas[scope.row.areaIdx].areaType === 1"
                     text size="small" @click="copyAjaxBody(record.id ,scope.row.areaIdx)">复制
          </el-button>
        </template>
        <template v-else-if="column.prop === '$response'" v-slot="scope">
          <el-button v-if="!areas[scope.row.areaIdx].areaType || areas[scope.row.areaIdx].areaType === 1"
                     text size="small" @click="copyResponseBody(record.id ,scope.row.areaIdx)">复制
          </el-button>
          <el-button v-if="!areas[scope.row.areaIdx].areaType || areas[scope.row.areaIdx].areaType === 3"
                     text size="small" @click="copyEssayResponseBody(record.id ,scope.row.areaIdx)">复制
          </el-button>
        </template>
        <template v-else-if="column.prop === 'question'" v-slot="scope">
          <el-text v-if="scope.row.isEssay">作文题</el-text>
          <el-text>{{ scope.row.question }}</el-text>
        </template>
        <template v-else-if="column.prop === 'logprob'" v-slot="scope">
          <el-text>{{ scope.row.logprob?.logprob?.toExponential(1) }}</el-text>
        </template>
      </el-table-column>
    </el-table>

    <review-form ref="reviewForm" @updateQs="updateQs"/>


  </div>

</template>

<script>
import ReviewForm from './reviewform.vue'
import {ElLoading} from "element-plus";

export default {
  components: {ReviewForm},
  props: {
    record: {
      type: Object,
      required: true
    },
    task: {
      type: Object,
      default: null,
    },
    config: {
      type: Object,
      default: null,
    },
    showError: {
      type: Boolean,
      default: false,
    },
    showAll: {
      type: Boolean,
      default: false,
    },
    colMode: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      columns: [
        {
          prop: "area",
          label: "图片",
          simpleMode: true,
          width: 60
        },
        {
          prop: "areaEnabled",
          label: "启用"
        },
        {
          prop: "status",
          label: "状态"
        },

        {
          prop: "question",
          label: "问题",
          width: 100,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "answer",
          label: "答案",
          width: 60,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "studentAnswer",
          label: "学生",
          width: 60,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "isCorrect",
          label: "是否正确",
          width: 80,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "operations",
          label: "操作",
          width: 140,
          simpleMode: true,
        },
        // {
        //   prop: "logprob",
        //   label: "logprob",
        //   width: 150,
        //   simpleMode: true,
        // },
        {
          prop: "hasChange",
          label: "是否修改",
          simpleMode: true,
          width: 90
        },
        {
          prop: "review",
          label: "评价",
          width: 200,
          "show-overflow-tooltip": true,
        },
        {
          prop: "prompt",
          label: "提示词",
        },
        {
          prop: "recordId",
          label: "请求体",
        },
        {
          prop: "$response",
          label: "回复体",
        },

      ],
      areas: [],
    }
  },
  computed: {
    questions() {
      let questions = []

      if (this.config) {
        this.areas = JSON.parse(this.config.areas)
        let reviewed = this.record.reviewedObj;
        for (let areaIdx = 0; areaIdx < this.areas.length; areaIdx++) {
          let area = this.areas[areaIdx]
          let areaReviewed = reviewed.find((item) => {
            return item.areaIdx === areaIdx;
          })
          area.areaImg = areaReviewed?.areaImg // 批改图片
          area.status = areaReviewed?.status // 批改状态
          area.error = areaReviewed?.error // 错误
          this.areas[areaIdx] = area;
          for (let qsIdx = 0; qsIdx < area.questions.length; qsIdx++) {
            let question = area.questions[qsIdx]
            let qsReviewed = areaReviewed?.reviewed?.[qsIdx]
            let isTrue = (qsReviewed?.isCorrect || 'Y') === 'Y'
            if (this.showError && !this.showAll && isTrue && area.status === '1') {
              continue
            }
            question = Object.assign(question, {
              areaIdx,
              qsIdx,
            }, qsReviewed)
            if (area.status !== '1') {
              question.review = area.error
            }
            if (area.areaType === 3 && question.review) {
              question.review = JSON.stringify(question.review)
            }
            question.areaType = area.areaType;
            questions.push(question)
          }
        }
      }

      return questions
    },
  },
  watch: {
  },
  created() {

  },
  methods: {
    copyEssayResponseBody(recordId, areaIdx) {
      this.$router.push({
        name: 'requestLog',
        params: {
          recordId: recordId,
          areaIdx:   areaIdx
        }
      });

      // navigator.clipboard.writeText(JSON.stringify(data)).then(() => {
      //   this.$message.success("复制成功")
      // })
      // this.$emit('showJsonDialog', data)
    },
    copyAjaxBody(recordId, areaIdx) {
      this.$router.push({
        name: 'requestLog',
        params: {
          recordId: recordId,
          areaIdx:   areaIdx
        }
      });

      // let loadingInstance = ElLoading.service({
      //   lock: true,
      //   text: 'Loading',
      //   background: 'rgba(0, 0, 0, 0.7)',
      // });
      // this.$axios.get(`/api/docCorrectRecord/area/getChatCompletionDTO?recordId=${recordId}&areaIdx=${areaIdx}`).then((res) => {
      //   navigator.clipboard.writeText(JSON.stringify(res.data)).then(() => {
      //     this.$message.success("复制成功")
      //   })
      //   this.$emit('showJsonDialog', res.data)
      // }).finally(()=>{
      //   loadingInstance.close();
      // })
    },
    copyResponseBody(recordId, areaIdx) {
      this.$router.push({
        name: 'requestLog',
        params: {
          recordId: recordId,
          areaIdx:   areaIdx
        }
      });
      // let reviewedObj = this.record.reviewedObj;
      // console.log('reviewedObj', reviewedObj)
      // let sum = 0;
      // for (let i = 0; i < reviewedObj.length; i++) {
      //   let reviewedObjItem = reviewedObj[i];
      //   let len = reviewedObjItem.reviewed.length || reviewedObjItem.reviewList.length;
      //   if (index < len) {
      //     let str = '';
      //     if (typeof reviewedObjItem.$response === 'string') {
      //       str = reviewedObjItem.$response;
      //     } else {
      //       str = JSON.stringify(reviewedObjItem.$response);
      //     }
      //     navigator.clipboard.writeText(str).then(() => {
      //       this.$message.success("复制成功")
      //     })
      //     this.$emit('showJsonDialog', JSON.parse(str))
      //     break;
      //   }
      //   sum += len;
      // }

    },
    errorCorrectionFromCropper(val) {
      this.onCorrect(this.questions[val.qsIdx], this.questions[val.qsIdx].isEssay, val?.score);
    },
    onEdit(data) {
      this.$refs.reviewForm.show({data})
    },
    updateQs(updated) {
      const {areaIdx, qsIdx} = updated;
      let reviewed = this.record.reviewedObj
      let configQs = this.config.areasObj[areaIdx].questions[qsIdx] || {};
      let configArea = this.config.areasObj[areaIdx] || {};

      // 纠错的信息
      let params = {
        isCorrect: updated.isCorrect,
        scored: updated.scored,
        studentAnswer: updated.studentAnswer,
        review: updated.review,
        hasChange: updated.hasChange
      }
      console.log('params', params)
      console.log('params', reviewed[areaIdx].reviewed[qsIdx])
      reviewed[areaIdx].reviewed[qsIdx] = Object.assign({}, reviewed[areaIdx].reviewed[qsIdx], params)
      console.log('params', reviewed[areaIdx].reviewed[qsIdx])
      let form = {
        id: this.record.id,
        hasChange: 1,
        reviewed: JSON.stringify(reviewed)
      }
      console.log('params', form.reviewed)
      this.$emit("errorCorrection", reviewed);
      this.$axios.post("/api/docCorrectRecord/update", form).then(res => {
        this.$message.success("更新成功");
        this.$refs.reviewForm.onClose()
      })

      this.$emit("onUpdate", {
        id: this.record.id,
        hasChange: 1
      });

      let data = {
        recordId: this.record.id,
        taskId: this.task.id,
        fileId: this.task.fileId,
        configId: this.task.configId,
        docName: this.record.docname,
        areaIdx: areaIdx,
        qsIdx: qsIdx,
        afterErrorCorrectionScore: updated.scored,
        beforeErrorCorrectionScore:  reviewed[areaIdx].reviewed[qsIdx].scored,
        afterErrorCorrectionAnswer: updated.isCorrect,
        beforeErrorCorrectionAnswer: reviewed[areaIdx].reviewed[qsIdx].isCorrect,
        rightAnswer: configQs.answer,
        studentAnswer: reviewed[areaIdx].reviewed[qsIdx].studentAnswer,
        areaType: configArea.areaType,
        areaImgUrl: reviewed[areaIdx].areaImg,
        detail: JSON.stringify({
          configQs: configQs,
          configArea: configArea,
          reviewedArea: reviewed[areaIdx],
          reviewedQs: reviewed[areaIdx].reviewed[qsIdx],
          qsOcrPrompt: this.config.configObj.qsOcrPrompt
        })
      }
      this.$axios.post("/api/errorCorrection/add", data).then(res => {
      });
      this.$forceUpdate()
    },
    onCorrect(data, e, score) {
      if (e) {
        this.$message.warning("该题型无法纠错")
        return;
      }
      if (data.areaType === 4) {
        console.log('onCorrect', data, e, score)
        if (score === null || score === undefined) {
          // 纠错 识别分数类型
          this.$prompt('请输入新的分数', '修改分数', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern: /^[0-9]+(\.[0-9]{1,2})?$/, // 正则，确保是数字且最多两位小数
            inputErrorMessage: '请输入有效的分数（数字，最多两位小数）'
          }).then(({ value }) => {
            // 用户确认输入
            console.log(value)
            if (value !== '') {
              data.scored = Number(value); // 更新分数
              data.hasChange = 1; // 标记已修改
              this.updateQs(data); // 更新问题
            }
          }).catch((e) => {
            console.log(e)
            // 用户点击取消时的处理逻辑
            this.$message.info("已取消修改");
          });
        } else {
          data.scored = Number(score); // 更新分数
          data.hasChange = 1; // 标记已修改
          this.updateQs(data); // 更新问题
        }

      } else {
        data.isCorrect = data.isCorrect === 'Y' ? 'N' : 'Y'
        data.hasChange = data.hasChange === 1 ? 0 : 1
        this.updateQs(data)
      }
    },
    copyPrompt(area) {
      const prompt = area.questions.map(qs => {
        return `## 题目\n${qs.question}\n## 答案的相关信息\n${qs.qsInfo}\n## 正确答案\n${qs.answer}\n## 得分点\n${qs.scorePoints}\n## 题目满分\n${qs.score}`
      }).join(`\n---\n`)
      navigator.clipboard.writeText(prompt).then(() => {
        this.$message.success("复制成功")
      })
    }
  }
}
</script>

<style lang="scss"></style>