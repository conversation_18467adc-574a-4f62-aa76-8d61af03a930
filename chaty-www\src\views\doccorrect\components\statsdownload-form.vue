<template>
  <el-dialog v-model="isShow" :title="title" width="500" :before-close="onClose">
    <el-form :model="formData" label-position="top">
      <el-form-item label="字体大小" prop="fontSize">
        <el-input-number v-model="formData.fontSize" :controls="false"></el-input-number>
      </el-form-item>

      <!-- 自定义分数段 -->
      <el-form-item label="自定义分数段" prop="segments">
        <div class="segments-container">
          <div
              v-for="(seg, index) in formData.segments"
              :key="index"
              class="segment-row"
          >
            <el-input-number
                v-model="seg.min"
                :min="0"
                :max="fullScore"
                :controls="false"
                style="width:80px;"
                placeholder="开始分"
            ></el-input-number>
            <span style="margin:0 4px;">-</span>
            <el-input-number
                v-model="seg.max"
                :min="seg.min"
                :max="fullScore"
                :controls="false"
                style="width:80px;"
                placeholder="结束分"
            ></el-input-number>
            <!-- <el-input
                v-model="seg.name"
                placeholder="名称"
                style="flex:1; margin:0 8px;"
            ></el-input> -->
            <el-button
                text
                icon="Delete"
                @click="removeSegment(index)"
            ></el-button>
          </div>
        </div>
        <el-button type="primary" plain @click="addSegment">新增分数段</el-button>
      </el-form-item>

      <el-form-item label="保存到远程文件夹" prop="save2Remote">
        <el-checkbox v-model="formData.save2Remote">保存到远程文件夹</el-checkbox>
      </el-form-item>

      <el-form-item label="是否将小数保存为整数（否则保留1位小数）" prop="isSaveInteger">
        <el-checkbox v-model="formData.isSaveInteger">是否将小数保存为整数（否则保留1位小数）</el-checkbox>
      </el-form-item>

      <el-form-item label="识别分数类型是否显示平均分" prop="scorePointTypeShowAverageScore">
        <el-checkbox v-model="formData.scorePointTypeShowAverageScore">识别分数类型是否显示平均分</el-checkbox>
      </el-form-item>

      <el-form-item label="远程打印" prop="printer">
        <el-checkbox v-model="formData.printerProps.print" @change="openPrinterSetting">远程打印</el-checkbox>
        <span style="margin-left: 10px">{{ printerSettingStr }}</span>
      </el-form-item>

      <el-form-item label="合并试卷" prop="tasks" v-if="showTasks">
        <el-select
            style="margin-bottom: 10px"
            suffix-icon="Search"
            placeholder="请输入试卷名称"
            remote
            filterable
            :remote-method="loadTaskOptions"
            :loading="taskLoading"
            @change="onTaskSelect"
            :model-value="taskName"
        >
          <el-option
              v-for="item in taskOptions"
              :key="item.id"
              :label="item.name"
              :value="item"
              :disabled="!!formData.tasks.find(t => t.id === item.id)"
          />
        </el-select>
        <el-table :data="formData.tasks" border empty-text="无数据">
          <el-table-column prop="name" label="名称" align="center" />
          <el-table-column prop="configName" label="试卷配置" align="center" />
          <el-table-column prop="operations" label="操作" align="center">
            <template #default="{ row }">
              <el-button
                  text
                  icon="Delete"
                  @click="formData.tasks.splice(formData.tasks.indexOf(row), 1)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button type="primary" @click="onSubmit" :loading="submiting">确认</el-button>
    </template>

    <printer-setting
        ref="printerSettingRef"
        @printerSettingsSubmit="printerSettingsSubmit"
    ></printer-setting>
  </el-dialog>
</template>

<script>
import PrinterSetting from "@/views/common/PrinterSetting.vue";

export default {
  components: { PrinterSetting },
  props: {
    submiting: {
      type: Boolean,
      default: false,
    },
    showTasks: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      title: "统计结果下载",
      isShow: false,
      fullScore: 0,       // 用于限制区间输入上限
      defaultStep: 10,    // 默认每段 10 分
      formData: {
        fontSize: 20,
        tasks: [],
        save2Remote: false,
        printerProps: { print: false },
        segments: [],
        isSaveInteger: true,
        scorePointTypeShowAverageScore: false,
      },
      taskLoading: false,
      taskOptions: [],
      taskName: "",
      printerSettingStr: "",
    };
  },
  watch: {
    submiting(val) {
      if (!val) {
        this.onClose();
      }
    },
  },
  methods: {
    // 新增默认或指定满分时调用
    show(data, maxScore = 100) {
      this.formData = data;
      this.fullScore = maxScore || this.defaultStep;

      if (!this.formData.segments || this.formData?.segments?.length === 0) {
        this.formData.segments = [];
        for (let start = 0; start < this.fullScore; start += this.defaultStep) {
          const end = Math.min(start + this.defaultStep - 1, this.fullScore - 1);
          this.formData.segments.push({ min: start, max: end, name: "" });
        }
      }

      this.isShow = true;
    },
    addSegment() {
      const lastSegment = this.formData.segments[this.formData.segments.length - 1];
      const newMin = lastSegment ? lastSegment.max + 1 : 0;
      const newMax = Math.min(newMin + this.defaultStep - 1, this.fullScore - 1);
      
      this.formData.segments.push({
        min: newMin,
        max: newMax,
        name: "",
      });
    },
    removeSegment(index) {
      this.formData.segments.splice(index, 1);
    },
    printerSettingsSubmit(val, str) {
      this.formData.printerProps = val;
      this.printerSettingStr = str;
    },
    openPrinterSetting() {
      if (this.formData.printerProps.print) {
        this.$refs.printerSettingRef.show();
      }
    },
    loadTaskOptions(query) {
      if (!query) return;
      this.taskName = query;
      this.taskLoading = true;
      const form = {
        name: query,
        status: 3,
        page: { size: -1, searchCount: false },
      };
      this.$axios
          .post(`/api/docCorrectTask/page`, form)
          .then((res) => {
            this.taskOptions = res.data.records;
          })
          .finally(() => {
            this.taskLoading = false;
          });
    },
    onTaskSelect(val) {
      this.formData.tasks.push(val);
    },
    onSubmit() {
      if (this.showTasks && this.formData.tasks.length === 0) {
        this.$message.error("请添加试卷");
        return;
      }
      this.$emit("submit", this.formData);
      this.onClose();
    },
    onClose() {
      this.formData = {
        fontSize: 20,
        tasks: [],
        save2Remote: false,
        printerProps: { print: false },
        segments: [],
      };
      this.taskName = "";
      this.isShow = false;
      this.printerSettingStr = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.segments-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.segment-row {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
