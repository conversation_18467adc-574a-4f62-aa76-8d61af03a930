<template>
  <div>
    <el-table :data="data" style="height: 100%" empty-text="无数据" border>
      <el-table-column
          prop="name"
          label="试卷名称"
          align="center"
      >
        <template v-slot="scope">
          <el-popover
              trigger="hover"
              v-slot:reference
              :content="scope.row.name"
          >
          <div class="name-cell">
            {{ scope.row.name }}
          </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="modelName" label="模型名称" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.modelName}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="errorCorrectionNumber" label="纠错数" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.errorCorrectionNumber }}/{{ scope.row.questionNumber}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="accuracy" label="正确率" align="center">
        <template v-slot="scope">
          <span>{{ (scope.row.accuracy * 100).toFixed(2) }}%</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style scoped>
.name-cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 最多显示两行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分用省略号表示 */
  font-size: 12px;
}
</style>
