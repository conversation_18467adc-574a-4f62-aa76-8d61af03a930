<template>
    <div class="main-wrapper">
        <el-page-header class="header-bar" @back="goBack">
        </el-page-header>
        <div class="main-content">
            <el-table v-loading="loading" :data="tableData" style="height: 100%" empty-text="无数据" :border="false">
                <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center"
                    :fixed="column.prop === 'operations' ? 'right' : ''">
                    <template v-if="column.prop === 'operations'" v-slot="scope">
                        <el-space :size="5">
                            <el-link type="primary" @click="onDelete(scope.row.id)">删除</el-link>
                        </el-space>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="footer-bar">
            <el-pagination background layout="prev, pager, next" v-model:page-size="pageSize"
                v-model:current-page="pageNumber" :total="total" @current-change="loadData" />
        </div>
    </div>
</template>

<script>

export default {
    components: {
    },
    data() {
        return {
            tableData: [],
            columns: [
                { label: "序号", type: "index", width: 100, align: "center" },
                { label: "姓名", prop: "name", width: 150, "show-overflow-tooltip"	: true },
                { label: "联系电话", prop: "phone", width: 150, "show-overflow-tooltip"	: true },
                { label: "电子邮箱", prop: "email", width: 150, "show-overflow-tooltip"	: true },
                { label: "所在学校", prop: "school", "show-overflow-tooltip"	: true },
                { label: "任教学科", prop: "subject", "show-overflow-tooltip"	: true },
                { label: "教学年级", prop: "grade", "show-overflow-tooltip"	: true },
                { label: "使用场景", prop: "scenarios", width: 200, "show-overflow-tooltip"	: true },
                { label: "功能点", prop: "features", width: 200, "show-overflow-tooltip"	: true },
                { label: "需求建议", prop: "comments", width: 200, "show-overflow-tooltip"	: true },
                { label: "提交时间", prop: "createTime", width: 200 },
                { label: "操作", prop: "operations", width: 100, fixed: 'right' },
            ],
            pageNumber: 1,
            pageSize: 10,
            total: 0,
            loading: false,
        }
    },
    created() {
        this.loadData();
    },
    methods: {
        loadData() {
            this.loading = true;
            this.$axios.post("/api/survey/page", {
                page: {
                    pageNumber: this.pageNumber,
                    pageSize: this.pageSize,
                }
            }).then(res => {
                this.tableData = res.data.records;
                this.total = res.data.total;
            }).finally(() => {
                this.loading = false;
            });
        },
        onDelete(id) {
            this.$axios.get(`/api/survey/delete?id=${id}`).then(() => {
                this.$message.success("删除成功");
                this.loadData();
            });
        }
    },
}
</script>

<style lang="scss" scoped>
.main-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header-bar {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        height: 48px;
        flex-shrink: 0;
        .header-form {
            :deep(.el-form-item) {
                margin-bottom: 0;
            }
        }
    }

    .main-content {
        flex: 1;
    }

    .footer-bar {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
}
</style>