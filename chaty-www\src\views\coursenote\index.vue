<template>
    <div class="container-wrapper">
        <el-card class="left-bar">
            <template #header>
                <div class="card-header">
                    <span>{{ $t('common.config') }}</span>
                    <el-radio-group class="type-radio" :model-value="typeName" @change="onTypeChange">
                        <el-radio-button :label="$t('common.library')" />
                        <el-radio-button :label="$t('common.bigModel')" />
                    </el-radio-group>
                </div>
            </template>
            <div class="library-container" v-if="type === 0">
                <!-- 知识库 -->
                <el-input v-model="library.search" :placeholder="$t('common.pleaseEnter', [$t('common.question')])" class="search-bar" size="large" suffix-icon="search"
                    @change="onSearchChange">
                    <template #prepend>
                        <el-select size="large" @change="onSearchChange" v-model="searchType" style="width: 110px">
                            <el-option :label="$t('common.all')" value="" />
                            <el-option :label="$t('common.library')" value="0" />
                            <el-option :label="$t('common.knowledge')" value="1" />
                            <el-option :label="$t('coursenote.docReview')" value="2" />
                        </el-select>
                    </template>
                </el-input>
                <el-scrollbar>
                    <div class="content-list" v-loading="libraryLoading">
                        <transition-group name=".el-zoom-in-top">
                            <el-card v-for="library in librarys" :key="library.id" class="content-list-item">
                                <template #header>
                                    <div class="box-card-header">
                                        <span>{{ libraryTypeName(library.type) }}</span>
                                        <el-button type="primary" style="width: 70px"
                                            @click="addToNotes(library)">{{ $t('common.select') }}</el-button>
                                    </div>
                                </template>
                                <el-scrollbar max-height="200px" style="user-select: text;">
                                    <el-text v-if="library.type === 0">
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.question')}：` }}</el-text>
                                        <el-text class="text-content" tag="p">{{ library.question }}</el-text>
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.answer')}：` }}</el-text>
                                        <el-text class="text-content" tag="p">{{ library.answer }}</el-text>
                                        <template v-if="library.knowledge">
                                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.knowledge')}：` }}</el-text>
                                            <el-text class="text-content" tag="p">{{ library.knowledge }}</el-text>
                                        </template>
                                    </el-text>
                                    <el-text v-else-if="library.type === 1">
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.name')}：` }}</el-text>
                                        <el-text class="text-content" tag="p">{{ library.knowledgeName }}</el-text>
                                        <template v-if="library.knowledge">
                                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.knowledge')}：` }}</el-text>
                                            <el-text class="text-content" tag="p">{{ library.knowledge }}</el-text>
                                        </template>
                                    </el-text>
                                    <el-text v-else-if="library.type === 2">
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.name')}：` }}</el-text>
                                        <el-text class="text-content" tag="p">{{ library.question }}</el-text>
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.question')}：` }}</el-text>
                                        <el-text class="text-content" tag="p">{{ library.question }}</el-text>
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.correctAnswer')}：` }}</el-text>
                                        <el-text class="text-content" tag="p">{{ library.correctAnswer }}</el-text>
                                        <template v-if="library.knowledge">
                                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.knowledge')}：` }}</el-text>
                                            <el-text class="text-content" tag="p">{{ library.knowledge }}</el-text>
                                        </template>
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('solve.studentAnswer')}：` }}</el-text>
                                        <el-text class="text-content" tag="p">{{ library.answer }}</el-text>
                                        <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.isTrue')}：` }}</el-text>
                                        <el-text class="text-content" :type="library.isTrue ? 'success' : 'danger'"
                                            tag="p">{{ library.isTrue ? '正确' : '错误' }}</el-text>
                                        <template v-if="library.errText">
                                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.error')}：` }}</el-text>
                                            <el-text class="text-content" :type="library.isTrue ? 'success' : 'danger'"
                                                tag="p">{{ library.errText }}</el-text>
                                        </template>
                                        <template v-if="library.comment">
                                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.evaluation')}：` }}</el-text>
                                            <el-text class="text-content" tag="p">{{ library.comment }}</el-text>
                                        </template>
                                    </el-text>
                                </el-scrollbar>
                            </el-card>
                        </transition-group>
                    </div>
                </el-scrollbar>
            </div>
            <div class="model-container" v-show="type === 1">
                <el-scrollbar>
                    <el-form ref="modelForm" class="model-form" :model="model" label-position="top" :rules="modelRules">
                        <el-form-item :label="`${$t('coursenote.modelList')}：`" prop="aimodel">
                            <el-select v-model="model.aimodel" style="width: 100%;">
                                <el-option :label="'gpt-4-turbo'" value="gpt-4-turbo" />
                                <el-option :label="'gpt-4'" value="gpt-4" />
                                <el-option :label="'gpt-3.5-turbo'" value="gpt-3.5-turbo" />
                                <el-option :label="'ERNIE-Bot'" value="ERNIE-Bot" />
                                <!-- <el-option label="ERNIE-Bot-turbo" value="ERNIE-Bot-turbo" />
                            <el-option label="CPM hackthon" value="CPM hackthon" />
                            <el-option label="BLOOMZ-7B" value="BLOOMZ-7B" /> -->
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="`${$t('solve.promotPreview')}：`" prop="promot">
                            <el-input v-model="model.promot" :autosize="{ minRows: 5, maxRows: 7 }" type="textarea" />
                        </el-form-item>
                        <el-form-item :label="`${$t('common.question')}：`" prop="question">
                            <el-input v-model="model.question" :autosize="{ minRows: 5, maxRows: 7 }" type="textarea"
                                :placeholder="$t('common.pleaseEnter', ['common.question'])">
                            </el-input>
                        </el-form-item>
                        <el-form-item :label="`${$t('coursenote.outputRes')}：`" prop="result">
                            <div class="result-card" v-loading="modelLoading">
                                <div v-show="model.status === 1">
                                    <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.answer')}：` }}</el-text>
                                    <custom-input v-model="model.answer" type="textarea"
                                        :autosize="{ minRows: 1 }"></custom-input>
                                    <el-text class="text-content" size="large" tag="p" type="primary">c</el-text>
                                    <custom-input v-model="model.knowledge" type="textarea"
                                        :autosize="{ minRows: 1 }"></custom-input>
                                </div>
                                <div v-show="model.status === 2">
                                    <el-text class="text-content" size="large" tag="p" type="warning">{{ `${$t('coursenote.parseFailed')}：` }}</el-text>
                                    <custom-input v-model="model.answer" type="textarea" color="warning"
                                        :autosize="{ minRows: 1 }"></custom-input>
                                </div>
                                <div v-show="model.status === -1">
                                    <el-text class="text-content" size="large" tag="p" type="danger">{{ `${$t('common.error')}：` }}</el-text>
                                    <custom-input v-model="model.answer" type="textarea" color="danger"
                                        :autosize="{ minRows: 1 }"></custom-input>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item>
                            <div style="width: 100%; text-align: right;">
                                <el-button type="primary" @click="solveQuestion">{{ $t('common.solve') }}</el-button>
                                <el-button type="primary" @click="submitQuestion">{{ $t('common.confirm') }}</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </el-scrollbar>
            </div>
        </el-card>
        <el-card class="right-content">
            <template #header>
                <div class="card-header">
                    <span>{{ $t('menu.coursenote') }}</span>
                    <div class="card-header-right">
                        <div class="review-stat">
                            <div class="review-stat-card">
                                <el-text class="value" type="success">{{ correctNum }}</el-text>
                                <el-text class="label">{{ $t('common.true') }}</el-text>
                            </div>
                            <div class="review-stat-card">
                                <el-text class="value" type="danger">{{ errorNum }}</el-text>
                                <el-text class="label">{{ $t('common.error') }}</el-text>
                            </div>
                            <div class="review-stat-card">
                                <el-text class="value" type="primary">{{ correctRate }}</el-text>
                                <el-text class="label">{{ $t('common.correctRate') }}</el-text>
                            </div>
                        </div>
                        <el-dropdown @command="downloadPDF">
                            <el-button :loading="loadingPDF" @click="downloadPDF('v2')" type="primary">
                                {{ $t('common.downloadPdf') }}
                                <el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="v1">V1</el-dropdown-item>
                                    <el-dropdown-item command="v2">V2</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </template>
            <div class="result-wrapper" v-loading="loading">
                <el-scrollbar>
                    <div v-for="(note, index) in coursenotes" :key="index" class="content-card">
                        <div class="content-card_options">
                            <el-button text>
                                <el-icon :size="15" @click="removeNote(index)">
                                    <Delete />
                                </el-icon>
                            </el-button>
                        </div>
                        <el-text v-if="note.type === 0">
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.question')}：` }}</el-text>
                            <custom-input v-model="note.question" type="textarea" :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.answer')}：` }}</el-text>
                            <custom-input v-model="note.answer" type="textarea" :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.knowledge')}：` }}</el-text>
                            <custom-input v-model="note.knowledge" type="textarea"
                                :autosize="{ minRows: 1 }"></custom-input>
                        </el-text>
                        <el-text v-else-if="note.type === 1">
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('coursenote.knowledgeName')}：` }}</el-text>
                            <custom-input v-model="note.knowledgeName" type="textarea"
                                :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.knowledge')}：` }}</el-text>
                            <custom-input v-model="note.knowledgeContent" type="textarea"
                                :autosize="{ minRows: 1 }"></custom-input>
                        </el-text>
                        <el-text v-else-if="note.type === 2">
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.name')}：` }}</el-text>
                            <custom-input v-model="note.name" type="textarea" :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.question')}：` }}</el-text>
                            <custom-input v-model="note.question" type="textarea" :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.correctAnswer')}：` }}</el-text>
                            <custom-input v-model="note.correctAnswer" type="textarea"
                                :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.knowledge')}：` }}</el-text>
                            <custom-input v-model="note.knowledge" type="textarea"
                                :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('coursenote.studentAnswer')}：` }}</el-text>
                            <custom-input v-model="note.answer" type="textarea" :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.isTrue')}：` }}</el-text>
                            <el-radio-group v-model="note.isTrue" @change="calcCorrectRate">
                                <el-radio :label="1">{{ `${$t('common.true')}：` }}</el-radio>
                                <el-radio :label="0">{{ `${$t('common.error')}：` }}</el-radio>
                            </el-radio-group>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.error')}：` }}</el-text>
                            <custom-input v-model="note.errText" type="textarea" :autosize="{ minRows: 1 }"></custom-input>
                            <el-text class="text-content" size="large" tag="p" type="primary">{{ `${$t('common.evaluation')}：` }}</el-text>
                            <custom-input v-model="note.comment" type="textarea" :autosize="{ minRows: 1 }"></custom-input>
                        </el-text>
                    </div>
                </el-scrollbar>
            </div>
        </el-card>
    </div>
</template>

<script>
import ContentDialog from './content-dialog.vue'
import CustomInput from '@/components/form/custom-input.vue'

export default {
    components: {
        ContentDialog,
        CustomInput,
    },
    data() {
        return {
            type: 0, // 0-题库 1-知识点 2-作业批改
            searchType: '',
            library: {
                search: '',
            },
            libraryLoading: false,
            librarys: [],
            model: {
                aimodel: 'gpt-4-turbo',
                promot: '请回答我的问题，并提供问题所考察的知识点。\n我的问题是: ${question}',
                question: '',
                result: '',
                answer: '',
                knowledge: '',
                status: 0,
            },
            modelRules: {
                promot: [
                    { required: true, message: this.$t('common.pleaseEnter', [this.$t('common.promot')]), trigger: 'blur' }
                ],
                question: [
                    { required: true, message: this.$t('common.pleaseEnter', [this.$t('common.question')]), trigger: 'blur' }
                ],
            },
            modelLoading: false,
            coursenotes: [],
            loadingPDF: false,
            errorNum: 0,
            correctNum: 0
        }
    },
    computed: {
        typeName() {
            return this.type === 0 ? this.$t('common.library') : this.$t('common.bigModel')
        },
        correctRate() {
            if (this.errorNum + this.correctNum === 0) return '100%'
            return (this.correctNum / (this.errorNum + this.correctNum) * 100).toFixed(0) + '%'
        }
    },
    watch: {
        'coursenotes.length'() {
            this.calcCorrectRate()
        },
        '$i18n.locale'() {
            this.typeName = this.type === 0 ? this.$t('common.library') : this.$t('common.bigModel')
        }
    },
    methods: {
        addContent(command) {
            if (command === 'add') {
                this.$refs.contentDialog.show();
            }
        },
        onTypeChange(typeName) {
            if (typeName === this.$t('common.bigModel')) {
                this.type = 1
            }
            if (typeName === this.$t('common.library')) {
                this.type = 0
            }
        },
        onSearchChange() {
            const search = this.library.search
            if (search === '') {
                this.librarys = []
                return
            }
            this.libraryLoading = true;
            this.$axios.get(`/api/questionLibrary/findByKeyword?keyword=${search}&searchType=${this.searchType}`).then(res => {
                this.librarys = res.data
            }).finally(() => {
                this.libraryLoading = false;
            })
        },
        solveQuestion() {
            this.$refs.modelForm.validate((valid, field) => {
                if (valid) {
                    this.model = Object.assign(this.model, {
                        result: '',
                        answer: '',
                        knowledge: '',
                        status: 0,
                    })
                    this.modelLoading = true;
                    this.$axios.post(`/api/chat/completion`, this.createCompletion()).then(res => {
                        this.model.result = this.postHandleReplay(res.data.$response)
                        try {
                            let parsed = JSON.parse(this.model.result)
                            if (!parsed.answer) {
                                throw new Error()
                            }
                            this.model.status = 1
                            this.model.answer = parsed.answer
                            this.model.knowledge = parsed.knowledge
                        } catch (error) {
                            console.error(error)
                            this.model.status = 2
                            this.model.answer = this.model.result
                        }
                    }, err => {
                        this.model.status = -1
                        this.model.result = err.message
                    }).finally(() => {
                        this.modelLoading = false;
                    })
                } else {
                    this.$message.error(this.$t('solve.pleaseCheckConfig'))
                }
            })
        },
        createCompletion() {
            return {
                model: this.model.aimodel,
                messages: [
                    {
                        role: 'user',
                        content: this.replaceText(this.model.promot, { question: this.model.question }) + '\n' +
                            'Replay Latex format string for the JSON value, Do not add extra structure; Check if the JSON syntax format of the response is correct; Provide output in JSON format as follows: \n' +
                            '{"answer":"${答案}","knowledge":"${知识点}"}'
                    }
                ],
                temperature: 0.1,
            }
        },
        replaceText(text, ctx) {
            return text.replace(/\${(\w+)}/g, function (match, key) {
                return ctx[key] || match;
            });
        },
        addToNotes(library) {
            this.$message.success(this.$t('common.saveSuccess'))
            console.log(library)
            this.coursenotes.push(library)
        },
        submitQuestion() {
            let note = {
                type: 0,
                question: this.model.question,
                answer: this.model.answer,
                knowledge: this.model.knowledge
            }
            this.coursenotes.push(note)
        },
        removeNote(index) {
            this.coursenotes.splice(index, 1)
        },
        downloadPDF(version = 'v2') {
            if (this.coursenotes.length === 0) {
                this.$message.error(this.$t('coursenote.pleaseAddQuestion'))
                return
            }
            this.loadingPDF = true
            this.$axios.post("/api/pdf/coursenote", {
                notes: this.coursenotes,
                version,
            }).then(res => {
                this.pdfUrl = this.$fileserver.fileurl(res.data.fileUrl);
                window.open(this.pdfUrl)
            }).finally(() => this.loadingPDF = false)
        },
        postHandleReplay(replay) {
            // 提取回复中的 JSON
            if (replay.startsWith('```json')) {
                replay = replay.substring(7, replay.length - 3)
            }
            if (replay.endsWith('```')) {
                replay = replay.substring(0, replay.length - 3)
            }
            return replay
        },
        libraryTypeName(type) {
            switch (type) {
                case 0:
                    return this.$t('common.library')
                case 1:
                    return this.$t('common.knowledge')
                case 2:
                    return this.$t('coursenote.docReview')
                default:
                    return ''
            }
        },
        calcCorrectRate() {
            let correctNum = 0
            let errorNum = 0
            this.coursenotes.forEach(note => {
                if (note.type === 2) {
                    if (note.isTrue) {
                        correctNum++
                    } else {
                        errorNum++
                    }
                }
            })
            this.correctNum = correctNum
            this.errorNum = errorNum
        }
    }
}
</script>

<style lang="scss" scoped>
.container-wrapper {
    width: auto !important;
    display: flex;
    column-gap: 20px;

    .left-bar {
        width: 500px;
        max-height: calc(100vh - 100px);

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .library-container {
            height: calc(100vh - 210px);
            display: flex;
            flex-direction: column;

            .search-bar {}

            :deep(.el-scrollbar) {
                height: flex 1;
            }

            .content-list {
                display: flex;
                flex-direction: column;
                row-gap: 20px;
                padding: 20px 0;

                .content-list-item {

                    :deep(.el-card__header) {
                        padding: 10px 20px;
                    }

                    :deep(.el-card__body) {
                        padding: 10px;
                    }

                    .box-card-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                }
            }
        }

        .model-container {

            :deep(.el-scrollbar) {
                height: calc(100vh - 210px);
            }
        }

        .model-form {
            height: calc(100vh - 210px);

            .result-card {
                width: 100%;
                padding: 10px;
                border: 1px solid var(--el-border-color);
                min-height: 200px;
            }
        }
    }

    .right-content {
        flex: 1;
        max-height: calc(100vh - 100px);

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .card-header-right {
                display: flex;
                align-items: center;
                column-gap: 20px;

                .el-button {
                    background-color: rgb(147, 84, 214);
                    border: none;
                }

                .review-stat {
                    display: flex;
                    column-gap: 5px;
                    margin-right: 50px;

                    .review-stat-card {
                        display: flex;
                        flex-direction: column;
                        text-align: center;
                        padding: 0 10px;

                        .label {
                            font-size: 12px;
                            color: var(--el-text-color-secondary);
                        }

                        .value {
                            font-size: 20px;
                            font-weight: bold;
                        }
                    }
                }
            }
        }
    }

}

.result-wrapper {
    height: calc(100vh - 210px);
    display: flex;
    flex-direction: column;

    :deep(.el-scrollbar) {
        flex: 1;
    }

    .content-card {
        padding: 10px;
        border: 1px solid var(--el-border-color);
        margin-bottom: 20px;
        position: relative;

        .content-card_options {
            display: none;
            position: absolute;
            top: 5px;
            right: 5px;
        }

    }

    .content-card:hover>.content-card_options {
        display: block;
    }
}

.text-content {
    white-space: pre-wrap;
    word-break: break-all;
}
</style>