<template>
    <el-dialog :model-value="isShow" title="纠错结果" width="800" :before-close="beforeClose">
        <el-table :data="qsStats.stats || []" empty-text="无数据" height="500px" :border="false">
            <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center"
                :fixed="column.prop === 'operations' ? 'right' : ''"
            >
                <template v-if="column.prop === 'correct'" v-slot="scope">
                    {{ scope.row.num - scope.row.total }}
                </template>
                <template v-if="column.prop === 'rate'" v-slot="scope">
                    {{ scope.row.num === 0 ? 0 : (((scope.row.num - scope.row.total) / scope.row.num) * 100).toFixed(2) }}%
                </template>
                <template v-if="column.prop === 'failNum'" v-slot="scope">
                  {{ scope.row?.failNum ?? '无' }}
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            isShow: false,
            data: undefined,
            columns: [
                { label: "题号", type: "index", width: 100, align: "center" },
                { label: "批改失败数", prop: "failCount" },
                { label: "批改错误数", prop: "total" },
                { label: "批改正确数", prop: "correct" },
                { label: "批改总数", prop: "num" },
                { label: "准确率", prop: "rate" },
            ]
        }
    },
    computed: {
        qsStats() {
            return this.data?.qsStats ? JSON.parse(this.data.qsStats) : {}
        }
    },
    methods: {
        show({ data }) {
            this.isShow = true;
            this.data = data;
        },
        beforeClose() {
            this.isShow = false;
            this.data = undefined;
        }
    }
}
</script>

<style lang="scss" scoped></style>