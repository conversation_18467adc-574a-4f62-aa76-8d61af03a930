<template>
    <div class="container">
        <el-form class="header-bar" :inline="true" label-width="100px">
            <el-form-item :label="`${$t('common.name')}：`">
                <el-input v-model="filter.name" />
            </el-form-item>
            <el-form-item :label="`${$t('common.question')}：`">
                <el-input v-model="filter.question" />
            </el-form-item>
            <el-form-item :label="`${$t('common.answer')}：`">
                <el-input v-model="filter.answer" />
            </el-form-item>
            <el-form-item :label="`${$t('common.isTrue')}：`">
                <el-select style="width: 196.4px"  v-model="filter.isTrue" placeholder=" " clearable>
                    <el-option value="0" :label="$t('common.error')"></el-option>
                    <el-option value="1" :label="$t('common.true')"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="loadReviews">{{ $t('common.search') }}</el-button>
                <el-button type="primary" @click="handleAdd">{{ $t('common.add') }}</el-button>
            </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="reviews" style="width: 100%" :border="true" size="large" class="main-table"
            :empty-text="$t('common.emptyData')">
            <template v-for="column in columns" :key="column.prop">
                <el-table-column v-bind="column" :label="$t(column.t)">
                    <template v-if="column.prop === 'operations'" #default="scope">
                        <el-button type="primary" @click="handleEdit(scope.row)">{{ $t('common.edit') }}</el-button>
                        <el-button type="danger" @click="handleDelete(scope.row.id)">{{ $t('common.delete') }}</el-button>
                    </template>
                    <template v-else-if="column.type === 'textarea'" #default="scope">
                        <el-scrollbar max-height="200px">
                            <el-text style="white-space: pre-wrap; word-break: break-all;">
                                {{ scope.row[column.prop] }}
                            </el-text>
                        </el-scrollbar>
                    </template>
                    <template v-else-if="column.type === 'dic'" #default="scope">
                        <el-tag>{{ $t(column.dic[scope.row[column.prop]]) }}</el-tag>
                    </template>
                </el-table-column>
            </template>
        </el-table>
        <form-dialog :isShow="isShowForm" :title="formTitle" :data="checkedData" @onClose="onFormClose"></form-dialog>
    </div>
</template>

<script>
import FormDialog from './form-dialog.vue'
export default {
    components: {
        FormDialog,
    },
    data() {
        return {
            columns: [
                {
                    prop: "name",
                    label: "名称",
                    t: "common.name",
                },
                {
                    prop: "question",
                    label: "题目",
                    type: 'textarea',
                    t: "common.question",
                },
                {
                    prop: "correctAnswer",
                    label: "正确答案",
                    type: 'textarea',
                    t: "common.correctAnswer",
                },
                {
                    prop: 'knowledge',
                    label: '知识点',
                    type: 'textarea',
                    t: "common.knowledge",
                },
                {
                    prop: "answer",
                    label: "答案",
                    type: 'textarea',
                    t: "common.answer",
                },
                {
                    prop: "isTrue",
                    label: "是否正确",
                    type: 'dic',
                    t: "common.isTrue",
                    dic: {
                        0: 'common.error',
                        1: 'common.true', 
                    },
                    align: 'center',
                    width: '90px'
                },
                {
                    prop: "errText",
                    label: "错误",
                    type: 'textarea',
                    t: "common.error"
                },
                {
                    prop: "comment",
                    label: "评价",
                    type: 'textarea',
                    t: "common.evaluation"
                },
                {
                    prop: 'operations',
                    label: '操作',
                    width: '200px',
                    t: "common.operations"
                }
            ],
            filter: {
                question: '',
                answer: '',
                isTrue: null,
                name: '',
            },
            reviews: [],
            loading: false,
            isShowForm: false,
            formTitle: '',
            checkedData: {},
        }
    },
    created() {
        this.loadReviews()
    },
    methods: {
        loadReviews() {
            this.loading = true
            let params = {
                name: this.filter.name,
                question: this.filter.question,
                answer: this.filter.answer,
                isTrue: this.filter.isTrue,
            }
            // TODO
            this.$axios.get("/api/review/list", { params }).then(res => {
                this.reviews = res.data
            }).finally(() => {
                this.loading = false
            })
        },
        handleAdd() {
            this.formTitle = "common.add"
            this.isShowForm = true
        },
        onFormClose(submited) {
            this.isShowForm = false
            if (submited) {
                this.checkedData = {}
                this.loadReviews()
            }
        },
        handleEdit(data) {
            this.formTitle = 'common.edit'
            this.checkedData = data
            this.isShowForm = true
        },
        handleDelete(id) {
            this.$confirm(this.$t('common.confirmDelete'), {
                confirmButtonText: this.$t('common.confirm'),
                cancelButtonText: this.$t('common.cancel'),
                type: 'warning'
            }).then(() => {
                this.$axios.post(`/api/review/delete?id=${id}`).then(res => {
                    this.$message.success(this.$t('common.deleteSuccess'))
                    this.loadReviews()
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .header-bar {
        padding: 10px;
    }

    .main-table {
        flex: 1;
    }
}
</style>