<template>
    <div class="ai-smart-correction">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-radial-bg"></div>
            <div class="container">
                <div class="hero-content">
                    <div class="badge">AI 智能教育新时代</div>
                    <h1 class="title">
                        AI 智能批改系统
                        <span class="subtitle">让教育更智慧，让批改更高效</span>
                    </h1>
                    <p class="description">
                        运用先进的 AI 技术，为教育工作者提供智能化的作业批改解决方案。
                        提高批改效率，降低工作负担，让教师更专注于教学本质。
                    </p>
                    <div class="action-buttons">
                        <el-button type="primary" size="large" class="primary-button" @click="toHome">立即体验</el-button>
                        <el-button size="large" class="secondary-button" @click="toHome">了解更多</el-button>
                    </div>
                </div>
            </div>
            <div class="hero-bottom-fade"></div>
        </div>

        <!-- Stats Section -->
        <div class="container">
            <div class="stats-section">
                <div class="stats-grid">
                    <div v-for="(stat, index) in stats" :key="index" class="stat-card">
                        <div class="stat-content">
                            <div class="icon-container">
                                <el-icon :size="24">
                                    <component :is="stat.icon" />
                                </el-icon>
                            </div>
                            <div>
                                <h3 class="stat-title">{{ stat.title }}</h3>
                                <div class="stat-value">
                                    <span class="value">{{ stat.value }}</span>
                                    <span class="desc">{{ stat.desc }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Section -->
            <div class="video-section">
                <div class="video-container">
                    <h2 class="section-title">
                        智能批改演示
                        <span class="section-subtitle">体验 AI 批改的魅力</span>
                    </h2>
                    <div class="video-wrapper">
                        <img src="/survey-bg1.jpg" alt="视频封面" class="video-cover" v-show="!isPlaying" />
                        <video ref="videoPlayer" src="https://www.saomiaoshijuan.com/static/intro.mp4" class="video-player" v-show="isPlaying"
                            @ended="isPlaying = false"></video>
                        <div class="video-gradient" v-show="!isPlaying"></div>
                        <button @click="togglePlay" class="play-button">
                            <el-icon :size="80" class="play-icon">
                                <VideoPlay v-if="!isPlaying" />
                                <VideoPause v-else />
                            </el-icon>
                        </button>
                    </div>
                </div>

                <!-- Form Section -->
                <div class="form-container">
                    <h2 class="section-title">预约体验</h2>
                    <p class="form-description">填写以下信息，我们的专业顾问将为您提供个性化演示</p>
                    <el-form ref="formRef" :model="form" label-position="top" :rules="rules" @submit.prevent="onSubmit"
                        class="appointment-form">
                        <div class="form-grid">
                            <el-form-item label="姓名" prop="name">
                                <el-input v-model="form.name" placeholder="请输入姓名" />
                            </el-form-item>

                            <el-form-item label="联系电话" prop="phone">
                                <el-input v-model="form.phone" placeholder="请输入联系电话" />
                            </el-form-item>

                            <el-form-item label="电子邮箱" prop="email">
                                <el-input v-model="form.email" placeholder="请输入电子邮箱" />
                            </el-form-item>

                            <el-form-item label="所在学校" prop="school">
                                <el-input v-model="form.school" placeholder="请输入学校名称" />
                            </el-form-item>

                            <el-form-item label="任教学科" prop="subject">
                                <el-select v-model="form.subject" placeholder="请选择学科" class="full-width">
                                    <el-option v-for="subject in subjects" :key="subject" :label="subject"
                                        :value="subject" />
                                </el-select>
                            </el-form-item>

                            <el-form-item label="教学年级" prop="grade">
                                <el-select v-model="form.grade" placeholder="请选择年级" class="full-width">
                                    <el-option v-for="grade in grades" :key="grade" :label="grade" :value="grade" />
                                </el-select>
                            </el-form-item>
                        </div>

                        <el-form-item label="预期使用场景" prop="scenarios">
                            <el-checkbox-group v-model="form.scenarios">
                                <el-checkbox v-for="scenario in scenarios" :key="scenario" :label="scenario"
                                    :value="scenario">
                                    {{ scenario }}
                                </el-checkbox>
                                <!-- <div class="checkbox-grid">
                                    
                                </div> -->
                            </el-checkbox-group>
                        </el-form-item>

                        <el-form-item label="关注的功能点" prop="features">
                            <el-checkbox-group v-model="form.features">
                                <el-checkbox v-for="feature in features" :key="feature" :label="feature"
                                    :value="feature">
                                    {{ feature }}
                                </el-checkbox>
                                <!-- <div class="checkbox-grid">
                                    
                                </div> -->
                            </el-checkbox-group>
                        </el-form-item>

                        <el-form-item label="其他需求或建议" prop="comments">
                            <el-input v-model="form.comments" type="textarea" placeholder="请输入您的其他需求或建议" :rows="4" maxlength="255" />
                        </el-form-item>

                        <el-form-item class="form-submit">
                            <el-button type="primary" native-type="submit" size="large" class="submit-button">
                                提交预约
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from '@/http'
import {
    VideoPlay,
    VideoPause,
    Monitor,
    Lightning,
    TrendCharts,
    CircleCheck
} from '@element-plus/icons-vue';

// Reactive state
const isPlaying = ref(false);
const animatedNumber = ref(0);
const formRef = ref(null);
const videoPlayer = ref(null);

const form = reactive({
    name: '',
    phone: '',
    email: '',
    school: '',
    subject: '',
    grade: '',
    scenarios: [],
    features: [],
    comments: '',
});

// Data
const subjects = [
    '语文', '数学', '英语', '物理', '化学',
    '生物', '历史', '地理', '政治',
];

const grades = [
    '一年级', '二年级', '三年级', '四年级', '五年级', '六年级',
    '初一', '初二', '初三', '高一', '高二', '高三',
];

const scenarios = [
    '日常作业批改', '考试试卷批改', '课堂练习批改',
    '作业错题分析', '学生成绩追踪',
];

const features = [
    '智能批改', '错误分析', '成绩统计',
    '个性化反馈', '批改记录导出', '学情分析报告',
];

const stats = [
    {
        icon: Monitor,
        title: '智能批改',
        value: '98%',
        desc: '准确率',
    },
    {
        icon: Lightning,
        title: '批改速度',
        value: '3s',
        desc: '每页',
    },
    {
        icon: TrendCharts,
        title: '效率提升',
        value: '10x',
        desc: '效率',
    },
    {
        icon: CircleCheck,
        title: '师生满意度',
        value: '96%',
        desc: '好评',
    },
];

// Form validation rules
const rules = {
    name: [{ required: true, message: '请输入您的姓名', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入您的联系电话', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }],
    school: [{ required: true, message: '请输入所在学校', trigger: 'blur' }],
    subject: [{ required: true, message: '请选择任教学科', trigger: 'change' }],
    grade: [{ required: true, message: '请选择教学年级', trigger: 'change' }],
    scenarios: [{ required: true, message: '请选择使用场景', trigger: 'change' }],
    features: [{ required: true, message: '请选择关注的功能', trigger: 'change' }],
};

// Animation for number counter
onMounted(() => {
    const interval = setInterval(() => {
        animatedNumber.value = (animatedNumber.value + 1) % 100;
    }, 50);

    return () => clearInterval(interval);
});

// Methods
const togglePlay = () => {
    isPlaying.value = !isPlaying.value;

    if (isPlaying.value) {
        videoPlayer.value?.play();
    } else {
        videoPlayer.value?.pause();
    }
};

const onSubmit = async () => {
    if (!formRef.value) return;

    await formRef.value.validate((valid) => {
        if (valid) {
            const params = {
                ...form,
                scenarios: form.scenarios.join(","),
                features: form.features.join(","),
            }
            console.log(params);
            axios.post("/api/survey/confirm", params).then(() => {
                ElMessage.success('提交成功！我们会尽快与您联系。');
                formRef.value.resetFields();
            })
        }
    });
};

const toHome = () => {
    window.location.href = '/';
};

</script>

<style lang="scss" scoped>
.ai-smart-correction {
    min-height: 100vh;
    background: linear-gradient(to bottom, #f9fafb, #ffffff);

    // Hero Section
    .hero-section {
        position: relative;
        height: 700px;
        background-image: url('/survey-bg.jpg');
        background-size: cover;
        background-position: center;
        overflow: hidden;

        .hero-overlay {
            position: absolute;
            inset: 0;
            background: linear-gradient(to right, rgba(30, 58, 138, 0.9), rgba(30, 64, 175, 0.8), transparent);
        }

        .hero-radial-bg {
            position: absolute;
            inset: 0;
            height: 100%;
            width: 100%;
            background: radial-gradient(circle at 30% 50%, rgba(56, 189, 248, 0.1) 0%, rgba(56, 189, 248, 0) 70%);
        }

        .container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            padding-top: 128px;
        }

        .hero-content {
            max-width: 768px;

            .badge {
                display: inline-block;
                padding: 8px 24px;
                border-radius: 9999px;
                background-color: rgba(59, 130, 246, 0.2);
                backdrop-filter: blur(4px);
                border: 1px solid rgba(96, 165, 250, 0.3);
                margin-bottom: 24px;
                color: #bfdbfe;
            }

            .title {
                font-size: 4.5rem;
                font-weight: 700;
                color: white;
                margin-bottom: 32px;
                line-height: 1.2;

                .subtitle {
                    display: block;
                    font-size: 2.25rem;
                    background: linear-gradient(to right, #93c5fd, #d8b4fe);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-top: 16px;
                }
            }

            .description {
                font-size: 1.25rem;
                color: #e5e7eb;
                margin-bottom: 48px;
                line-height: 1.7;
                max-width: 42rem;
            }

            .action-buttons {
                display: flex;
                gap: 24px;

                .primary-button {
                    padding: 0 40px;
                    height: 56px;
                    font-size: 1.125rem;
                    border-radius: 28px;
                    background: linear-gradient(to right, #3b82f6, #2563eb);
                    border: none;
                    box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3);
                    transition: all 0.3s;

                    &:hover {
                        background: linear-gradient(to right, #2563eb, #1d4ed8);
                        box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.35);
                        transform: translateY(-2px);
                    }
                }

                .secondary-button {
                    padding: 0 40px;
                    height: 56px;
                    font-size: 1.125rem;
                    border-radius: 28px;
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    background-color: transparent;
                    backdrop-filter: blur(4px);
                    transition: all 0.3s;

                    &:hover {
                        background-color: rgba(255, 255, 255, 0.1);
                    }
                }
            }
        }

        .hero-bottom-fade {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 128px;
            background: linear-gradient(to top, #f9fafb, transparent);
        }
    }

    // Stats Section
    .stats-section {
        margin-top: -96px;
        position: relative;
        z-index: 10;

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 24px;

            @media (min-width: 768px) {
                grid-template-columns: repeat(4, 1fr);
            }

            .stat-card {
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                padding: 24px;
                border: 1px solid #f3f4f6;
                transition: all 0.3s;

                &:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                }

                .stat-content {
                    display: flex;
                    align-items: center;
                    gap: 16px;

                    .icon-container {
                        font-size: 26px;
                        color: #3b82f6;
                        background-color: #eff6ff;
                        border-radius: 9999px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 70px;
                        height: 70px;
                    }

                    .stat-title {
                        font-size: 1.125rem;
                        font-weight: 600;
                        color: #1f2937;
                        margin-bottom: 4px;
                    }

                    .stat-value {
                        display: flex;
                        align-items: baseline;
                        gap: 8px;
                        margin-top: 4px;

                        .value {
                            font-size: 1.875rem;
                            font-weight: 700;
                            color: #2563eb;
                        }

                        .desc {
                            color: #6b7280;
                        }
                    }
                }
            }
        }
    }

    // Container for all content
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 24px;
    }

    // Video Section
    .video-section {
        padding: 96px 0;

        .video-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 48px;
            margin-bottom: 64px;
            transition: all 0.3s;

            &:hover {
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            }

            .section-title {
                font-size: 2.25rem;
                font-weight: 700;
                color: #1f2937;
                margin-bottom: 32px;
                text-align: center;

                .section-subtitle {
                    display: block;
                    font-size: 1.25rem;
                    color: #6b7280;
                    margin-top: 12px;
                    font-weight: 400;
                }
            }

            .video-wrapper {
                position: relative;
                aspect-ratio: 16 / 9;
                background-color: #111827;
                border-radius: 12px;
                overflow: hidden;

                &:hover {
                    .video-cover {
                        transform: scale(1.05);
                    }
                }

                .video-cover {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: transform 0.7s;
                }

                .video-player {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .video-gradient {
                    position: absolute;
                    inset: 0;
                    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
                }

                .play-button {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: transparent;
                    border: none;
                    cursor: pointer;
                    transition: transform 0.3s;

                    &:hover {
                        transform: translate(-50%, -50%) scale(1.1);
                    }

                    .play-icon {
                        color: white;
                        opacity: 0.9;

                        &:hover {
                            opacity: 1;
                        }
                    }
                }
            }
        }
    }

    // Form Section
    .form-container {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        padding: 48px;

        .section-title {
            font-size: 2.25rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 12px;
            text-align: center;
        }

        .form-description {
            color: #6b7280;
            text-align: center;
            margin-bottom: 48px;
        }

        .appointment-form {
            max-width: 768px;
            margin: 0 auto;

            .form-grid {
                display: grid;
                grid-template-columns: 1fr;
                gap: 24px;

                @media (min-width: 768px) {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            .checkbox-grid {
                display: grid;
                grid-template-columns: 1fr;
                gap: 16px;

                @media (min-width: 768px) {
                    grid-template-columns: repeat(3, 1fr);
                }
            }

            .form-submit {
                width: 100%;
                text-align: center;
                margin-top: 16px;

                :deep(.el-form-item__content) {
                    justify-content: center;
                }

                .submit-button {
                    padding: 0 48px;
                    height: 48px;
                    font-size: 1.125rem;
                    border-radius: 24px;
                    background: linear-gradient(to right, #3b82f6, #2563eb);
                    border: none;
                    box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.3);
                    transition: all 0.3s;

                    &:hover {
                        background: linear-gradient(to right, #2563eb, #1d4ed8);
                        box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.35);
                        transform: translateY(-2px);
                    }
                }
            }
        }
    }

    // Utils
    .full-width {
        width: 100%;
    }
}
</style>