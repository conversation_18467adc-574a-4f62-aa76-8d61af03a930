<template>
  <el-dialog v-model="isShow" title="详细版表格" width="800" :before-close="beforeClose">
    <el-form :model="form" label-width="100px">

      <el-form-item label="按照学号排序">
        <el-switch v-model="isSortByStuNo" active-text="排序" inactive-text="不排序"></el-switch>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <div style="text-align: right">
          <el-button type="primary" @click="onSubmit">提交</el-button>
          <el-button type="danger" @click="beforeClose">取消</el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      isShow: false,
      form: {},
      isSortByStuNo: true
    };
  },
  methods: {
    onSubmit() {
      this.$emit('submit', this.isSortByStuNo);
      this.beforeClose();
    },
    // 关闭弹窗
    beforeClose() {
      this.isShow = false;
      this.isSortByStuNo = true;
    },
    // 显示弹窗
    show(data, fileId) {
      this.isShow = true;

    }
  }
};
</script>

<style lang="scss" scoped>
.el-dialog .el-card {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

.text-center {
  text-align: center;
}
</style>
