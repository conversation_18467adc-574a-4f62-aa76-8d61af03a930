<template>
  <div>

    <el-dialog
        title="添加大题类型"
        v-model="dialogVisible"
        width="800px"
        :before-close="onCancel"
    >
      <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="140px"
      >

        <el-form-item label="该大题的中文描述" prop="name">
          <el-input
              v-model="form.name"
              placeholder="如 阅读理解"
              clearable
          />
        </el-form-item>

        <el-form-item label="提示词" prop="value">
          <el-input
              type="textarea"
              v-model="form.value"
              placeholder="请输入默认值（可为空）"
              autosize
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus';

export default {
  name: 'AddQuestionTypeDialog',
  data() {
    return {
      dialogVisible: false,
      form: {
        name: '',
        value: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入中文名称进行描述', trigger: 'blur' }
        ],
        value: [
          { required: true, message: '请输入提示词', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    show() {
      this.dialogVisible = true;
    },
    onCancel() {
      this.dialogVisible = false;
      // 重置表单
      this.$nextTick(() => {
        this.$refs.formRef.resetFields();
      });
    },
    onSubmit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        this.$axios
            .post('/api/prompts/addQuestionType', this.form)
            .then(() => {
              ElMessage.success('添加成功');
              this.dialogVisible = false;
              this.$emit('close')
            })
            .catch(() => {
              ElMessage.error('添加失败，请重试');
            });
      });
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
