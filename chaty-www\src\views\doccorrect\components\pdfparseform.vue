<template>
    <el-dialog v-model="isShow" :title="title" width="500" :before-close="onClose">
        <el-form ref="formRef" style="margin: 0 20px;" 	
            :model="form">
            <el-form-item label="试卷：">
                <el-upload style="width: 200px" ref="pdfUploader" accept=".pdf" 
                    :data="getParseConfig"
                    :action="$fileserver.multipdfUrl" 
                    :with-credentials="true" 
                    :show-file-list="true"
                    :auto-upload="false"
                    :file-list="docList"
                    :on-change="onDocChange"
                    :on-success="uploadDoc">
                    <el-space>
                        <el-button icon="Upload" type="primary">上传试卷</el-button>
                        <el-button icon="Upload" type="primary" @click.stop="showRemoteFolder">远程文件夹</el-button>
                    </el-space>
                </el-upload>
            </el-form-item>
            <el-form-item label="页数：" prop="pageNum">
                <el-input-number style="width: 160px" :min="1" :controls="false" v-model="form.pageNum"
                    :value-on-clear="1" />
            </el-form-item>
            <el-form-item v-for="(page, index) in form.pages" :key="index" :label="`第${index + 1}页：`">
                <el-switch style="margin-right: 20px;" inactive-text="解析" v-model="page.isParse" />
                <el-switch style="margin-right: 20px;" inactive-text="翻转" v-model="page.isRotation" />
                <el-button v-if="form.pageNum !== 1" text icon="Delete" @click="form.pages.splice(index, 1)" />
            </el-form-item>
            <el-form-item label="保存试卷配置：">
                <el-switch v-model="form.isCreateConfig" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" @click="onClose">取消</el-button>
            <el-button type="primary" @click="onSubmit">确认</el-button>
        </template>

        <!-- 远程文件上传 -->
        <file-selector ref="fileSelector" @file-selected="onRemoteFileSelected" />
    </el-dialog>
</template>

<script>
import FileSelector from '../../common/FileSelector.vue';

export default {
    components: {
        FileSelector
    },
    data() {
        return {
            isShow: false,
            title: 'PDF拆分',
            form: {
                isCreateConfig: true,
                pageNum: 2,
                pages: [
                    { isParse: true, isRotation: false },
                    { isParse: true, isRotation: false }
                ]
            },
            docList: []
        }
    },
    watch: {
        "form.pageNum": {
            handler(val) {
                if (val > this.form.pages.length) {
                    for (let i = this.form.pages.length; i < val; i++) {
                        this.form.pages.push({ isParse: true, isRotation: false });
                    }
                } else {
                    this.form.pages = this.form.pages.slice(0, val);
                }
            }
        },
        "form.pages.length": {
            handler(val) {
                this.form.pageNum = val;
            }
        }
    },
    methods: {
        show() {
            this.isShow = true
        },
        onClose() {
            this.$refs.formRef.resetFields()
            this.$refs.pdfUploader.clearFiles()
            this.docList = []
            this.isShow = false
        },
        onSubmit() {
            if (this.docList.length === 0) {
                this.$message.error("请上传试卷");
                return;
            }
            this.$refs.pdfUploader.submit()
        },
        uploadDoc(response, file, fileList) {
            let files = response.data
            if (!files || files.length === 0) {
                this.$message.error("文件上传失败!");
                return;
            }
            let filename = file.name.substring(0, file.name.lastIndexOf("."));
            let form = {
                filename,
                docList: files,
                isCreateConfig: this.form.isCreateConfig,
            };
            this.$axios.post("/api/docCorrectTask/add/file", form).then(res => {
                this.$message.success("文件上传成功");
                this.$emit("submit");
                this.onClose();
            });
        },
        onDocChange(uploadFile, uploadFiles) {
            console.log(uploadFile)
            let status = uploadFile.status
            if (status === 'ready') {
                this.docList = [uploadFile]
            }
        },
        getParseConfig() {
            return {
                config: JSON.stringify(this.form)
            };
        },
        showRemoteFolder() {
            this.$refs.fileSelector.show();
        },
        onRemoteFileSelected(uploadFile) {
            console.log(uploadFile)
            this.docList = [uploadFile];
        }
    }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small) {
    margin-bottom: 5px;
}

:deep(.el-form-item) {
    margin-bottom: 5px;
}
</style>