<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex; align-items: center; width: 100%">
          <el-image src="/icon/16.png" class="left-icon"></el-image>
          <el-text class="title">待办试卷</el-text>
          <el-form inline ref="formRef" :model="ftpMessageIndexPageData.formData" class="header-form"
                   style="flex:1; margin-left: 10px;">
            <el-form-item label="文件名称" style="margin-right: 15px">
              <el-input
                  v-model="ftpMessageIndexPageData.formData.fileName"
                  placeholder="文件名称"
                  clearable
                  style="width: 180px"
                  @input="debouncedLoadData"
              />
            </el-form-item>
            <el-form-item label="试卷备注" style="margin-right: 15px">
              <el-input
                  v-model="ftpMessageIndexPageData.formData.fileRemark"
                  placeholder="试卷备注"
                  clearable
                  style="width: 180px"
                  @input="debouncedLoadData"
              />
            </el-form-item>
            <el-form-item label="范围">
              <el-switch v-model="ftpMessageIndexPageData.formData.isSelectToday" :active-value="true"
                         active-text="今天" inactive-text="全部" @change="loadData"></el-switch>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="loading" @click="loadData">查询/刷新</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #extra>
        <div style="display: flex;align-items: center">
          <el-statistic class="header-action header-stats" title="进行中" value-style="color: #E6A23C;"
                        :value="statusCount.inProgress"/>
          <el-statistic class="header-action header-stats" title="已批改" value-style="color: #67C23A;"
                        :value="statusCount.completed"/>
          <el-statistic class="header-action header-stats" title="总数量" value-style="color: #E74C3C;"
                        :value="statusCount.totalQuantity"/>
        </div>
      </template>
    </el-page-header>

    <div class="main-content">
      <div class="title-menu" style="margin-bottom: 16px;">
        <el-menu mode="horizontal" size="mini" :default-active="ftpMessageIndexPageData.activeTitle"
                 @select="onTitleSelect">
          <el-menu-item index="">
            <el-badge :value="allUnFinishJobs?.['全部'] ?? 0" class="item"
                      :hidden="(allUnFinishJobs?.['全部'] ?? 0) === 0">
              <el-text style="font-weight: bolder;font-size: 16px">全部</el-text>
            </el-badge>
          </el-menu-item>
          <el-menu-item
              v-for="item in allTitles"
              :key="item.id"
              :index="item.title"
          >
            <el-badge :value="allUnFinishJobs?.[item.title] ?? 0" class="item"
                      :hidden="(allUnFinishJobs?.[item.title] ?? 0) === 0">
              <el-text style="font-weight: bolder;font-size: 16px">{{ item.title }}</el-text>
            </el-badge>
          </el-menu-item>
        </el-menu>
      </div>
      <div v-if="ftpMessageIndexPageData.activeTitle !== ''"
           style="display: flex;justify-content: space-between;width: 100%">
        <div
            style="display: flex;align-items: center;margin-top: -5px;margin-bottom: 5px">
          <div style="font-weight: bolder">路径：</div>
          <el-link type="primary" @click="jumpPrint(printUrl + (activeFtpMessageTitleObj?.path || '/'))">
            {{ activeFtpMessageTitleObj?.path || '未设置' }}
          </el-link>
          <el-tooltip placement="top" content="在弹窗打开后，请再次点击上传按钮">
            <el-button icon="Upload" size="small" type="text" color="#3981ff" style="margin-left: 10px;color:#3981ff"
                       @click="$refs.fileSelectorDialog.show(activeFtpMessageTitleObj?.path)">开始上传
            </el-button>
          </el-tooltip>
          <div style="font-weight: bolder;margin-left: 20px;">学校备注：</div>
          <div @click="openSchoolRemark" style="cursor: pointer;text-decoration: underline">
            {{ activeFtpMessageTitleObj?.remark || '点我修改' }}
          </div>
        </div>
        <div style="font-size: 14px">Tip: 点击每一行的空白处即可去完成</div>
      </div>

      <el-table
          v-loading="loading"
          :data="tableData"
          style="height: 100%"
          empty-text="暂无数据"
          :border="false"
          :row-style="getRowStyle"
          @row-click="viewDetail"
      >
        <el-table-column
            v-for="column in visibleColumns"
            :key="column.prop || column.type"
            v-bind="column"
            :align="column?.align ?? 'center'"
        >
          <template v-if="column.prop === 'fileCompleted'" #header>
            <div style="display:flex; align-items:center; justify-content:center">
              <span>试卷</span>
              <el-popover
                  v-model:visible="filterVisible.fileCompleted"
                  trigger="click"
                  placement="bottom"
                  width="120"
              >
                <el-select
                    v-model="ftpMessageIndexPageData.formData.fileCompleted"
                    placeholder="状态"
                    clearable
                    size="small"
                    @change="onFilterChange('fileCompleted')"
                >
                  <el-option label="已完成" :value="true"/>
                  <el-option label="未完成" :value="false"/>
                </el-select>
                <template #reference>
                  <el-icon style="margin-left:4px; cursor:pointer">
                    <Filter/>
                  </el-icon>
                </template>
              </el-popover>
            </div>
          </template>

          <template v-else-if="column.prop === 'exportCompleted'" #header>
            <div style="display:flex; align-items:center; justify-content:center">
              <span>导出</span>
              <el-popover
                  v-model:visible="filterVisible.exportCompleted"
                  trigger="click"
                  placement="bottom"
                  width="120"
              >
                <el-select
                    v-model="ftpMessageIndexPageData.formData.exportCompleted"
                    placeholder="状态"
                    clearable
                    size="small"
                    @change="onFilterChange('exportCompleted')"
                >
                  <el-option label="已完成" :value="true"/>
                  <el-option label="未完成" :value="false"/>
                </el-select>
                <template #reference>
                  <el-icon style="margin-left:4px; cursor:pointer">
                    <Filter/>
                  </el-icon>
                </template>
              </el-popover>
            </div>
          </template>

          <!-- 收藏列 -->
          <template v-if="column.prop === 'isCollect'" #header>
            <div style="display:flex; align-items:center; justify-content:center">
              <span>收藏</span>
              <el-popover
                  v-model:visible="filterVisible.isCollect"
                  trigger="click"
                  placement="bottom"
                  width="120"
              >
                <el-select
                    v-model="ftpMessageIndexPageData.formData.isCollect"
                    placeholder="状态"
                    clearable
                    size="small"
                    @change="onFilterChange('isCollect')"
                >
                  <el-option label="已收藏" :value="true"/>
                  <el-option label="未收藏" :value="false"/>
                </el-select>
                <template #reference>
                  <el-icon style="margin-left:4px; cursor:pointer">
                    <Filter/>
                  </el-icon>
                </template>
              </el-popover>
            </div>
          </template>

          <!-- 标准卷列 -->
          <template v-else-if="column.prop === 'configPackageCompleted'" #header>
            <div style="display:flex; align-items:center; justify-content:center">
              <span>标准卷</span>
              <el-popover
                  v-model:visible="filterVisible.configPackageCompleted"
                  trigger="click"
                  placement="bottom"
                  width="120"
              >
                <el-select
                    v-model="ftpMessageIndexPageData.formData.configPackageCompleted"
                    placeholder="状态"
                    clearable
                    size="small"
                    @change="onFilterChange('configPackageCompleted')"
                >
                  <el-option label="已完成" :value="true"/>
                  <el-option label="未完成" :value="false"/>
                </el-select>
                <template #reference>
                  <el-icon style="margin-left:4px; cursor:pointer">
                    <Filter/>
                  </el-icon>
                </template>
              </el-popover>
            </div>
          </template>

          <!--          <template v-else-if="column.prop === 'isCorrectFinish'" #header>-->
          <!--            <div style="display:flex; align-items:center; justify-content:center">-->
          <!--              <span>批改进度</span>-->
          <!--              <el-popover-->
          <!--                  v-model:visible="filterVisible.isCorrectFinish"-->
          <!--                  trigger="click"-->
          <!--                  placement="bottom"-->
          <!--                  width="120"-->
          <!--              >-->
          <!--                <el-select-->
          <!--                    v-model="ftpMessageIndexPageData.formData.isCorrectFinish"-->
          <!--                    placeholder="状态"-->
          <!--                    clearable-->
          <!--                    size="small"-->
          <!--                    @change="onFilterChange('isCorrectFinish')"-->
          <!--                >-->
          <!--                  <el-option-->
          <!--                      v-for="(opt, key) in isCorrectFinishOptions"-->
          <!--                      :key="key"-->
          <!--                      :label="opt.label"-->
          <!--                      :value="Number(key)"-->
          <!--                  />-->
          <!--                </el-select>-->
          <!--                <template #reference>-->
          <!--                  <el-icon style="margin-left:4px; cursor:pointer">-->
          <!--                    <Filter />-->
          <!--                  </el-icon>-->
          <!--                </template>-->
          <!--              </el-popover>-->
          <!--            </div>-->
          <!--          </template>-->

          <!-- 备注列 -->
          <template v-else-if="column.prop === 'remark'" #header>
            <div style="display:flex; align-items:center; justify-content:center">
              <span>备注</span>
              <el-popover
                  v-model:visible="filterVisible.remark"
                  trigger="click"
                  placement="bottom"
                  width="200"
              >
                <el-input
                    v-model="ftpMessageIndexPageData.formData.remark"
                    placeholder="请输入关键词"
                    size="small"
                    clearable
                    @keyup.enter.native="onFilterChange('remark')"
                />
                <template #reference>
                  <el-icon style="margin-left:4px; cursor:pointer">
                    <Filter/>
                  </el-icon>
                </template>
              </el-popover>
            </div>
          </template>

          <template v-else-if="column.prop === 'fileName'" #header>
            <div style="display:flex; align-items:center; justify-content:center">
              <span>文件名称</span>
              <el-popover
                  v-model:visible="filterVisible.fileName"
                  trigger="click"
                  placement="bottom"
                  width="200"
              >
                <el-input
                    v-model="ftpMessageIndexPageData.formData.fileName"
                    placeholder="请输入关键词"
                    size="small"
                    clearable
                    @keyup.enter.native="onFilterChange('fileName')"
                />
                <template #reference>
                  <el-icon style="margin-left:4px;cursor:pointer">
                    <Filter/>
                  </el-icon>
                </template>
              </el-popover>
            </div>
          </template>

          <template v-else-if="column.prop === 'schoolName'" #header>
            <div style="display:flex; align-items:center; justify-content:center">
              <span>标题</span>
              <el-popover
                  v-model:visible="filterVisible.schoolName"
                  trigger="click"
                  placement="bottom"
                  width="200"
              >
                <el-input
                    v-model="ftpMessageIndexPageData.formData.schoolName"
                    placeholder="请输入关键词"
                    size="small"
                    clearable
                    @keyup.enter.native="onFilterChange('schoolName')"
                />
                <template #reference>
                  <el-icon style="margin-left:4px; cursor:pointer">
                    <Filter/>
                  </el-icon>
                </template>
              </el-popover>
            </div>
          </template>

          <template v-if="column.type === 'index'" #default="{ $index }">
            <div class="bolder">{{ $index + 1 }}</div>
          </template>
          <template v-if="column.prop === 'notificationTime'" #default="{ row }">
            <div class="bolder">{{ row.notificationTime || '-' }}</div>
          </template>
          <template v-if="column.prop === 'fileCompleted'" #default="{ row }">
            <el-tag
                v-if="!row?.docCorrectFile?.id"
                :type="row.fileCompleted? 'success' : 'warning'"
                style="cursor: pointer"
                class="bolder"
                @click.stop="quickEditRemark(row.id, 'fileCompleted', !row.fileCompleted)"
            >
              {{ row.fileCompleted ? '已完成' : '未完成' }}
            </el-tag>
            <div v-else-if="row?.docCorrectFile?.id" style="display: flex;flex-direction: column">
              <el-link type="primary" class="bolder" :href="'/markPapers/stepMarkPapers/'+ row?.docCorrectFile?.id"
                       @click.stop>{{ row?.docCorrectFile?.name || '点击跳转试卷' }}
              </el-link>
              <el-text class="bolder" v-if="row?.docCorrectFile?.recordSize">份数：{{
                  row?.docCorrectFile?.recordSize
                }}份
              </el-text>
            </div>
          </template>
          <!-- 标准卷列（替换掉你现有的 #default 模板） -->
          <template v-if="column.prop === 'configPackageCompleted'" #default="{ row }">
            <!-- 没有标准卷时，仍用原来的标签 -->
            <el-tag
                v-if="!row?.docCorrectConfigPackage?.id"
                :type="row.configPackageCompleted? 'success' : 'warning'"
                style="cursor: pointer"
                class="bolder"
                @click.stop="quickEditRemark(row.id, 'configPackageCompleted', !row.configPackageCompleted)"
            >
              {{ row.configPackageCompleted ? '已完成' : '未完成' }}
            </el-tag>

            <!-- 有标准卷时：整块区域支持鼠标悬浮预览 -->
            <div
                v-else
                class="file-hover-trigger"
                style="display: flex; flex-direction: column"
                @mouseenter="showImg(toEmbUrl(row?.docCorrectConfigPackage?.firstImageUrl), $event)"
                @mousemove="moveImg($event)"
                @mouseleave="hideImg"
            >
              <el-link
                  type="primary"
                  :href="'/correctConfigPackages/stepConfigPapers/'+ row?.docCorrectConfigPackage?.id"
                  class="bolder"
                  @click.stop
              >
                {{ row?.docCorrectConfigPackage?.name || '点击跳转标准卷' }}
              </el-link>

              <el-text v-if="row?.docCorrectConfigPackage?.config" class="bolder">
                标注卷：{{ JSON.parse(row?.docCorrectConfigPackage?.config ?? '[]').length }}页
              </el-text>
            </div>
          </template>

          <template v-if="column.prop === 'exportCompleted'" #default="{ row }">
            <el-tag
                v-if="!(row?.docCorrectFile?.exportFolderPath && row?.exportCompleted)"
                :type="row?.exportCompleted ? 'success' : 'warning'"
                style="cursor: pointer"
                @click.stop="quickEditRemark(row.id, 'exportCompleted', !row?.exportCompleted)"
                class="bolder"
            >
              {{ row?.exportCompleted ? '已完成' : '未完成' }}
            </el-tag>
            <el-tooltip v-else placement="top" :content="row?.docCorrectFile.exportFolderPath">
              <el-link type="primary" @click.stop="jumpPrint(printUrl + row?.docCorrectFile.exportFolderPath)"> {{
                  row.docCorrectFile.exportFolderPath
                      .split('/')
                      .filter(Boolean)
                      .pop()
                }}
              </el-link>
            </el-tooltip>
          </template>

          <template v-if="column.prop === 'fileName'" #default="{ row }">
            <div style="display: flex;align-items: center">

              <div style="display: flex;flex-direction: column;width: 100%">

                <span
                    class="file-hover-trigger"
                    @mouseenter="showImg(row?.imgUrl, $event)"
                    @mousemove="moveImg($event)"
                    @mouseleave="hideImg"
                >
                  <el-link v-if="row?.fileDetail" type="primary"
                           :href="$fileserver.fileurl(JSON.parse(row.fileDetail).url)" target="_blank"
                           style="display:inline-block; white-space:normal; word-break:break-all;"
                           class="bolder"
                           @click.stop>
                    {{ row.fileName }}
                  </el-link>

                  <el-link v-else-if="row?.fileName && !row?.fileName.includes('.pdf')" type="primary"
                           @click.stop="jumpPrint(`${printUrl}${row?.filePath ?? ''}`)">
                    {{ row.fileName }}
                  </el-link>

                  <span v-else class="bolder">{{ row.fileName }}</span>
                </span>


                <!--                <el-tooltip v-if="row?.pdfPaperSize" placement="bottom"-->
                <!--                            :content="`宽：${row?.width}mm 高:${row?.height} mm`">-->
                <div class="bolder">pdf页数：{{ row?.pdfPaperSize }}
                  <el-text v-if="row?.docType" class="bolder">({{ row?.docType }})</el-text>
                </div>
                <!--                </el-tooltip>-->

              </div>
              <!--              <el-icon-->
              <!--                  :style="{ cursor: 'pointer', color: row.isCollect ? '#E6A23C' : '#c0c4cc', fontSize: '20px' }"-->
              <!--                  @click.stop="toggleCollect(row)"-->
              <!--              >-->
              <!--                <component :is="row?.isCollect ? 'StarFilled' : 'Star'"/>-->
              <!--              </el-icon>-->
            </div>

          </template>
          <template v-if="column.prop === 'remark'" #default="{ row }">
            <el-link type="primary" @click.stop="openRemark(row)">
              {{ row?.remark || '点我修改' }}
            </el-link>
          </template>
          <template v-if="column.prop === 'fileRemark'" #default="{ row }">
            <el-link type="primary"
                     @click.stop="openFileRemarkRemark(row)">
              {{ row?.docCorrectFile?.remark || row?.fileRemark || '点我修改' }}
            </el-link>
          </template>
          <template v-if="column.prop === 'operations'" #default="{ row }">
            <el-space :size="5" style="display: flex;flex-wrap: wrap;row-gap: 10px;justify-content: center">
              <el-button type="primary" text size="small" @click.stop="viewDetail(row)">去完成</el-button>
              <el-dropdown @click.stop>
                    <span>
                      <el-icon class="el-icon--right" @click.stop>
                        <more/>
                      </el-icon>
                    </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click.stop="showPdfPaperSize(row)">文件尺寸</el-dropdown-item>
                    <el-dropdown-item @click.stop="markAsCompleted(row, true)">修改状态</el-dropdown-item>
                    <el-dropdown-item @click.stop="onDelete(row.id)" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-space>
          </template>
          <template v-else-if="column.prop === 'isCorrectFinish'" #default="{ row }">
            <el-tag class="bolder" v-if="!row?.docCorrectFile"
                    type="warning">
              未完成
            </el-tag>
            <el-text v-else-if="row?.docCorrectFile?.status === 3">用时{{ getDurationText(row) }}</el-text>
            <el-text class="bolder" v-else-if="row?.docCorrectFile">已批改{{
                getMinutesAgo(row?.docCorrectFile?.lastCorrectTime)
              }}
            </el-text>
            <el-text v-else class="bolder">-</el-text>
          </template>
          <template v-else-if="column.prop === 'notificationTime'" #default="{ row }">
            <div class="bolder" style="display: flex;flex-direction: column">
              <div>{{ $getFeiShuTimeFormat(row.notificationTime) }}</div>
              <el-text class="bolder" v-if="row?.docCorrectFile?.status === 2" style="color: red">已批改{{
                  getMinutesAgo(row?.docCorrectFile?.lastCorrectTime)
                }}
              </el-text>
              <div v-else-if="row?.docCorrectFile?.exportFolderPath || row?.exportCompleted">
                处理用时{{ getMinutesAgo(row?.createTime, row?.exportCompletedTime) }}
              </div>
              <div v-else>已过去{{ getMinutesAgo(row?.createTime) }}</div>
            </div>
          </template>
          <template v-if="column.prop === 'isCollect'" #default="{ row }">
            <el-icon
                :style="{ cursor: 'pointer', color: row.isCollect ? '#E6A23C' : '#c0c4cc', fontSize: '20px' }"
                @click.stop="toggleCollect(row)"
            >
              <component :is="row.isCollect ? 'StarFilled' : 'Star'"/>
            </el-icon>
          </template>
          <!-- 匹配结果单元格 -->
          <template v-if="column.prop === 'matchStatus'" #default="{ row }">
            <div style="display:flex; flex-direction:column; align-items:center; gap:6px">
              <el-tag v-if="row.matchStatus === 'matching'" type="warning" class="bolder">匹配中</el-tag>
              <el-tag v-else-if="row.matchStatus === 'matchFailed'" type="danger" class="bolder">匹配失败</el-tag>

              <template v-else-if="row.matchStatus === 'matchSuccess'">
                <div class="bolder" v-if="row.matchedScore">匹配分数：{{ formatScore(row.matchedScore) }}</div>
                <div class="bolder" v-else>无匹配</div>
                <el-button
                    size="small"
                    type="primary"
                    @click.stop="openMatchDetail(row)"
                >
                  查看详情
                </el-button>
              </template>

              <span v-else class="bolder">-</span>
            </div>
          </template>

        </el-table-column>
      </el-table>
    </div>

    <div class="footer-bar">
      <el-pagination background layout="sizes, prev, pager, next"
                     :page-sizes="[5, 6, 7, 8, 9, 10, 20, 50, 100]"
                     v-model:page-size="ftpMessageIndexPageData.pageSize"
                     v-model:current-page="ftpMessageIndexPageData.pageNumber" :total="total"
                     @size-change="loadData()"
                     @current-change="onPageChange"/>
    </div>
    <el-dialog
        v-model="markDialogVisible"
        title="批量设置完成状态"
        width="400px"
        @close="resetMarkForm"
    >
      <el-form :model="markForm" label-width="120px">
        <el-form-item label="试卷完成状态">
          <el-switch
              v-model="markForm.fileCompleted"
              active-text="已完成"
              inactive-text="未完成"
          />
        </el-form-item>
        <el-form-item label="标准卷完成状态">
          <el-switch
              v-model="markForm.configPackageCompleted"
              active-text="已完成"
              inactive-text="未完成"
          />
        </el-form-item>
        <el-form-item label="导出完成状态">
          <el-switch
              v-model="markForm.exportCompleted"
              active-text="已完成"
              inactive-text="未完成"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="markDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmMark">保存</el-button>
      </template>
    </el-dialog>
    <el-dialog
        v-model="remarkDialogVisible"
        title="编辑备注"
        width="500px"
    >
      <el-form :model="remarkForm">
        <el-form-item label="备注" label-width="60px">
          <el-input
              v-model="remarkForm.remark"
              type="textarea"
              rows="4"
              placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="remarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRemark">保存</el-button>
      </template>
    </el-dialog>

    <el-dialog
        v-model="fileRemarkDialogVisible"
        title="编辑备注"
        width="500px"
    >
      <el-form :model="fileRemarkForm">
        <div class="hint-text" style="margin-left: 60px">例如：250611-崇文-英语-302</div>
        <el-form-item label="备注" label-width="60px">
          <el-input
              v-model="fileRemarkForm.remark"
              type="textarea"
              rows="4"
              placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="fileRemarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveFileRemark">保存</el-button>
      </template>
    </el-dialog>

    <el-dialog
        v-model="ftpMessageFileRemarkDialogVisible"
        title="编辑备注"
        width="500px"
    >
      <el-form :model="ftpMessageFileRemarkForm">
        <el-form-item label="备注" label-width="60px">
          <el-input
              v-model="ftpMessageFileRemarkForm.fileRemark"
              type="textarea"
              rows="4"
              placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="ftpMessageFileRemarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveFtpMessageFileRemark">保存</el-button>
      </template>
    </el-dialog>

    <el-dialog
        v-model="schoolRemarkDialogVisible"
        title="编辑学校备注"
        width="500px"
    >
      <el-form :model="schoolRemarkForm">
        <el-form-item label="备注" label-width="60px">
          <el-input
              v-model="schoolRemarkForm.remark"
              type="textarea"
              rows="4"
              placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item label="排序权重，越大越在前边" label-width="185px">
          <el-input-number
              v-model="schoolRemarkForm.weight"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="schoolRemarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveSchoolRemark">保存</el-button>
      </template>
    </el-dialog>

    <FileSelector ref="fileSelectorDialog"
                  type="uploadByPath">
    </FileSelector>

    <div
        v-if="hoverPreview.visible"
        class="hover-preview-img"
        :style="{ left: hoverPreview.x + 'px', top: hoverPreview.y + 'px' }"
    >
      <img :src="hoverPreview.src" alt="预览" />
    </div>

    <el-dialog
        v-model="matchDetailVisible"
        :title="matchDetailTitle || '匹配详情'"
        width="1000px"
    >
      <el-table :data="matchDetailList" height="680" stripe>
        <el-table-column type="index" label="#" width="60" align="center"/>
        <el-table-column label="预览"  align="center">
          <template #default="{ row }">
            <el-image
                v-if="row.image"
                :src="row.image"
                :preview-src-list="[row.image]"
                fit="cover"
                style="width:360px;border-radius:6px; border:1px solid #eee;"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" width="200" show-overflow-tooltip/>
<!--        <el-table-column prop="text" label="文本" show-overflow-tooltip/>-->
        <el-table-column label="相似度" width="120" align="center">
          <template #default="{ row }">
            {{ formatScore(row.score) }}
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="matchDetailVisible = false">关闭</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import {ElLoading} from "element-plus";

const printUrl = import.meta.env.VITE_PRINT_URL
import {useUserStore} from "@/store";
import {Star, StarFilled} from '@element-plus/icons-vue';
import FileSelector from "@/views/common/FileSelector.vue";

const store = useUserStore();
export default {
  name: 'FtpMessageManage',
  components: {
    FileSelector,
    Star,
    StarFilled
  },
  data() {
    return {
      hoverPreview: {
        visible: false,
        src: '',
        x: 0,
        y: 0
      },
      ftpMessageIndexPageData: {
        formData: {
          schoolName: '',
          fileName: '',
          filePath: '',
          fileCompleted: null,
          configPackageCompleted: null,
          exportCompleted: null,
          dateRange: [],
          isSelectToday: false,
          fileRemark: '',
          isCollect: null,
          isCorrectFinish: null,
          remark: ''
        },
        pageNumber: 1,
        pageSize: 10,
        activeTitle: ''
      },
      tableData: [],
      columns: [
        {type: 'index', label: '序号', width: 80},
        // {prop: 'isCollect', label: '收藏', width: 70},
        {prop: 'schoolName', label: '标题', width: 100},
        {prop: 'fileName', label: '文件名称', width: 260},
        // {prop: 'filePath', label: '文件路径'},
        // {prop: 'notificationCount', label: '通知次数', width: 120},
        {prop: 'configPackageCompleted', label: '标准卷', width: 260},
        {prop: 'fileCompleted', label: '试卷', width: 230},
        // {prop: 'isCorrectFinish', label: '批改进度', width: 120},
        {prop: 'exportCompleted', label: '导出进度', width: 150},
        {prop: 'remark', label: '备注'},
        {prop: 'fileRemark', label: '试卷备注'},
        {prop: 'matchStatus', label: '匹配结果', width: 130},
        {prop: 'notificationTime', label: '通知时间', width: 180},
        // { prop: 'createTime', label: '创建时间', width: 180 },
        {prop: 'operations', label: '操作', width: 120}
      ],

      total: 0,
      loading: false,
      remarkDialogVisible: false,
      remarkForm: {
        id: null,
        remark: ''
      },
      fileRemarkForm: {
        id: null,
        remark: ''
      },
      ftpMessageFileRemarkForm: {
        id: null,
        fileRemark: ''
      },
      schoolRemarkForm: {
        id: null,
        remark: '',
        weight: 0
      },
      markDialogVisible: false,
      fileRemarkDialogVisible: false,
      schoolRemarkDialogVisible: false,
      ftpMessageFileRemarkDialogVisible: false,
      markForm: {
        id: null,
        fileCompleted: false,
        configPackageCompleted: false,
        exportCompleted: false
      },
      titleOptions: [],
      titleLoading: false,
      allTitles: [],
      isCorrectFinishOptions: {
        0: {value: 2, label: "纠错中", type: "warning"},
        1: {value: 3, label: "已完成", type: "success"},
      },
      printUrl: printUrl,
      activeFtpMessageTitleObj: null,
      allUnFinishJobs: null,
      debounceTimer: null,
      refreshTimer: null,
      statusCount: {
        inProgress: 0,
        completed: 0,
        totalQuantity: 0
      },
      filterVisible: {
        fileCompleted: false,
        exportCompleted: false,
        isCollect: false,
        configPackageCompleted: false,
        isCorrectFinish: false,
        remark: false,
        fileName: false,
        schoolName: false
      },
      // 颜色相关
      colorPalette: [
        '#e0ece0',
        '#f1f6f0',
        '#eef5fb',
        '#ECEFF1'
      ],
      nameColorMap: {},
      colorPaletteIdx: 0,
      matchDetailVisible: false,
      matchDetailTitle: '',
      matchDetailList: [], // 解析后的结果数组

    };
  },
  watch: {
    ftpMessageIndexPageData: {
      handler(newVal) {
        store.setFtpMessageIndexPageData(newVal);
      },
      deep: true
    },
    'ftpMessageIndexPageData.formData.isSelectToday': {
      handler(newVal) {
        this.loadData(); // 当 isSelectToday 变化时，重新加载数据
      }
    }
  },
  computed: {
    visibleColumns() {
      return this.columns.filter(column => {
        if (column.prop === 'schoolName') {
          return this.ftpMessageIndexPageData.activeTitle === ''
        }
        return true
      })
    }
  },
  methods: {
    // 分数保留两位小数
    formatScore(score) {
      if (score === null || score === undefined || isNaN(Number(score))) return '-';
      return Number(score).toFixed(2);
    },

// TOS 地址转公网地址（优先替换 images/，再兜底替换根前缀）
    toEmbUrl(tosUrl) {
      if (!tosUrl || typeof tosUrl !== 'string') return '';
      let url = tosUrl.replace('tos://embedding/images/', 'https://embedding.tos-cn-beijing.volces.com/');
      if (url === tosUrl) {
        url = tosUrl.replace('tos://embedding/', 'https://embedding.tos-cn-beijing.volces.com/');
      }
      return url;
    },

    openMatchDetail(row) {
      try {
        const jsonStr = row?.macthResult || row?.matchResult || '[]';
        const raw = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;

        // 规范化为表格可用结构
        this.matchDetailList = (raw || []).map((it, idx) => {
          const fields = it.fields || {};
          return {
            idx: idx + 1,
            id: it.id || fields.id || '',
            text: fields.text || '',
            score: it.score,
            isBuild: it.isBuild,
            image: this.toEmbUrl(fields.image || '')
          };
        });

        this.matchDetailTitle = `匹配详情 - ${row?.fileName || row?.schoolName || ''}`;
        this.matchDetailVisible = true;
      } catch (e) {
        this.$message.error('匹配结果解析失败');
      }
    },
    showImg(src, e) {
      if (!src) return; // 没图就不弹
      this.hoverPreview.src = src;
      this.hoverPreview.visible = true;
      this.moveImg(e);
    },
    moveImg(e) {
      if (!this.hoverPreview.visible) return;
      const offset = 16; // 鼠标偏移，避免挡住指针
      let x = e.clientX + offset;
      let y = e.clientY + offset;

      // 简单的防出屏处理（可选）
      const vw = window.innerWidth;
      const vh = window.innerHeight;
      const maxW = 380; // 预览最大宽度（见样式）
      const maxH = 380; // 预览最大高度
      if (x + maxW > vw) x = e.clientX - maxW - offset;
      if (y + maxH > vh) y = e.clientY - maxH - offset;

      this.hoverPreview.x = x;
      this.hoverPreview.y = y;
    },
    hideImg() {
      this.hoverPreview.visible = false;
      this.hoverPreview.src = '';
    },
    jumpPrint(path) {
      window.open(path, '_blank');
    },
    getDurationText(row) {
      const file = row?.docCorrectFile;
      if (!file?.lastCorrectTime || !file?.finishCorrectTime) {
        return '';
      }

      const diff = new Date(file.finishCorrectTime) - new Date(file.lastCorrectTime);
      const m = Math.floor(diff / 60000);
      const s = Math.floor((diff % 60000) / 1000);

      return m > 0
          ? `${m}分${s}秒`
          : `${s}秒`;
    },
    onFilterChange(prop) {
      this.filterVisible[prop] = false;
      this.loadData();
    },
    toggleCollect(row) {
      const newValue = !row.isCollect;
      this.$axios
          .post('/api/ftpMessage/update', {
            id: row.id,
            isCollect: newValue
          })
          .then(() => {
            this.$message.success(newValue ? '已收藏' : '取消收藏');
            this.loadData();
          })
          .catch(err => {
            this.$message.error('更新失败：' + err.message);
          });
    },
    /**
     * 计算从 lastTime 到 nowTime（或当前时间）的时间差，格式为：
     *   · 小于 1 分钟： "26s"
     *   · 1 分钟–1 小时： "1分30s"
     *   · ≥1 小时： "2小时15分10s"
     * @param {string|number|Date} lastTime — 起始时间
     * @param {string|number|Date|null} nowTime — 结束时间；若为 null，则使用当前时间
     * @returns {string} 时分秒格式的时间差
     */
    getMinutesAgo(lastTime, nowTime = null) {
      if (!lastTime) return '-';

      const lastTs = new Date(lastTime).getTime();
      if (isNaN(lastTs)) return '--';

      const endTs = nowTime != null
          ? new Date(nowTime).getTime()
          : Date.now();
      if (isNaN(endTs)) return '--';

      const diffMs = endTs - lastTs;
      if (diffMs < 0) return '0s';

      const totalSeconds = Math.floor(diffMs / 1000);

      // 超过 72 小时，直接返回 "72h"
      if (totalSeconds >= 72 * 3600) {
        return '72h';
      }

      if (totalSeconds < 60) {
        return `${totalSeconds}s`;
      }

      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      if (hours > 0) {
        return `${hours}小时${minutes}分${seconds}s`;
      } else {
        return `${minutes}分${seconds}s`;
      }
    },

    onTitleSelect(title) {
      this.ftpMessageIndexPageData.activeTitle = title;
      this.ftpMessageIndexPageData.formData.schoolName = title;

      this.activeFtpMessageTitleObj = this.allTitles.find(item => item.title === title);
      this.loadData();
    },
    loadAllTitles() {
      const params = {
        page: {
          pageNumber: 1,
          pageSize: 9999
        }
      }
      this.$axios
          .post(`/api/ftpMessageTitle/select`, params)
          .then(res => {
            this.allTitles = res.data.records;
            // 设置一下active
            this.onTitleSelect(this.ftpMessageIndexPageData.activeTitle || '');
          });
    },
    selectUnFinishJobs() {
      this.$axios
          .get(`/api/ftpMessage/selectStatusCount`)
          .then(res => {
            this.allUnFinishJobs = res.data.unFinishJobs;

            let keys = Object.keys(this.allUnFinishJobs);
            // 统计全部
            this.allUnFinishJobs['全部'] = keys.reduce((sum, key) => sum + (this.allUnFinishJobs[key] || 0), 0);

            this.statusCount = res.data;
          });
    },
    /**
     * 远程搜索标题列表
     * @param {string} query 用户输入的搜索关键字
     */
    remoteSearchTitle(query) {
      if (query !== '') {
        this.titleLoading = true;
        this.$axios
            .get(`/api/ftpMessage/titles?title=${query}`)
            .then(res => {
              // 假设后端返回格式为 { data: [{ id, name }, …] }
              const titleList = res.data;
              // 将返回的数据格式化为 el-select 需要的格式
              this.titleOptions = titleList.map(item => ({
                id: item,
                name: item
              }));
            })
            .catch(() => {
              this.titleOptions = [];
            })
            .finally(() => {
              this.titleLoading = false;
            });
      } else {
        this.titleOptions = [];
      }
    },
    calculateNameColorMap(data) {
      const freq = {};
      data.forEach(row => {
        if (row.schoolName) {
          freq[row.schoolName] = (freq[row.schoolName] || 0) + 1;
        }
      });

      const map = this.nameColorMap;
      let idx = this.colorPaletteIdx;
      const colorPalette = this.colorPalette;

      data.forEach(row => {
        const schoolName = row.schoolName;
        if (schoolName && freq[schoolName] > 1 && !map[schoolName]) {
          map[schoolName] = colorPalette[idx % colorPalette.length];
          idx++;
        }
      });

      this.nameColorMap = map;
      this.colorPaletteIdx = idx;
    },
    getRowStyle({row}) {
      const bg = this.nameColorMap[row.schoolName];
      return bg ? {background: bg} : {};
    },
    goBack() {
      this.$router.back();
    },
    loadData() {
      this.loading = true;
      const [start, end] = this.ftpMessageIndexPageData.formData.dateRange;
      // const loadingInstance = ElLoading.service({})
      this.$axios
          .post('/api/ftpMessage/select', {
            page: {
              pageNumber: this.ftpMessageIndexPageData.pageNumber,
              pageSize: this.ftpMessageIndexPageData.pageSize
            },
            schoolName: this.ftpMessageIndexPageData.formData.schoolName,
            fileName: this.ftpMessageIndexPageData.formData.fileName,
            filePath: this.ftpMessageIndexPageData.formData.filePath,
            fileCompleted: this.ftpMessageIndexPageData.formData.fileCompleted,
            configPackageCompleted: this.ftpMessageIndexPageData.formData.configPackageCompleted,
            exportCompleted: this.ftpMessageIndexPageData.formData.exportCompleted,
            notificationTimeStart: start,
            notificationTimeEnd: end,
            isSelectToday: this.ftpMessageIndexPageData.formData.isSelectToday,
            fileRemark: this.ftpMessageIndexPageData.formData.fileRemark,
            isCollect: this.ftpMessageIndexPageData.formData.isCollect,
            isCorrectFinish: this.ftpMessageIndexPageData.formData.isCorrectFinish,
            remark: this.ftpMessageIndexPageData.formData.remark
          })
          .then(res => {
            const data = res.data;
            this.calculateNameColorMap(data.records);
            this.tableData = data.records;
            this.total = data.total;
          })
          .finally(() => {
            setTimeout(() => {
              this.loading = false;
              // loadingInstance.close();
            }, 500);
          });
    },
    reset() {
      this.ftpMessageIndexPageData.formData = {
        schoolName: '',
        fileName: '',
        filePath: '',
        fileCompleted: null,
        configPackageCompleted: null,
        exportCompleted: null,
        dateRange: [],
        isSelectToday: false,
        fileRemark: ''
      };
      this.ftpMessageIndexPageData.pageNumber = 1;
      this.loadData();
    },
    onPageChange(page) {
      this.ftpMessageIndexPageData.pageNumber = page;
      this.loadData();
    },
    onDelete(id) {
      this.$confirm('确定要删除该条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => this.$axios.get('/api/ftpMessage/delete', {params: {id}}))
          .then(() => {
            this.$message.success('删除成功');
            this.loadData();
            this.selectUnFinishJobs();
          })
          .catch(() => {
          });
    },
    handleRowClick(row) {
      console.log('row', row)
    },
    viewDetail(row) {
      let fileDetail = row?.fileDetail ? JSON.parse(row.fileDetail) : {};
      fileDetail.fileRemark = row?.fileRemark || '';
      // formFileName
      let topic = row?.topic ? JSON.parse(row.topic) : {};
      let formFileName = `${row?.schoolName}-${topic?.subject || ''}-${topic?.className || ''}`
      fileDetail.formFileName = formFileName;
      console.log('formFileName', formFileName)
      fileDetail = JSON.stringify(fileDetail);
      const fileDetailEncoded = encodeURIComponent(fileDetail);
      this.$confirm('请选择去向', '', {
        confirmButtonText: '上传试卷',
        cancelButtonText: '上传标准卷',
        type: 'warning',
        distinguishCancelAndClose: true
      })
          .then(() => {
            this.$router.push(`/markPapers/upload?ftpMessageDetail=${fileDetailEncoded}&ftpMessageFileId=${row.id}`);
          })
          .catch((type) => {
            if (type === 'cancel') {
              this.$router.push(
                  `/correctConfigPackages/upload?ftpMessageDetail=${fileDetailEncoded}&ftpMessageFileId=${row.id}`
              );
            }
          });
    },
    markAsCompleted(row, allFinish = false) {
      this.markForm.id = row.id;
      this.markForm.fileCompleted = allFinish ? true : row.fileCompleted;
      this.markForm.configPackageCompleted = allFinish ? true : row.configPackageCompleted;
      this.markForm.exportCompleted = allFinish ? true : row.exportCompleted;
      this.markDialogVisible = true;
    },
    resetMarkForm() {
      this.markForm = {
        id: null,
        fileCompleted: false,
        configPackageCompleted: false,
        exportCompleted: false
      };
    },
    quickEditRemark(id, key, value) {
      let param = {
        id
      }
      param[key] = value;
      this.$axios
          .post('/api/ftpMessage/update', param)
          .then(() => {
            this.$message.success('完成状态更新成功');
            this.loadData();
            this.selectUnFinishJobs();
          })
          .catch(err => {
            this.$message.error('更新失败：' + err.message);
          });
    },
    confirmMark() {
      this.$axios
          .post('/api/ftpMessage/update', {
            id: this.markForm.id,
            fileCompleted: this.markForm.fileCompleted,
            configPackageCompleted: this.markForm.configPackageCompleted,
            exportCompleted: this.markForm.exportCompleted
          })
          .then(() => {
            this.$message.success('完成状态更新成功');
            this.markDialogVisible = false;
            this.loadData();
            this.selectUnFinishJobs();
          })
          .catch(err => {
            this.$message.error('更新失败：' + err.message);
          });
    },
    showPdfPaperSize(row) {
      this.$confirm(`宽: ${row.width}mm 高:${row?.height}mm`, '提示', {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      });
    },
    openRemark(row) {
      this.remarkForm.id = row.id;
      this.remarkForm.remark = row.remark || '';
      this.remarkDialogVisible = true;
    },
    openSchoolRemark() {
      const row = this.activeFtpMessageTitleObj
      this.schoolRemarkForm.id = row.id;
      this.schoolRemarkForm.remark = row.remark || '';
      this.schoolRemarkForm.weight = row.weight ?? 0;
      this.schoolRemarkDialogVisible = true;
    },
    openFileRemarkRemark(row) {
      if (!row?.docCorrectFile?.id) {
        this.ftpMessageFileRemarkForm.id = row?.id;
        this.ftpMessageFileRemarkForm.remark = row?.fileRemark || '';
        this.ftpMessageFileRemarkDialogVisible = true;
      } else {
        this.fileRemarkForm.id = row?.docCorrectFile.id;
        this.fileRemarkForm.remark = row?.docCorrectFile?.remark || '';
        this.fileRemarkDialogVisible = true;
      }

    },
    saveRemark() {
      this.$axios
          .post('/api/ftpMessage/update', {
            id: this.remarkForm.id,
            remark: this.remarkForm.remark
          })
          .then(() => {
            this.$message.success('备注保存成功');
            this.remarkDialogVisible = false;
            this.loadData();
          })
          .catch(err => {
            this.$message.error('保存失败：' + err.message);
          });
    },
    saveFtpMessageFileRemark() {
      this.$axios
          .post('/api/ftpMessage/update', {
            id: this.ftpMessageFileRemarkForm.id,
            fileRemark: this.ftpMessageFileRemarkForm?.fileRemark
          })
          .then(() => {
            this.$message.success('备注保存成功');
            this.ftpMessageFileRemarkDialogVisible = false;
            this.loadData();
          })
          .catch(err => {
            this.$message.error('保存失败：' + err.message);
          });
    },
    saveFileRemark() {
      if (!this.fileRemarkForm.id) return;
      this.$axios
          .post('/api/docCorrectFile/update', {
            id: this.fileRemarkForm.id,
            remark: this.fileRemarkForm.remark
          })
          .then(() => {
            this.$message.success('备注保存成功');
            this.fileRemarkDialogVisible = false;
            this.loadData();
          })
          .catch(err => {
            this.$message.error('保存失败：' + err.message);
          });
    },
    saveSchoolRemark() {
      if (!this.schoolRemarkForm.id) return;
      this.$axios
          .post('/api/ftpMessageTitle/update', {
            id: this.schoolRemarkForm.id,
            remark: this.schoolRemarkForm.remark,
            weight: this.schoolRemarkForm.weight
          })
          .then(() => {
            this.$message.success('备注保存成功');
            this.schoolRemarkDialogVisible = false;
            this.loadAllTitles();
          })
          .catch(err => {
            this.$message.error('保存失败：' + err.message);
          });
    },
    debouncedLoadData() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.debounceTimer = setTimeout(() => {
        this.loadData();
      }, 300);
    },
  },
  created() {
    const ftpMessageIndexPageData = store.getFtpMessagePageDataIndexPageData;
    if (ftpMessageIndexPageData) {
      this.ftpMessageIndexPageData = JSON.parse(JSON.stringify(ftpMessageIndexPageData));
    }
    this.loadData();
    this.loadAllTitles();
    this.selectUnFinishJobs();

    this.refreshTimer = setInterval(() => {
      this.loadData();
      this.selectUnFinishJobs();
    }, 30000)
  },
  beforeDestroy() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-page-header__header) {
  width: 100% !important;
}

.bolder {
  font-weight: 500 !important;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: scroll; /* 强制显示滚动机制 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */

  .header-bar {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-bottom: 20px;

    .left-icon {
      width: 28.28px;
      height: 22.89px;
      transform: scaleX(-1);
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-right: 19px;
    }

    .header-action {
      margin-right: 15px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
        font-size: 20px !important;
        font-weight: bold !important;
      }
    }

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
  }

  .footer-bar {
    height: 80px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.hint-text {
  font-size: 13px;
  color: #999999;
  font-style: italic;
  white-space: nowrap;
}

.title-menu {
  // 覆盖Element Plus横向菜单激活tab样式
  :deep(.el-menu--horizontal) {
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-menu--horizontal .el-menu-item) {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    background: transparent;
    border: none;
    border-radius: 0;
    transition: all 0.2s;
    padding: 0 24px;
    height: 56px;
    line-height: 56px;
    margin-right: 8px;
    position: relative;
    overflow: visible;
    border-bottom: none;
  }

  :deep(.el-menu--horizontal .el-menu-item.is-active) {
    color: #0057fe !important;
    font-weight: bold;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    z-index: 1;
    border-bottom: none;
  }

  // 激活状态的蓝色底边条，与菜单底边框重合
  :deep(.el-menu--horizontal .el-menu-item.is-active::after) {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: #0057fe;
    z-index: 2;
  }

  // 鼠标悬浮效果
  :deep(.el-menu--horizontal .el-menu-item:not(.is-active):hover) {
    color: #0057fe;
    background: transparent;
  }

  :deep(.el-menu--horizontal .el-menu-item:not(.is-active):hover::after) {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e6f3ff;
    z-index: 2;
  }

  :deep(.el-badge__content.is-fixed) {
    top: 2px !important; // 再向上
    right: -12px !important; // 再向右
    transform: none !important;
  }
}

:deep(.el-statistic__head) {
  font-size: 18px !important;
  font-weight: bold !important;
}

:deep(.el-statistic__content .el-statistic__number) {
  font-size: 18px !important;
  font-weight: normal !important;
}

/* 预览层：固定定位，不受表格单元格 overflow 影响 */
.hover-preview-img {
  position: fixed;
  z-index: 9999;
  background: #fff;
  padding: 6px;
  border: 1px solid #e5e6eb;
  box-shadow: 0 6px 18px rgba(0,0,0,0.15);
  border-radius: 6px;
  pointer-events: none; /* 不抢鼠标事件，避免闪烁 */
}

.hover-preview-img img {
  display: block;
  max-width: 1000px;
  max-height: 1000px;
}

</style>
