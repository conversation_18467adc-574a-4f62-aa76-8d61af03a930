import {defineStore} from 'pinia'
import {getAuthData} from '@/api/auth'

export const useUserStore = defineStore("user", {
    state: () => ({
        user: null,
        school: null,
        roles: null,
        isShowLogin: false,
        locale: null,
        guideStatus: {
            markPaper: true,
            essay: true,
            packageConfig: true,
            markPaperFileTask: null,
        },
        aimodelOptions: {
            // 推荐第一个
            "doubao-1-5-vision-pro-32k-250115": {
                value: "doubao-1-5-vision-pro-32k-250115",
                label: "doubao-1-5-vision-pro-32k-250115(直接输出json)",
                responseFormat: true,
                jsonobject: false,
                jsonschema: false
            },

            "doubao_Seed_16_flash_jsonschema": {
                value: "doubao-seed-1-6-flash-250615",
                label: "doubao_Seed_16_flash(jsonschema)",
                responseFormat: true,
                jsonobject: false,
                jsonschema: true
            },

            "doubao_Seed_16_jsonschema": {
                value: "doubao-seed-1-6-250615",
                label: "doubao_Seed_16(jsonschema)",
                responseFormat: true,
                jsonobject: false,
                jsonschema: true
            },

            //jsonobject
            // "doubao-15-thinking-vision-pro-250428-jsonobject": {
            //     value: "doubao-1-5-thinking-vision-pro-250428",
            //     label: "doubao-15-thinking-vision-pro-250428(jsonobject)",
            //     responseFormat: true,
            //     jsonobject: true,
            //     jsonschema: false
            // },

            "doubao-1.5-vision-pro-250328_jsonobject": {
                value: "doubao-1.5-vision-pro-250328",
                label: "doubao-1.5-vision-pro-250328(jsonobject)",
                responseFormat: true,
                jsonobject: true,
                jsonschema: false
            },

            //jsonschema
            // "doubao-15-thinking-vision-pro-250428-jsonschema": {
            //     value: "doubao-1-5-thinking-vision-pro-250428",
            //     label: "doubao-15-thinking-vision-pro-250428(jsonschema)",
            //     responseFormat: true,
            //     jsonobject: false,
            //     jsonschema: true
            // },

            // "doubao-15-thinking-vision-pro-250428": {
            //     value: "doubao-1-5-thinking-vision-pro-250428",
            //     label: "doubao-15-thinking-vision-pro-250428(直接输出json)",
            //     responseFormat: true,
            //     jsonobject: false,
            //     jsonschema: false
            // },
            "doubao-1.5-vision-pro-250328": {
                value: "doubao-1.5-vision-pro-250328",
                label: "doubao-1.5-vision-pro-250328(直接输出json)",
                responseFormat: true
            },

            // "doubao-1-5-thinking-pro-m-250415": {
            //     value: "doubao-1-5-thinking-pro-m-250415",
            //     label: "doubao-1-5-thinking-pro-m-250415(直接输出json)",
            //     responseFormat: true
            // },
            // "doubao-1-5-thinking-pro-250415": {
            //     value: "doubao-1-5-thinking-pro-250415",
            //     label: "doubao-1-5-thinking-pro-250415(直接输出json)",
            //     responseFormat: true
            // },
            "doubao-vision-pro-32k-241028": {
                value: "doubao-vision-pro-32k-241028",
                label: "doubao-vision-pro-32k-241028(直接输出json)",
                responseFormat: true
            },
            "doubao-vision-lite-32k-241015": {
                value: "doubao-vision-lite-32k-241015",
                label: "doubao-vision-lite-32k-241015(直接输出json)",
                responseFormat: true
            },
            "doubao-1.5-ui-tars-250328": {
                value: "doubao-1.5-ui-tars-250328",
                label: "doubao-1.5-ui-tars-250328(直接输出json)",
                responseFormat: true
            },
            "doubao-embedding-vision-250328": {
                value: "doubao-embedding-vision-250328",
                label: "doubao-embedding-vision-250328(直接输出json)",
                responseFormat: true
            },
            "doubao-1.5-vision-lite-250315": {
                value: "doubao-1.5-vision-lite-250315",
                label: "doubao-1.5-vision-lite-250315(直接输出json)",
                responseFormat: true
            },

            // "doubao_Seed_16_thinking": {
            //     value: "doubao-seed-1-6-thinking-250615",
            //     label: "doubao_Seed_16_thinking(直接输出json)",
            //     responseFormat: true
            // },

            // "doubao_Seed_16_thinking_jsonobject": {
            //     value: "doubao-seed-1-6-thinking-250615",
            //     label: "doubao_Seed_16_thinking(jsonobject)",
            //     responseFormat: true,
            //     jsonobject: true,
            //     jsonschema: false
            // },

            // "doubao_Seed_16_thinking_jsonschema": {
            //     value: "doubao-seed-1-6-thinking-250615",
            //     label: "doubao_Seed_16_thinking(jsonschema)",
            //     responseFormat: true,
            //     jsonobject: false,
            //     jsonschema: true
            // },

            "doubao_Seed_16": {
                value: "doubao-seed-1-6-250615",
                label: "doubao_Seed_16(直接输出json)",
                responseFormat: true
            },

            "doubao_Seed_16_jsonobject": {
                value: "doubao-seed-1-6-250615",
                label: "doubao_Seed_16(jsonobject)",
                responseFormat: true,
                jsonobject: true,
                jsonschema: false
            },

            "doubao_Seed_16_flash": {
                value: "doubao-seed-1-6-flash-250615",
                label: "doubao_Seed_16_flash(直接输出json)",
                responseFormat: true
            },

            "doubao_Seed_16_flash_jsonobject": {
                value: "doubao-seed-1-6-flash-250615",
                label: "doubao_Seed_16_flash(jsonobject)",
                responseFormat: true,
                jsonobject: true,
                jsonschema: false
            },

            "gemini-2.0-flash-exp": {
                value: "gemini-2.0-flash-exp",
                label: "gemini-2.0-flash-exp",
                responseFormat: true
            },
            "gpt-4o": {value: "gpt-4o", label: "gpt-4o", vision: true},
            "gpt-4o-2024-08-06": {
                value: "gpt-4o-2024-08-06",
                label: "gpt-4o-2024-08-06",
                vision: true,
                responseFormat: true
            },
            "gpt-4o-2024-08-06_3": {
                value: "gpt-4o-2024-08-06_3",
                label: "gpt-4o-2024-08-06_3",
                vision: true,
                responseFormat: true
            },
            "gpt-4o-mini": {value: "gpt-4o-mini", label: "gpt-4o-mini", vision: true, responseFormat: true},
            "gpt-4-vision-preview": {value: "gpt-4-vision-preview", label: "gpt-4-vision-preview", vision: true},
            "Claude 3 Opus": {value: "Claude 3 Opus", label: "Claude 3 Opus", vision: true},
            "Claude 3 Sonnet": {value: "Claude 3 Sonnet", label: "Claude 3 Sonnet", vision: true},
            "gpt-4-turbo": {value: "gpt-4-turbo", label: "gpt-4-turbo"},
            "gpt-4": {value: "gpt-4", label: "gpt-4"},
            "gpt-3.5-turbo": {value: "gpt-3.5-turbo", label: "gpt-3.5-turbo"},
            "ERNIE-Bot": {value: "ERNIE-Bot", label: "ERNIE-Bot"},
            "qwen-turbo": {value: "qwen-turbo", label: "qwen-turbo"},
            "qwen-plus": {value: "qwen-plus", label: "qwen-plus"},
            "qwen-max": {value: "qwen-max", label: "qwen-max"},

        },
        allXYOffsetDataById: {},
        defaultConfigs: null,
        defaultCorrectModal: 'doubao-1-5-vision-pro-32k-250115',
        defaultDocType: '8k',
        isCollapse: null,
        markPaperLeftWidth: 0,
        nowCropperByFileIds: {},
        isFreeDraw: null,
        isAutoSortArea: null,
        markPaperIndexPageData: null,
        correctConfigPackagesIndexPageData: null,
        essayPapersIndexPageData: null,
        comprehensiveIndexPageData: null,
        ftpMessageIndexPageData: null,
        allMarkZoomDataById: {},
        allFontSizeDataById: {},
        allOpacityDataById: {},
        downloadSelectPrintUser: null,
        uploadSelectPrintUser: null,
        imagesList: {},
        tempCollapse: false
    }),
    getters: {
        getUser: (state) => {
            return state.user || JSON.parse(localStorage.getItem('user'));
        },
        getRoles: (state) => {
            return state.roles || JSON.parse(localStorage.getItem('roles'));
        },
        getSchool: (state) => {
            return state?.school || (() => {
                const str = localStorage.getItem('school');
                if (!str || str === 'undefined') return {};
                try {
                    return JSON.parse(str);
                } catch (e) {
                    return {};
                }
            })();

        },
        getLocale: (state) => {
            let locale = state.locale || localStorage.getItem('locale');
            if (!locale) {
                locale = "cn"
                state.locale = locale
            }
            return locale
        },
        needMarkPaperGuide: (state) => state.guideStatus.markPaper,
        needEssayGuide: (state) => state.guideStatus.essay,
        needPackageConfigGuide: (state) => state.guideStatus.packageConfig,
        needMarkPaperFileTaskGuide: (state) => {
            if (state.guideStatus.markPaperFileTask === null || state.guideStatus.markPaperFileTask === undefined) {
                state.guideStatus.markPaperFileTask = JSON.parse(localStorage.getItem('needMarkPaperFileTaskGuide')) ?? true;
            }
            return state.guideStatus.markPaperFileTask;
        },
        getAimodelOptions: (state) => {
            return JSON.parse(localStorage.getItem('aimodelOptions')) || state.aimodelOptions;
        },
        getRoleAuths: (state) => {
            const menus = new Set()
            if (!state?.getRoles) return []
            state.getRoles.forEach(role => {
                const roleAuth = role.roleAuth && JSON.parse(role.roleAuth)
                if (roleAuth && roleAuth.menus) {
                    roleAuth.menus.forEach(menu => {
                        menus.add(menu)
                    })
                }
            })
            return menus
        },
        getXYOffsetData: (state) => {
            return (id) => {
                return state.allXYOffsetDataById[id] || JSON.parse(localStorage.getItem('allXYOffsetDataById'))?.[id] || {
                    xOffset: 0,
                    yOffset: 0
                }
            }
        },
        getMarkZoomDataById: (state) => {
            return (id) => {
                return state.allMarkZoomDataById[id] || JSON.parse(localStorage.getItem('allMarkZoomDataById'))?.[id] || 80
            }
        },
        getFontSizeDataById: (state) => {
            return (id) => {
                return state.allFontSizeDataById[id] || JSON.parse(localStorage.getItem('allFontSizeDataById'))?.[id] || 80
            }
        },
        getOpacityDataById: (state) => {
            return (id) => {
                return state.allOpacityDataById[id] || JSON.parse(localStorage.getItem('allOpacityDataById'))?.[id] || 0.6
            }
        },
        getImageDataById: (state) => {
            return (id) => {
                if (id in state.imagesList) {
                    return state.imagesList[id]
                } else {
                    return JSON.parse(localStorage.getItem('imagesList'))?.[id];
                }
            }
        },
        getDefaultConfigs: (state) => {
            return state.defaultConfigs || JSON.parse(localStorage.getItem('defaultConfigs')) || {};
        },
        getDefaultConfig: (state) => {
            return (id) => {
                return state.defaultConfigs[id] || JSON.parse(localStorage.getItem('defaultConfigs'))?.[id] || ''
            }
        },
        getNowCropperByFileIds: (state) => {
            return (id) => {
                return state.nowCropperByFileIds[id] || JSON.parse(localStorage.getItem('nowCropperByFileIds'))?.[id] || null
            }
        },
        getDefaultCorrectModal(state) {
            return JSON.parse(localStorage.getItem('defaultCorrectModal')) || state.defaultCorrectModal;
        },
        getDefaultJsonObject(state) {
            return false;
        },
        getDefaultJsonSchema(state) {
            return false;
        },
        getDefaultDocType: (state) => {
            return state.defaultDocType;
        },
        getIsCollapse: (state) => {
            if (state.isCollapse === null) {
                state.isCollapse = JSON.parse(localStorage.getItem('isCollapse')) ?? false;
            }
            return state.isCollapse;
        },
        getMarkPaperLeftWidth(state) {
            if (state.markPaperLeftWidth === 0) {
                state.markPaperLeftWidth = JSON.parse(localStorage.getItem('markPaperLeftWidth')) ?? 580;
            }
            return state.markPaperLeftWidth;
        },
        getIsFreeDraw(state) {
            if (state.isFreeDraw === null || state.isFreeDraw === undefined) {
                state.isFreeDraw = JSON.parse(localStorage.getItem('isFreeDraw')) ?? false;
            }
            return state.isFreeDraw;
        },
        getIsAutoSortArea(state) {
            if (state.isAutoSortArea === null || state.isAutoSortArea === undefined) {
                state.isAutoSortArea = JSON.parse(localStorage.getItem('isAutoSortArea')) ?? false;
            }
            return state.isAutoSortArea;
        },
        getMarkPaperIndexPageData(state) {
            if (state.markPaperIndexPageData === null) {
                state.markPaperIndexPageData = JSON.parse(localStorage.getItem('markPaperIndexPageData')) ?? null;
            }
            return state.markPaperIndexPageData;
        },
        getCorrectConfigPackagesIndexPageData(state) {
            if (state.correctConfigPackagesIndexPageData === null) {
                state.correctConfigPackagesIndexPageData = JSON.parse(localStorage.getItem('correctConfigPackagesIndexPageData')) ?? null;
            }
            return state.correctConfigPackagesIndexPageData;
        },
        getEssayPapersIndexPageData(state) {
            if (state.essayPapersIndexPageData === null) {
                state.essayPapersIndexPageData = JSON.parse(localStorage.getItem('essayPapersIndexPageData')) ?? null;
            }
            return state.essayPapersIndexPageData;
        },
        getComprehensiveIndexPageData(state) {
            if (state.comprehensiveIndexPageData === null) {
                state.comprehensiveIndexPageData = JSON.parse(localStorage.getItem('comprehensiveIndexPageData')) ?? null;
            }
            return state.comprehensiveIndexPageData;
        },
        getFtpMessagePageDataIndexPageData(state) {
            if (state.ftpMessageIndexPageData === null) {
                state.ftpMessageIndexPageData = JSON.parse(localStorage.getItem('ftpMessageIndexPageData')) ?? null;
            }
            return state.ftpMessageIndexPageData;
        },
        getDownloadSelectPrintUser(state) {
            if (state.downloadSelectPrintUser === null) {
                state.downloadSelectPrintUser = JSON.parse(localStorage.getItem('downloadSelectPrintUser')) ?? null;
            }
            return state.downloadSelectPrintUser;
        },
        getUploadSelectPrintUser(state) {
            if (state.uploadSelectPrintUser === null) {
                state.uploadSelectPrintUser = JSON.parse(localStorage.getItem('uploadSelectPrintUser')) ?? null;
            }
            return state.uploadSelectPrintUser;
        },
        getTempCollapse(state) {
            if (state.tempCollapse === null) {
                state.tempCollapse = JSON.parse(localStorage.getItem('tempCollapse')) ?? null;
            }
            return state.tempCollapse;
        }
    },
    actions: {
        setUser(user) {
            this.user = user;
            if (user) {
                localStorage.setItem('user', JSON.stringify(user));
            } else {
                localStorage.removeItem('user');
            }
        },
        showLogin(isShow) {
            this.isShowLogin = isShow;
        },
        setLocale(locale) {
            this.locale = locale
            localStorage.setItem("locale", locale)
        },
        setGuideStatus(page, status) {
            // page: 'markPaper' | 'essay' | 'packageConfig'
            // status: boolean
            this.guideStatus[page] = status;
            this.saveGuideStatus();
        },
        initGuideStatus() {
            let storedGuide = localStorage.getItem('guideStatus');
            if (storedGuide) {
                this.guideStatus = JSON.parse(storedGuide);
            } else {
                // 默认都需要引导（示例）
                this.guideStatus = {
                    markPaper: true,
                    essay: true,
                    packageConfig: true
                }
            }
        },
        saveGuideStatus() {
            localStorage.setItem('guideStatus', JSON.stringify(this.guideStatus));
        },
        setRoles(roles) {
            this.roles = roles;
            localStorage.setItem('roles', JSON.stringify(roles));
        },
        setSchool(school) {
            this.school = school;
            localStorage.setItem('school', JSON.stringify(school));
        },
        async initAuthData() {
            const authData = await getAuthData()
            this.setRoles(authData.data.roles)
            this.setSchool(authData.data.school)
        },
        setXYOffsetData(id, data) {
            if (!id) {
                return
            }
            if (typeof data !== 'object' || data === null) {
                return
            }
            const {xOffset, yOffset} = data
            if (typeof xOffset !== 'number' || typeof yOffset !== 'number') {
                return
            }
            this.allXYOffsetDataById = {
                ...this.allXYOffsetDataById,
                [id]: {xOffset, yOffset}
            }
            this.saveXYOffsetData();
        },
        saveXYOffsetData() {
            try {
                localStorage.setItem('allXYOffsetDataById', JSON.stringify(this.allXYOffsetDataById));
            } catch (e) {
                console.error("Failed to save allXYOffsetDataById to localStorage:", e);
            }
        },
        removeXYOffsetData(id) {
            if (this.allXYOffsetDataById[id]) {
                const {[id]: _, ...rest} = this.allXYOffsetDataById
                this.allXYOffsetDataById = rest
                this.saveXYOffsetData()
            }
        },
        setMarkZoomDataById(id, data) {
            if (!id) {
                return
            }
            this.allMarkZoomDataById = {
                ...this.allMarkZoomDataById,
                [id]: data || 100
            }
            this.saveMarkZoomData();
        },
        setFontSizeDataById(id, data) {
            if (!id) {
                return
            }
            this.allFontSizeDataById = {
                ...this.allFontSizeDataById,
                [id]: data || 100
            }
            this.saveFontSizeData();
        },
        setOpacityDataById(id, data) {
            if (!id) {
                return
            }
            this.allOpacityDataById = {
                ...this.allOpacityDataById,
                [id]: data || 1.0
            }
            this.saveOpacityData();
        },
        saveMarkZoomData() {
            try {
                localStorage.setItem('allMarkZoomDataById', JSON.stringify(this.allMarkZoomDataById));
            } catch (e) {
                console.error("Failed to save allMarkZoomDataById to localStorage:", e);
            }
        },
        saveFontSizeData() {
            try {
                localStorage.setItem('allFontSizeDataById', JSON.stringify(this.allFontSizeDataById));
            } catch (e) {
                console.error("Failed to save allFontSizeDataById to localStorage:", e);
            }
        },
        saveOpacityData() {
            localStorage.setItem('allOpacityDataById', JSON.stringify(this.allOpacityDataById));
        },
        setImageDataById(id, data) {
            const keys = Object.keys(this.imagesList);
            if (keys.length > 200) {
                const firstKey = keys[0];
                delete this.imagesList[firstKey];
            }
            this.imagesList[id] = data;
            localStorage.setItem('imagesList', JSON.stringify(this.imagesList));
        },
        removeMarkZoomData(id) {
            if (this.allMarkZoomDataById[id]) {
                const {[id]: _, ...rest} = this.allMarkZoomDataById
                this.allMarkZoomDataById = rest
                this.saveMarkZoomData()
            }
        },
        removeFontSizeData(id) {
            if (this.allFontSizeDataById[id]) {
                const {[id]: _, ...rest} = this.allFontSizeDataById
                this.allFontSizeDataById = rest
                this.saveFontSizeData()
            }
        },
        setDefaultConfigs(data) {
            try {
                this.defaultConfigs = data;
                localStorage.setItem('defaultConfigs', JSON.stringify(data));
            } catch (e) {
                console.error("Failed to save allXYOffsetDataById to localStorage:", e);
            }
        },
        setNowCropperByFileIds(id, data) {
            if (!id) {
                return
            }
            if (typeof data !== 'object' || data === null) {
                return
            }

            this.nowCropperByFileIds = {
                ...this.nowCropperByFileIds,
                [id]: JSON.parse(JSON.stringify(data))
            }
            try {
                localStorage.setItem('nowCropperByFileIds', JSON.stringify(this.nowCropperByFileIds));
            } catch (e) {
                console.error("Failed to save nowCropperByFileIds to localStorage:", e);
            }
        },
        changeIsCollapse() {
            this.isCollapse = !this.isCollapse;
            localStorage.setItem('isCollapse', JSON.stringify(this.isCollapse));
        },
        setTempCollapse(tempCollapse) {
            this.tempCollapse = tempCollapse;
            localStorage.setItem('tempCollapse', JSON.stringify(this.tempCollapse));
        },
        setMarkPaperLeftWidth(width) {
            this.markPaperLeftWidth = width;
            localStorage.setItem('markPaperLeftWidth', JSON.stringify(width));
        },
        setNeedMarkPaperFileTaskGuide(need) {
            this.guideStatus.markPaperFileTask = need;
            localStorage.setItem('needMarkPaperFileTaskGuide', JSON.stringify(need));
        },
        setIsFreeDraw(isFreeDraw) {
            localStorage.setItem('isFreeDraw', JSON.stringify(isFreeDraw));
            this.isFreeDraw = isFreeDraw;
        },
        setIsAutoSortArea(isAutoSortArea) {
            localStorage.setItem('isAutoSortArea', JSON.stringify(isAutoSortArea));
            this.isAutoSortArea = isAutoSortArea;
        },
        setMarkPaperIndexPageData(markPaperIndexPageData) {
            localStorage.setItem('markPaperIndexPageData', JSON.stringify(markPaperIndexPageData));
            this.markPaperIndexPageData = markPaperIndexPageData;
        },
        setCorrectConfigPackagesIndexPageData(correctConfigPackagesIndexPageData) {
            localStorage.setItem('correctConfigPackagesIndexPageData', JSON.stringify(correctConfigPackagesIndexPageData));
            this.correctConfigPackagesIndexPageData = correctConfigPackagesIndexPageData;
        },
        setEssayPapersIndexPageData(essayPapersIndexPageData) {
            localStorage.setItem('essayPapersIndexPageData', JSON.stringify(essayPapersIndexPageData));
            this.essayPapersIndexPageData = essayPapersIndexPageData;
        },
        setComprehensiveIndexPageData(comprehensiveIndexPageData) {
            localStorage.setItem('comprehensiveIndexPageData', JSON.stringify(comprehensiveIndexPageData));
            this.comprehensiveIndexPageData = comprehensiveIndexPageData;
        },
        setFtpMessageIndexPageData(ftpMessageIndexPageData) {
            localStorage.setItem('ftpMessageIndexPageData', JSON.stringify(ftpMessageIndexPageData));
            this.ftpMessageIndexPageData = ftpMessageIndexPageData;
        },
        setDownloadSelectPrintUser(downloadSelectPrintUser) {
            localStorage.setItem('downloadSelectPrintUser', JSON.stringify(downloadSelectPrintUser));
            this.downloadSelectPrintUser = downloadSelectPrintUser;
        },
        setUploadSelectPrintUser(uploadSelectPrintUser) {
            localStorage.setItem('uploadSelectPrintUser', JSON.stringify(uploadSelectPrintUser));
            this.uploadSelectPrintUser = uploadSelectPrintUser;
        },
        setDefaultCorrectModal(defaultCorrectModal) {
            localStorage.setItem("defaultCorrectModal", JSON.stringify(defaultCorrectModal || 'doubao-1-5-vision-pro-32k-250115'))
            this.defaultCorrectModal = defaultCorrectModal;
        },
        setAimodelOptions(aimodelOptions) {
            if (!aimodelOptions) return;
            localStorage.setItem("aimodelOptions", JSON.stringify(aimodelOptions));
            this.aimodelOptions = aimodelOptions;
        }
    }
})
