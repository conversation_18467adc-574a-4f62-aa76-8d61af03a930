const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');


const gitHash = execSync('git rev-parse --short HEAD').toString().trim();
const version = `${new Date().toISOString()}-${gitHash}`;

const content = JSON.stringify({ version }, null, 2);

fs.writeFileSync(path.resolve(__dirname, '../dist/version.json'), content);
console.log('[Build] Generated version.json:', content);
