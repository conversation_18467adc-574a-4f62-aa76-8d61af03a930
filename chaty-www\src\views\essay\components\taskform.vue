<template>
  <el-form :model="form" :label-width="100">
    <el-row :gutter="60">
      <!-- 第一列 -->
      <el-col :span="11">
        <el-form-item label="任务名称：" prop="name" v-if="isEdit">
          <el-input v-model="form.name"/>
        </el-form-item>

        <el-form-item label="作文文体：" prop="essayStyle" required>
          <el-select v-model="form.essayStyle" placeholder="请选择文体" :disabled="!form.isEnglishEssay">
            <el-option v-for="item in essayStyles" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>

        <el-form-item label="评分方案：" prop="scoringScheme" required>
          <el-radio-group v-model="form.scoringScheme" disabled>
            <el-radio v-for="item in scoringSchemes" :label="item.value">{{item.label}}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="字数要求：" prop="wordCount">
          <el-input-number v-model="form.wordCount" :min="0" :precision="0" :step="100" placeholder="请输入字数要求"/>
        </el-form-item>

        <el-form-item label="年级：" prop="grade">
          <el-select v-model="form.grade" placeholder="请选择年级">
            <el-option v-for="grade in gradeOptions" :key="grade.label" :label="grade.label" :value="grade.label"/>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="10">
        <el-form-item label="要求：" prop="score" v-if="form.materials" required>
          <el-input v-model="form.materials" placeholder="请输入作文要求"/>
        </el-form-item>

        <el-form-item label="满分：" prop="score" v-if="form.scoringScheme === 'score'" required >
          <el-input-number v-model="form.score" :min="0" :precision="0" :step="10" placeholder="请输入作文满分是多少" :disabled="!form.isEnglishEssay"/>
        </el-form-item>

        <el-form-item label="作文题目：" prop="essayTitle">
          <el-input v-model="form.essayTitle" placeholder="请输入作文题目"/>
        </el-form-item>

<!--        <el-form-item label="作文类型：" prop="essayType">-->
<!--          <el-radio-group v-model="form.essayType">-->
<!--            <el-radio label="unit">单元作文</el-radio>-->
<!--            <el-radio label="custom">自定义作文</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->

        <el-form-item label="作文类型：" prop="isEnglishEssay">
          <el-radio-group v-model="form.isEnglishEssay">
            <el-radio :label="true">英语作文</el-radio>
            <el-radio :label="false">语文作文</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-col>

    </el-row>
  </el-form>
</template>


<script>
export default {
  props: {
    initialData: Object,
    loadConfigs: Function,
    submitLoading: Boolean,
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      scoringSchemes: [
        {label: "A/B/C制", value: "A/B/C"},
        {label: "分数制", value: "score"},
      ],
      essayStyles: [
        {label: "英文作文", value: "englishEssay"},
        {label: "语文作文", value: "chineseEssay"},
        {label: "议论文", value: "argumentative"},
        {label: "说明文", value: "expository"},
        {label: "记叙文", value: "narrative"},
        {label: "描写文", value: "descriptive"},
        {label: "抒情文", value: "lyrical"},
        {label: "应用文", value: "practical"},
      ],
      form: {
        name: "",
        configId: "",
        grade: "初一",
        essayType: "unit",
        questionType: "topic",
        essayTitle: "",
        scoringScheme: "score",
        essayStyle: "englishEssay",
        wordCount: 300,
        materials: "要求使用正确的句型，词汇准确，语法无误，段落结构清晰，内容与题目相关，并能够充分表达个人观点",
        score: 100,
        isEnglishEssay: true
      },
      configOptions: [],
      configLoading: false,
      gradeOptions: [
          // value没用
        {label: "一年级", value: "1"},
        {label: "二年级", value: "2"},
        {label: "三年级", value: "3"},
        {label: "四年级", value: "4"},
        {label: "五年级", value: "5"},
        {label: "六年级", value: "6"},
        {label: "初一", value: "7"},
        {label: "初二", value: "8"},
        {label: "初三", value: "9"},
        {label: "高一", value: "10"},
        {label: "高二", value: "11"},
        {label: "高三", value: "12"},
        {label: "英语四级", value: "13"},
        {label: "英语六级", value: "14"},
      ],
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (newVal) {
          this.form = {...newVal};  // 更新表单数据
          this.loadConfigs(newVal.configName || ''); // 加载配置
        }
      },
      immediate: true
    },
    'form.isEnglishEssay'(newVal) {
      if (!newVal) {
        // 作文文体不能为英语作文
        if (this.form.essayStyle === 'englishEssay') {
          this.form.essayStyle = 'chineseEssay'
        }
        // 最大分数为10分
        this.form.score = 10;

      }
      this.$emit('changeIsEnglishEssay', newVal);
    }
  },
  methods: {
    loadConfigs(query) {
      if (!query) return;

      this.configLoading = true;
      this.$axios
          .post("/api/docCorrectConfig/page", {
            name: query,
            page: {pageNumber: 1, pageSize: 10, searchCount: false}
          })
          .then(res => {
            this.configOptions = res.data.records;
          })
          .finally(() => {
            this.configLoading = false;
          });
    },
    onSubmit() {
      this.$emit('submit', this.form); // 触发父组件的提交事件
    },
  },
};
</script>
