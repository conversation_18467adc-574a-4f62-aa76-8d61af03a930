const { execSync } = require('child_process');

console.log('🔄 开始更新部署...');

try {
  // 1. 构建项目
  console.log('🔨 构建项目...');
  execSync('npm run build', { stdio: 'inherit' });

  // 2. 部署到CloudBase
  console.log('📤 部署到CloudBase...');
  const envId = process.env.CLOUDBASE_ENV_ID || 'miaomiaowangwang-4faj496d7131c2c';
  const deployCommand = `cloudbase hosting:deploy dist -e ${envId} --region ap-shanghai`;
  execSync(deployCommand, { stdio: 'inherit' });

  console.log('✅ 更新完成！');
  console.log(`🌐 访问地址: https://${envId}-1369222364.tcloudbaseapp.com`);

} catch (error) {
  console.error('❌ 更新失败:', error.message);
  process.exit(1);
} 