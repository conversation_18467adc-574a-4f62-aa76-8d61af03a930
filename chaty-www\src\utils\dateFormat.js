export const getFeiShuTimeFormat = (datetime) => {
    if (!datetime) {
        return '-';
    }
    const dt = new Date(datetime.replace(' ', 'T'));
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterdayStart = new Date(todayStart);
    yesterdayStart.setDate(yesterdayStart.getDate() - 1);
    const yearStart = new Date(now.getFullYear(), 0, 1);

    const pad = n => String(n).padStart(2, '0');
    const hhmm = `${pad(dt.getHours())}:${pad(dt.getMinutes())}`;

    if (dt >= todayStart) {
        return `今天 ${hhmm}`;
    }
    if (dt >= yesterdayStart) {
        return `昨天 ${hhmm}`;
    }
    if (dt >= yearStart) {
        return `${dt.getMonth() + 1}月${dt.getDate()}日 ${hhmm}`;
    }
    return `${dt.getFullYear()}年${dt.getMonth() + 1}月${dt.getDate()}日 ${hhmm}`;
}