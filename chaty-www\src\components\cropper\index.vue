<template>
  <div class="cropper-wrapper" ref="cropperWrapper">
    <div v-if="imgUrl && !startClick" class="cropper-container">
      <img class="cropper-img" :src="imgUrl" id="img" crossorigin="anonymous">
    </div>
    <div v-else-if="imgUrl && startClick" class="cropper-container">
      <canvas ref="canvas"
              @contextmenu="recordClick"
              @mousedown="onMouseDown"
              @mousemove="onMouseMove"
              @mouseup="onMouseUp"
              @wheel="onWheel"></canvas>
    </div>
    <template v-else>
      <slot name="empty">
        <el-empty class="empty-tip" description="加载中..." />
      </slot>
    </template>
  </div>
</template>

<script>
import Cropper from 'cropperjs'
import { ElLoading } from 'element-plus';

export function img2base64(imgUrl) {
  return fetch(imgUrl)
      .then(res => res.blob())
      .then(blob => URL.createObjectURL(blob));
}

export default {
  props: {
    loading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    loading: {
      immediate: true,
      handler(e) {
        if (e && this.$refs.cropperWrapper) {
          this.loadingInstance = ElLoading.service({
            lock: true,
            text: '加载中...',
            fullscreen: false,
            target: this.$refs.cropperWrapper,
            background: 'rgba(0, 0, 0, 0.7)'
          });
          // 最长10s关闭
          setTimeout(() => {
            if (this.loadingInstance)
              this.loadingInstance.close();
          }, 10000)
        } else {
          if (this.loadingInstance)
            this.loadingInstance.close();
        }
      }
    }
  },
  data() {
    return {
      imgUrl: null,
      cropper: null,
      clickedPoints: [], // 存储点击的坐标点
      startClick: false, // 控制是否切换到 canvas 模式
      naturalHeight: null,
      naturalWidth: null,
      imageOffset: { x: 0, y: 0 }, // 图片在 canvas 中的偏移
      imageScale: 1, // 图片缩放比例
      selected: [],
      loadingInstance: null,
      // 拖拽与缩放相关属性
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      dragOffset: { x: 0, y: 0 },
      currentAreas: [],
      // qsIds 数组，每个元素形如 { area: { x, y }, text: '题号' }
      qsIds: []
    }
  },
  methods: {
    setImg(url) {
      this.imgUrl = url;
      if (this.imgUrl) {
        this.refreshCropper();
      } else if (this.cropper) {
        this.cropper.destroy();
      }
    },
    refreshCropper(next) {
      if (this.cropper) {
        this.cropper.destroy();
      }
      this.$nextTick(() => {
        this.cropper = new Cropper(document.getElementById('img'), {
          autoCrop: false,
          dragMode: 'move',
          movable: true,
          zoomable: true,
          background: false,
          toggleDragModeOnDblclick: false,
          ready: () => {
            this.cropper.clear();
            this.cropper.setDragMode('move');
            if (next) next();
          },
        });
      });
    },
    reset() {
      if (this.cropper) {
        this.cropper.destroy();
      }
      this.imgUrl = null;
      this.cropper = null;
    },
    crop(area) {
      this.unCrop();
      this.cropper.setDragMode('crop');
      if (area) {
        this.cropper.crop();
        this.cropper.setData(area);
      }
    },
    unCrop() {
      this.cropper.clear();
      this.cropper.setDragMode('move');
    },
    doCrop() {
      let cropped = this.cropper.getData(true);
      this.unCrop();
      return cropped;
    },
    clearCropped() {
      this.cropper.clear();
    },
    // 记录右键点击坐标：不再单独调用 drawFlag，而是保存点击点后整体重绘
    recordClick(event) {
      event.preventDefault();
      if (!this.startClick) return;
      const canvas = this.$refs.canvas;
      const rect = canvas.getBoundingClientRect();

      // 获取点击位置相对于 canvas 的坐标
      let clickX = event.clientX - rect.left;
      let clickY = event.clientY - rect.top;

      // 转换为图片的实际像素坐标（这里的偏移量可根据需求微调）
      const x = (clickX - this.imageOffset.x) / this.imageScale - 20;
      const y = (clickY - this.imageOffset.y) / this.imageScale - 40;

      // 检查点击是否在图片范围内
      if (x >= 0 && x <= this.image.naturalWidth && y >= 0 && y <= this.image.naturalHeight) {
        this.clickedPoints.push({ x: Math.round(x), y: Math.round(y) });
        let area = {
          x: Math.round(x),
          y: Math.round(y),
          width: 20,
          height: 20,
          rotate: 0,
          scaleX: 1,
          scaleY: 1
        }
        this.$emit('pointsUpdated', area, this.clickedPoints.length, this.selected);
      } else {
        console.log("点击位置超出图片范围");
      }
      // 每次右键点击后重新渲染整个 canvas，
      // 这样会自动调用 drawClickedPoints 绘制所有保存的点击点
      this.drawImageToCanvas(this.currentAreas);
      console.log(this.clickedPoints);
    },
    // 如果需要单独绘制 flag，可保留此方法，但本例中不再单独调用
    drawFlag(canvas, clickX, clickY) {
      const ctx = canvas.getContext('2d');
      ctx.drawImage(document.getElementById("flagimg"), clickX, clickY, 20, 20)
    },
    stopClickedPoints() {
      this.startClick = false;
    },
    // 清空点击点，并开始新记录，同时存储当前区域数据用于重绘
    clearPoints(areas, selected) {
      this.clickedPoints = [];
      this.startClick = true;
      this.selected = selected;
      this.setCanvasImg(this.imgUrl, areas);
    },
    // 加载图片到 canvas，并保存区域数据用于后续拖拽/缩放重绘
    async setCanvasImg(url, areas) {
      this.imgUrl = url;
      this.currentAreas = areas;
      this.image = new Image();
      this.image.crossOrigin = 'anonymous';
      this.image.src = await img2base64(url);
      this.image.onload = () => {
        const canvas = this.$refs.canvas;
        canvas.width = canvas.parentElement.clientWidth;
        canvas.height = canvas.parentElement.clientHeight;
        const scaleX = canvas.width / this.image.naturalWidth;
        const scaleY = canvas.height / this.image.naturalHeight;
        this.imageScale = Math.min(scaleX, scaleY);
        this.imageOffset.x = (canvas.width - this.image.naturalWidth * this.imageScale) / 2;
        this.imageOffset.y = (canvas.height - this.image.naturalHeight * this.imageScale) / 2;
        this.drawImageToCanvas(areas);
      };
    },
    // 重绘 canvas，依次绘制图片、标注区域、题号以及点击点
    drawImageToCanvas(areas) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext('2d');
      const container = canvas.parentElement;
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;

      if (!this.image || !this.image.naturalWidth) return;

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(
          this.image,
          0, 0, this.image.naturalWidth, this.image.naturalHeight,
          this.imageOffset.x, this.imageOffset.y,
          this.image.naturalWidth * this.imageScale, this.image.naturalHeight * this.imageScale
      );
      this.drawRect(areas);
      this.drawQsIds();
      this.drawClickedPoints();
    },
    // 绘制标注区域
    drawRect(areas) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext('2d');
      areas.forEach((rect, idx) => {
        if (!this.selected.includes(idx)) return;
        let { x, y, width, height } = rect.area;
        ctx.strokeStyle = rect.color || 'black';
        ctx.lineWidth = 1;
        const scaledX = x * this.imageScale + this.imageOffset.x;
        const scaledY = y * this.imageScale + this.imageOffset.y;
        const scaledWidth = width * this.imageScale;
        const scaledHeight = height * this.imageScale;
        ctx.strokeRect(scaledX, scaledY, scaledWidth, scaledHeight);
      });
    },
    // 绘制 qsIds 中的题号，并让字体大小随缩放比例变化
    drawQsIds() {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext('2d');
      if (!this.qsIds || !this.qsIds.length) return;
      const baseFontSize = 60;
      const scaledFontSize = baseFontSize * this.imageScale;
      ctx.font = `${scaledFontSize}px Arial`;
      ctx.fillStyle = 'blue';
      this.qsIds.forEach(qs => {
        const scaledX = qs.area.x * this.imageScale + this.imageOffset.x;
        const scaledY = qs.area.y * this.imageScale + this.imageOffset.y;
        ctx.fillText(qs.text, scaledX, scaledY);
      });
    },
    // 新增方法：绘制 clickedPoints 数组中的所有点击点
    drawClickedPoints() {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext('2d');
      if (!this.clickedPoints || !this.clickedPoints.length) return;
      // 使用与 flag 相同的尺寸，这里依然调用 drawImage 以便用已有的 flag 图片
      this.clickedPoints.forEach(pt => {
        const flagX = pt.x * this.imageScale + this.imageOffset.x;
        const flagY = pt.y * this.imageScale + this.imageOffset.y;
        ctx.drawImage(document.getElementById("flagimg"), flagX, flagY, 20, 20);
      });
    },
    // ===== 拖拽功能 =====
    onMouseDown(event) {
      if (!this.startClick) return;
      this.isDragging = true;
      this.dragStart = { x: event.clientX, y: event.clientY };
      this.dragOffset = { ...this.imageOffset };
    },
    onMouseMove(event) {
      if (!this.isDragging) return;
      const dx = event.clientX - this.dragStart.x;
      const dy = event.clientY - this.dragStart.y;
      this.imageOffset.x = this.dragOffset.x + dx;
      this.imageOffset.y = this.dragOffset.y + dy;
      this.drawImageToCanvas(this.currentAreas);
    },
    onMouseUp() {
      this.isDragging = false;
    },
    // ===== 鼠标滚轮缩放 =====
    onWheel(event) {
      if (!this.startClick) return;
      event.preventDefault();
      const canvas = this.$refs.canvas;
      const rect = canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;
      const oldScale = this.imageScale;
      const scaleFactor = event.deltaY < 0 ? 1.1 : 0.9;
      let newScale = oldScale * scaleFactor;
      newScale = Math.max(0.1, Math.min(newScale, 5));
      this.imageOffset.x = mouseX - ((mouseX - this.imageOffset.x) * (newScale / oldScale));
      this.imageOffset.y = mouseY - ((mouseY - this.imageOffset.y) * (newScale / oldScale));
      this.imageScale = newScale;
      this.drawImageToCanvas(this.currentAreas);
    }
  },
}
</script>

<style lang="scss" scoped>
.cropper-wrapper {
  padding: 20px;
  background-color: var(--el-bg-color-page);

  .cropper-container {
    width: 100%;
    height: 100%;

    .cropper-img {
      display: block;
      object-fit: contain;
      width: 100%;
      max-height: 100%;
    }
  }

  .empty-tip {
    height: 100%;
  }
}
</style>
