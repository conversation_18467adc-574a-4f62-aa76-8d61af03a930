import config from "./index";

const alistUrl = import.meta.env.VITE_ALIST_URL
const alistInPath = import.meta.env.VITE_ALIST_PATH
const alistRootPath = import.meta.env.VITE_ALIST_ROOT_PATH
const alistOutPath = import.meta.env.VITE_OUT_ALIST_PATH

function fileurl(url) {
    return `${config.apiUrl || ''}${url}` 
}

function remoteFile(file, path = '') {
    return `${alistUrl}/d${path}/${file.name}?sign=${file.sign}`
}

function remoteOutFile(file, path = '') {
    return `${alistUrl}/p${alistRootPath}${path}/${file.name}?sign=${file.sign}`
}

export default {
    uploadUrl: `${config.apiUrl || ''}/api/file/upload`,
    pdf2imgUrl: `${config.apiUrl || ''}/api/file/upload/doc2img`,
    multipdfUrl: `${config.apiUrl || ''}/api/file/upload/multiPdf`,
    multiImageToPdfUrl: `${config.apiUrl || ''}/api/file/upload/multiImageToPdf`,
    imageToPdfUrl: `${config.apiUrl || ''}/api/file/upload/img2pdf`,
    saveRemoteFile: `/api/file/upload/remote`,
    remoteFile,
    remoteOutFile,
    fileurl,
}