<template>
  <el-dialog v-model="isShow" :title="title" width="500" :before-close="onClose" append-to-body>
    <el-form :model="form" label-width="100">
      <div>将此页调整至那一页（被插入的页将顺延往后）</div>
      <div v-if="currentPage && totalPages">当前页：{{currentPage.docname.split('_').pop()}} / {{totalPages}}</div>
      <br>
      <el-form-item label="选择面：" prop="selectedSide">
        <el-select v-model="form.selectedSide" placeholder="请选择面">
          <el-option v-for="side in availableSides" :key="side" :label="side" :value="side"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="页码：" prop="pageNumber">
        <el-input v-model="form.pageNumber" type="number" placeholder="请输入页码" min="1"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="doSubmit">确认</el-button>
    </template>
  </el-dialog>

</template>

<script>
export default {
  data() {
    return {
      isShow: false,
      title: "调整页面位置",
      submitLoading: false,
      form: {
        selectedSide: "",
        pageNumber: null
      },
      availableSides: [], // 将在 show 方法中给出
      currentPage: null, // 当前页
      totalPages: null,
      fileId: null
    };
  },
  methods: {
    show(currentPage, totalPages, availableSides, fileId) {
      this.currentPage = currentPage;
      this.totalPages = totalPages;
      this.availableSides = availableSides; // 假设传入面1、面2等
      this.form = {
        selectedSide: this.availableSides[0] || "", // 默认选择第一个面
        pageNumber: currentPage + 1 // 默认页码为当前页的下一个
      };
      this.fileId = fileId;
      this.isShow = true;
    },
    onClose() {
      this.isShow = false;
      this.form = { selectedSide: "", pageNumber: null };
    },
    doSubmit() {
      this.submitLoading = true;
      const { selectedSide, pageNumber } = this.form;
      if (pageNumber < 1 || pageNumber > this.totalPages + 1) {
        this.submitLoading = false;
        this.$message.error("请输入有效的页码");
        return;
      }
      console.log('currentPage', this.currentPage)
      // 提交调整页码的请求
      const param = {
        selectedTaskIdx: selectedSide,
        selectPageNumber: pageNumber,
        currentPageId: this.currentPage.id,
        currentTaskId: this.currentPage.taskId,
        fileId: this.fileId
      };
      console.log(param)
      this.$axios.post('/api/docCorrectFile/adjust', param).then(() => {
        this.submitLoading = false;
        this.$message.success("页面调整成功");
        this.onClose();
        this.$emit('submit');
      }).catch(() => {
        this.submitLoading = false;
        this.$message.error("页面调整失败");
      });
    }
  }
};
</script>

<style scoped>
</style>
