<template>
  <el-dialog v-model="isShow" title="样式三-试卷统计设置" width="1000" :before-close="beforeClose">
    <el-form :model="form" label-width="120px">
      <!-- 试卷搜索选择 -->
      <el-form-item label="选择试卷">
        <el-select
            v-model="selectedPaperId"
            placeholder="请输入试卷名称 搜索添加"
            clearable
            filterable
            remote
            :remote-method="loadPaperOptions"
            :loading="loadingPaperOption"
            style="width: 100%; margin-bottom: 10px"
        >
          <el-option
              v-for="item in paperOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          />
        </el-select>
        <el-select
            v-model="selectedPackageId"
            placeholder="或者输入试卷标准卷名称 搜索添加"
            clearable
            filterable
            remote
            :remote-method="loadPackageOptions"
            :loading="loadingPackageOption"
            style="width: 100%"
        >
          <el-option
              v-for="item in packageOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          />
        </el-select>
      </el-form-item>

      <!-- 已选试卷列表 -->
      <el-form-item label="已选试卷">
        <el-table :data="form.fileDetails" border style="width: 100%">
          <el-table-column prop="className" label="班级名称" width="250">
            <template #default="{ row }">
              <el-input v-model="row.className" placeholder="请输入班级名称"/>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="试卷名称" width="250"/>
<!--          <el-table-column prop="fileId" label="id"/>-->
          <el-table-column label="操作">
            <template #default="{ $index }">
              <el-button type="danger" @click="removePaper($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="年级名称" prop="gradeName">
        <el-input v-model="form.gradeName" placeholder="请输入年级名称" />
      </el-form-item>

      <el-form-item label="学科名称" prop="subjectName">
        <el-input v-model="form.subjectName" placeholder="请输入学科名称" />
      </el-form-item>

      <!-- 合格分和优秀分 -->
      <el-form-item label="合格分">
        <el-input-number v-model="form.hegeScore" :min="0" :max="100"/>
        <el-text style="margin-left: 10px">总分60%</el-text>
      </el-form-item>
      <el-form-item label="优秀分">
        <el-input-number v-model="form.youxiuScore" :min="0" :max="100"/>
        <el-text style="margin-left: 10px">总分85%</el-text>
      </el-form-item>
      <el-form-item label="按照学号排序">
        <el-switch v-model="form.orderByStudentNumber" active-text="排序" inactive-text="不排序"></el-switch>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <div style="text-align: right">
          <el-button type="primary" @click="onSubmit">提交</el-button>
          <el-button type="danger" @click="beforeClose">取消</el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      isShow: false,
      selectedPaperId: null, // 当前选中的试卷ID
      selectedPackageId: null, // 当前选中的试卷包ID
      paperOptions: [], // 试卷列表
      packageOptions: [], // 试卷包列表
      loadingPaperOption: false, // 试卷搜索加载状态
      loadingPackageOption: false, // 试卷包搜索加载状态
      form: {
        fileDetails: [], // 已选试卷列表
        youxiuScore: null, // 优秀分
        hegeScore: null, // 合格分
        gradeName: "六年级",  // 添加年级名称默认值
        subjectName: "英语",  // 添加学科名称默认值
        ranges: [],
        orderByStudentNumber: true
      },
      sortTimeout: null,

    };
  },
  methods: {
    // 加载试卷选项
    loadPaperOptions(name) {
      if (!name || name === "") {
        this.paperOptions = [];
        return;
      }
      this.loadingPaperOption = true;
      this.loadPapers(name)
          .then((res) => {
            this.paperOptions = res;
          })
          .finally(() => {
            this.loadingPaperOption = false;
          });
    },
    // 加载试卷包选项
    loadPackageOptions(name) {
      if (!name || name === "") {
        this.packageOptions = [];
        return;
      }
      this.loadingPackageOption = true;
      this.loadPackages(name)
          .then((res) => {
            this.packageOptions = res;
          })
          .finally(() => {
            this.loadingPackageOption = false;
          });
    },
    // 调用接口加载试卷
    loadPapers(name) {
      return this.$axios
          .post("/api/docCorrectFile/page", {
            name,
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            return res.data.records;
          });
    },
    // 调用接口加载试卷包
    loadPackages(name) {
      return this.$axios
          .post("/api/docCorrectConfigPackage/page", {
            name,
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            return res.data.records;
          });
    },
    // 处理试卷选择
    onPaperSelect() {
      const selectedPaper = this.paperOptions.find(
          (paper) => paper.id === this.selectedPaperId
      );
      if (selectedPaper) {
        const exists = this.form.fileDetails.some(
            item => item.fileId === selectedPaper.id
        );
        if (!exists) {
          this.form.fileDetails.push({
            fileId: selectedPaper.id,
            name: selectedPaper.name,
            className: selectedPaper.name
          });
        }
        this.selectedPaperId = null;
      }
    },
    // 处理试卷包选择
    onPackageSelect() {
      if (!this.selectedPackageId) return;
      this.$axios
          .get(`/api/docCorrectFile/getDocCorrectFileListByConfigPackageId?id=${this.selectedPackageId}`)
          .then((res) => {
            const fileList = res.data;
            if (fileList && fileList.length) {
              fileList.forEach((file) => {
                const exists = this.form.fileDetails.some(
                    item => item.fileId === file.id
                );
                if (!exists) {
                  this.form.fileDetails.push({
                    fileId: file.id,
                    name: file.name,
                    className: file.name
                  });
                }
              });
            }
            this.selectedPackageId = null;
          });
    },
    removePaper(index) {
      this.form.fileDetails.splice(index, 1);
    },
    onSubmit() {
      if (!this.form.fileDetails.length) {
        this.$message.error("请至少选择一个试卷！");
        return;
      }
      if (!this.form.hegeScore || !this.form.youxiuScore) {
        this.$message.error("请输入合格分和优秀分！");
        return;
      }
      // 检查是否有重命名
      let names = {}
      for (let i = 0; i < this.form.fileDetails.length; i++) {
        let item = this.form.fileDetails[i];
        if (item.className in names || names[item.className]) {
          this.$message.error("班级名称重复，请重新输入！");
          return;
        }
        names[item.className] = true;
      }
      this.$axios
          .post("/api/docCorrectFile/classStatisticData", this.form)
          .then((res) => {
            let data = {
              sheetData: res.data.sheetData,
              sheetNames: this.form.fileDetails.map(item => item.className),
              url: res.data.url,
              orderByStudentNumber: this.form.orderByStudentNumber,
              gradeName: this.form.gradeName,
              subjectName: this.form.subjectName
            }
            this.$emit('submit', data)
          })
          .finally(() => {
            this.beforeClose();
          });
    },
    // 关闭弹窗
    beforeClose() {
      this.isShow = false;
      this.form = {
        fileDetails: [],
        youxiuScore: null,
        hegeScore: null,
        gradeName: "六年级",  // 重置年级名称默认值
        subjectName: "英语",  // 重置学科名称默认值
        orderByStudentNumber: true
      };
      this.selectedPaperId = null;
      this.selectedPackageId = null;
      this.paperOptions = [];
      this.packageOptions = [];
    },
    addByPackage(name) {
      this.$axios
          .post("/api/docCorrectConfigPackage/page", {
            name,
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            if (res.data.records && res.data.records.length === 1) {
              this.selectedPackageId = res.data.records[0].id;
              this.onPackageSelect();
            }
          });
    },
    // 显示弹窗
    show(data, fileId, ranges, configName) {
      let totalScore = data.scoreTypesMaxScore.pop();
      this.form.hegeScore = (totalScore * 0.6).toFixed(1);
      this.form.youxiuScore = (totalScore * 0.85).toFixed(1);
      this.isShow = true;
      this.form.ranges = ranges;

      this.addByPackage(configName);
    },
    addByFiledId(id) {
      this.$axios
          .get("/api/docCorrectFile/getById?id=" + id)
          .then((res) => {
            this.form.fileDetails.push({
              fileId: res.data.id,
              name: res.data.name,
              className: res.data.name
            });
          });
    },
  },
  watch: {
    'form.fileDetails': {
      handler(newVal) {
        if (this.sortTimeout) {
          clearTimeout(this.sortTimeout);
        }
        // 判断当前数组是否已经有序，如果已经有序则不执行排序操作
        this.sortTimeout = setTimeout(() => {
          let isSorted = true;
          for (let i = 1; i < newVal.length; i++) {
            if (newVal[i - 1].className > newVal[i].className) {
              isSorted = false;
              break;
            }
          }
          if (!isSorted) {
            this.form.fileDetails.sort((a, b) => a.className.localeCompare(b.className));
            this.$message.success("排序成功")
          }
        }, 500);

      },
      deep: true
    },
    // 监听试卷选择
    selectedPaperId(newVal) {
      if (newVal) {
        this.onPaperSelect();
      }
    },
    // 监听试卷包选择
    selectedPackageId(newVal) {
      if (newVal) {
        this.onPackageSelect();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dialog .el-card {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

.text-center {
  text-align: center;
}
</style>