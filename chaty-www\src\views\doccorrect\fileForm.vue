<template>
    <el-dialog v-model="isShow" :title="title" width="600" :before-close="onClose">
        <el-form :model="form" ref="formRef">
            <el-form-item label="试卷：">
                <el-upload style="width: 200px" ref="pdfUploader" accept=".pdf" 
                    :data="getParseConfig"
                    :action="$fileserver.multipdfUrl" 
                    :with-credentials="true" 
                    :show-file-list="true"
                    :auto-upload="false"
                    :file-list="docList"
                    :on-change="onDocChange"
                    :on-success="uploadDoc">
                    <el-button type="primary">上传试卷</el-button>
                </el-upload>
            </el-form-item>
            <el-form-item label="名称：" prop="name">
                <el-input style="width: 200px" v-model="form.name" />
            </el-form-item>
            <el-form-item label="编号：" prop="code">
                <el-input style="width: 200px" v-model="form.code" 
                    @change="onCodeChange"
                    :suffix-icon="codeLoading ? 'Loading' : ''" />
            </el-form-item>
            <el-form-item label="页数：" prop="pageNum">
                <el-input-number style="width: 200px" :min="1" :controls="false" v-model="form.pageNum"
                    :value-on-clear="1" />
            </el-form-item>
            <el-form-item v-for="(page, index) in form.pages" :key="index" :label="`第${index + 1}页：`">
                <el-space :size="20">
                    <el-switch inactive-text="解析" v-model="page.isParse" />
                    <el-switch inactive-text="翻转" v-model="page.isRotation" />
                    <el-select v-model="page.configId" 
                        filterable remote
                        style="width: 200px"
                        :loading="page.configLoading"
                        :remote-method="(query) => loadConfigs(query, page)"
                        placeholder="请选择试卷配置" >
                        <el-option
                            v-for="item in page.configOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                    <el-button v-if="form.pageNum !== 1" type="danger" text icon="Delete" @click="form.pages.splice(index, 1)" />
                </el-space>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-space>
                <el-button type="warning" @click="onClose">取消</el-button>
                <el-dropdown split-button type="primary" @click="onSubmit('correct')" @command="onSubmit">
                    提交并批改
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="submit">提交</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </el-space>
        </template>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            isShow: false,
            title: '上传试卷',
            form: {
                name: '',
                pageNum: 1,
                pages: [
                    { isParse: true, isRotation: false, configId: '', configOptions: [], configLoading: false }
                ]
            },
            docList: [],
            action: 'correct',
            codeLoading: false
        }
    },
    watch: {
        "form.pageNum": {
            handler(val) {
                if (val > this.form.pages.length) {
                    for (let i = this.form.pages.length; i < val; i++) {
                        this.form.pages.push({ isParse: true, isRotation: false, configId: '', configOptions: [], configLoading: false });
                    }
                } else {
                    this.form.pages = this.form.pages.slice(0, val);
                }
            }
        },
        "form.pages.length": {
            handler(val) {
                this.form.pageNum = val;
            }
        }
    },
    methods: {
        show() {
            this.isShow = true
        },
        onClose() {
            this.$refs.formRef.resetFields()
            this.docList = []
            this.isShow = false
            this.$emit('onClose')
        },
        onSubmit(action) {
          console.log(action)
            this.action = action || 'correct'
            if (this.docList.length === 0) {
                this.$message.error("请上传试卷");
                return;
            }
            this.$refs.pdfUploader.submit()
        },
        uploadDoc(response) {
            let files = response.data
            if (!files || files.length === 0) {
                this.$message.error("文件上传失败!");
                return;
            }

            const pages = this.form.pages.filter(item => item.isParse)
                    .map((item, index) => {
                        const configObj = item.configOptions?.find(config => config.id === item.configId)
                        return {
                            ...item,
                            configId: item.configId,
                            configName: configObj?.name,
                            docs: files[index]
                        }
                    })
            const form = {
                ...this.form,
                pages,
                action: this.action
            };
            this.$axios.post("/api/docCorrectFile/create", form).then(res => {
                this.$message.success("文件上传成功");
                this.$emit("submit");
                this.onClose();
            });
        },
        onDocChange(uploadFile) {
            let status = uploadFile.status
            if (status === 'ready') {
                this.docList = [uploadFile]
                this.form.name = uploadFile.name?.split('.')[0]
            }
        },
        getParseConfig() {
            return {
                config: JSON.stringify(this.form)
            };
        },
        loadConfigs(query, page) {
            if (query === '') {
                page.options = []
                return
            }

            page.configLoading = true
            this.$axios.post("/api/docCorrectConfig/page", {
                name: query,
                page: {
                    pageSize: -1,
                    pageNumber: 1
                }
            }).then(res => {
                page.configOptions = res.data.records
            }).finally(() => {
                page.configLoading = false
            })
        },
        onCodeChange(val) {
            if (!val || val === '') {
                return
            }
            this.codeLoading = true
            this.$axios.get(`/api/docFileConfig/getByCode?code=${val}`).then(res => {
                if (res.data) {
                    const pageConfig = JSON.parse(res.data.config)
                    this.form.pages = pageConfig.map(conf => {
                        return {
                            ...conf,
                            configOptions: [
                                { id: conf.configId, name: conf.configName }
                            ], 
                            configLoading: false
                        }
                    }) 
                }
            }).finally(() => {
                this.codeLoading = false
            })
        }
    }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small) {
    margin-bottom: 5px;
}

:deep(.el-form-item) {
    margin-bottom: 15px;
}
</style>