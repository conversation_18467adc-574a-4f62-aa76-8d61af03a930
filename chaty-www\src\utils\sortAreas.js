/**
 * 自动按列分组并排序：
 * - 根据水平投影重叠度（重叠宽度 / 区域宽度 > 0.8）将区域归为同列；
 * - 列内根据该列平均高度判断行分隔：若相邻框垂直间距超出该列平均高度，则新行；
 * - 每行内按 x 从左到右排序；
 * - 在控制台打印检测到的列数和每列行数及总行数
 * @param {Array<{ area?: { x?: number, y?: number, width?: number, height?: number } }>} areas
 * @returns {Array} 新排序后的 areas（保留无效条目于末尾）
 */
export const autoSortAreas = (areas) => {
        // 1. 过滤有效项
        const valid = areas.filter(a =>
            a.area?.x != null && !isNaN(a.area.x) &&
            a.area?.y != null && !isNaN(a.area.y) &&
            a.area?.width != null && !isNaN(a.area.width) &&
            a.area?.height != null && !isNaN(a.area.height) &&
            !(a.area.x === 0 && a.area.y === 0)
        );
        const invalid = areas.filter(a => !valid.includes(a));
        if (!valid.length) return [...invalid];

        // 2. 构建中间项
        const items = valid.map(a => ({
            orig: a,
            x1: a.area.x,
            x2: a.area.x + a.area.width,
            y1: a.area.y,
            y2: a.area.y + a.area.height,
            width: a.area.width,
            height: a.area.height,
            centerY: a.area.y + a.area.height / 2
        }));

        // 3. 按 x1 升序
        items.sort((a, b) => a.x1 - b.x1);

        // 4. 分列：基于重叠度 > 0.8
        const columns = [];
        items.forEach(item => {
            let assigned = false;
            for (const col of columns) {
                // 计算与该列的重叠度，使用列均值范围
                const colX1 = Math.min(...col.map(i => i.x1));
                const colX2 = Math.max(...col.map(i => i.x2));
                const overlap = Math.min(colX2, item.x2) - Math.max(colX1, item.x1);
                const overlapRatio = overlap > 0 ? overlap / item.width : 0;
                if (overlapRatio > 0.8) {
                    col.push(item);
                    assigned = true;
                    break;
                }
            }
            if (!assigned) {
                columns.push([item]);
            }
        });
        // 按列左侧位置排序
        columns.sort((a, b) => Math.min(...a.map(i => i.x1)) - Math.min(...b.map(i => i.x1)));
        console.log(`检测到列数: ${columns.length}`);

        // 5. 列内分行并排序
        const sorted = [];
        const rowCounts = [];
        columns.forEach((colItems, ci) => {
            // 该列平均高度
            const avgH = colItems.reduce((sum, i) => sum + i.height, 0) / colItems.length;
            // 按 y1 排序
            colItems.sort((a, b) => a.y1 - b.y1);
            // 分行：基于垂直重叠度 > 0.8
            const rows = [];
            let currentRow = [colItems[0]];
            for (let j = 1; j < colItems.length; j++) {
                const prev = colItems[j - 1];
                const curr = colItems[j];
                // 计算垂直重叠高度
                const overlapY = Math.min(prev.y2, curr.y2) - Math.max(prev.y1, curr.y1);
                const overlapRatio = overlapY > 0 ? overlapY / curr.height : 0;
                if (overlapRatio > 0.8) {
                    // 重叠度大于80%，同一行
                    currentRow.push(curr);
                } else {
                    // 不同一行，推送上一行并开启新行
                    rows.push(currentRow);
                    currentRow = [curr];
                }
            }
            // 添加最后一行
            rows.push(currentRow);
            // 记录并打印行数
            rowCounts.push(rows.length);
            console.log(`第 ${ci + 1} 列行数: ${rows.length}`);
            // 行内 x 排序并扁平化
            rows.forEach(row => {
                row.sort((a, b) => a.x1 - b.x1);
                row.forEach(it => sorted.push(it.orig));
            });
        });


        ;
        const totalRows = rowCounts.reduce((sum, c) => sum + c, 0);
        console.log(`每列行数: ${rowCounts.join(', ')}，总行数: ${totalRows}`);

        return [...sorted, ...invalid];
    }
;

/**
 * 比较两组 areas 顺序和值是否完全一致
 */
export const isSameOrder = (a1, a2) => {
    if (a1.length !== a2.length) return false;
    return a1.every((a, i) => {
        const b = a2[i];
        const ax1 = a.area?.x, ay1 = a.area?.y, aw = a.area?.width, ah = a.area?.height;
        const bx1 = b.area?.x, by1 = b.area?.y, bw = b.area?.width, bh = b.area?.height;
        return ax1 === bx1 && ay1 === by1 && aw === bw && ah === bh;
    });
};
