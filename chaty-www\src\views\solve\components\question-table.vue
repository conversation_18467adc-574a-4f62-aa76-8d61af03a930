<template>
  <div>
    <el-table :data="tableData" style="width: 100%" border >
      <!-- 序号列 -->
      <el-table-column
          label="序号"
          width="70"
          align="center"
          prop="idx">
      </el-table-column>

      <!-- 问题列，宽度设为300 -->
      <el-table-column
          prop="question"
          label="问题"
          width="300"
          align="center">
      </el-table-column>

      <!-- 正确答案列，宽度设为100 -->
      <el-table-column
          prop="correctAnswer"
          label="正确答案"
          width="100"
          align="center">
      </el-table-column>

      <!-- 动态生成的其他列，居中显示 -->
      <el-table-column
          v-for="(column, index) in dynamicColumns"
          :key="index"
          :prop="column.prop"
          :label="column.label || '模型T'"
          align="center">
        <template v-slot="scope">
          {{scope.row[column.prop]}}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      required: true
    },
    dynamicColumns: {
      type: Array,
      required: true,
      default: () => []
    }
  }
}
</script>

<style scoped>
/* 如果需要进一步控制表格样式可以在此处添加 */
</style>
