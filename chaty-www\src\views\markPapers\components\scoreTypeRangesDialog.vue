<template>
  <el-dialog v-model="isShow" title="个性化导出设置" width="800" :before-close="() => beforeClose()">
    <el-form :model="form" label-width="80px">
      <el-form-item label="选择学校">
        <el-select v-model="activeSchoolIndex" placeholder="请选择学校" @change="onSchoolChange">
          <el-option
              v-for="(school, index) in schools"
              :key="school.schoolId"
              :label="school.schoolName"
              :value="index"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择班级" v-if="activeSchoolIndex !== -1">
        <el-select v-model="activeClassIndex" placeholder="请选择班级" @change="onClassChange">
          <el-option
              v-for="(cls, index) in classList"
              :key="cls.classId"
              :label="cls.className"
              :value="index"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否过滤姓名不匹配学生" label-width="176px">
        <el-radio-group v-model="form.filterEnabled">
          <el-radio :label="true">筛选</el-radio>
          <el-radio :label="false">不过滤</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="学号排序">
        <el-switch v-model="form.isSortByStuNo" active-text="排序" inactive-text="不排序"></el-switch>
      </el-form-item>

      <el-form-item>
        <div style="width: 91%;display: flex;flex-direction: row-reverse;margin-top: 10px">
          <el-button type="primary" @click="onsubmit">生成excel</el-button>
          <el-button type="danger" @click="beforeClose" style="margin-right: 10px">{{ $t('common.cancel') }}</el-button>
        </div>

      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import {getSchools} from "@/api/auth";

export default {
  data() {
    return {
      isShow: false,
      form: {
        type: '',
        students: [],
        filterEnabled: false,
        isSortByStuNo: true
      },
      width: '500px',
      schools: [],
      classList: [],
      activeSchoolIndex: -1,
      activeClassIndex: -1,
      defaultSchoolId: null,
      defaultClassId: null
    };
  },
  methods: {
    // 获取学校列表
    fetchSchools() {
      this.$axios.get('/api/school/all').then(res => {
        this.schools = res.data;
        // 如果未选中学校且数据存在，则默认选中第一个学校
        if (this.schools.length && this.activeSchoolIndex === -1 && !this.defaultSchoolId) {
          this.activeSchoolIndex = 0;
          this.onSchoolChange();
        } else if(this.defaultSchoolId) {
          let index = this.schools.findIndex(school => school.schoolId === this.defaultSchoolId);
          this.activeSchoolIndex = index === -1 ? 0 : index;
          this.onSchoolChange();
          this.defaultSchoolId = null;
        }
      });
    },
    // 根据选中的学校获取班级列表
    fetchClasses() {
      if (
          this.activeSchoolIndex === -1 ||
          !this.schools.length ||
          !this.schools[this.activeSchoolIndex]?.schoolId
      ) {
        return;
      }
      this.$axios
          .get(`/api/class/by-school/${this.schools[this.activeSchoolIndex].schoolId}`)
          .then(res => {
            this.classList = res.data;
            // 如果班级列表存在且没有默认选中，则默认选中第一个班级
            if (this.classList.length && this.activeClassIndex === -1 && !this.defaultClassId) {
              this.activeClassIndex = 0;
              this.onClassChange();
            } else if (this.defaultClassId) {
              let index = this.classList.findIndex(cls => cls.classId === this.defaultClassId);
              this.activeClassIndex = index === -1 ? 0 : index;
              this.onClassChange();
              this.defaultClassId = null;
            }
          });
    },
    // 根据选中的班级获取学生列表
    fetchStudents() {
      if (this.activeClassIndex === -1 || !this.classList.length) {
        return;
      }
      this.$axios
          .get(`/api/user/student/list/${this.classList[this.activeClassIndex].classId}`)
          .then(res => {
            this.form.students = res.data;
          });
    },
    onSchoolChange() {
      this.activeClassIndex = -1;
      this.classList = [];
      this.form.students = [];
      this.fetchClasses();
    },
    // 当班级变化时，获取学生列表
    onClassChange() {
      this.fetchStudents();
    },
    show(schoolId, classId) {
      this.defaultClassId = classId;
      this.defaultSchoolId = schoolId;

      this.fetchSchools();

      this.isShow = true;
    },
    onsubmit() {
      if (!this.form.students.length) {
        this.$message.error('无学生！');
        return;
      }
      let students = {};
      this.form.students.forEach((student) => {
        students[student.nickname] = student.studentId
      })
      this.form.students = students;

      this.$emit('submit', this.form);
      this.beforeClose()
    },
    beforeClose() {
      this.form = {type: '', ranges: [], filterEnabled: false, students: []};
      this.isShow = false;
      this.activeClassIndex = -1;
      this.activeSchoolIndex = -1;
      this.schools = [];
      this.classList = [];
      this.form.isSortByStuNo = true;
      this.defaultSchoolId = null;
      this.defaultClassId = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dialog .el-card {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

.text-center {
  text-align: center;
}
</style>
