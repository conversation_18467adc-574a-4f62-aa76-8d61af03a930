<template>
    <el-scrollbar class="docpage-wrapper">
        <el-card class="question-card" v-for="question in questions" :key="question.id">
            <template #header>
                <div class="card-header">
                    <span>{{ `题目 ${question.id}` }}</span>
                    <el-button style="margin-left: auto;" text size="small" plain icon="Delete"
                        @click="doDelete(question.id)"></el-button>
                </div>
            </template>
            <div class="card-content">
                <el-text class="content-label" type="primary" tag="p">题目：</el-text>
                <el-text class="content-text" tag="p">{{ question.question }}</el-text>
                <el-text class="content-label" type="primary" tag="p">正确答案：</el-text>
                <el-text class="content-text" tag="p">{{ question.correctAnswer }}</el-text>
                <el-text class="content-label" type="primary" tag="p">
                    高度：
                    <el-text class="content-text">{{ question.height }}</el-text>
                </el-text>
            </div>
        </el-card>
    </el-scrollbar>
</template>

<script>
export default {
    data() {
        return {
            questions: []
        }
    },
    methods: {
        add(question) {
            if (this.questions.find(q => q.id === question.id)) {
                this.$message.warning('请勿重复添加')
                return
            }
            this.questions.push(question)
            this.$message.success("添加成功")
        },
        doDelete(id) {
            this.questions = this.questions.filter(q => q.id !== id)
        },
        docGenerate() {
            if (this.questions.length === 0) {
                this.$message.warning('请添加题目')
                return
            }
            this.$axios.post("/api/pdf/createDoc?template=docgenerate", {
                questions: this.questions
            }).then(res => {
                this.downloadFile(this.$fileserver.fileurl(res.data.fileUrl), "试卷.pdf")
            })
        },
        downloadFile(url, name) {
            // 使用fetch获取文件内容
            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    // 如果需要下载，可以使用前面提到的下载代码
                    const a = document.createElement("a");
                    a.style.display = "none";
                    a.href = URL.createObjectURL(blob);
                    a.download = name;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                })
                .catch(error => {
                    console.error('发生错误:', error);
                });
        },
    }
}
</script>

<style lang="scss" scoped>
.docpage-wrapper {

    .question-card {
        margin-bottom: 10px;

        :deep(.el-card__header) {
            padding: 5px 10px;
        }

        .card-header {
            display: flex;
            align-items: center;
        }

        .card-content {
            .content-text {
                min-height: 30px;
            }
        }
    }
}
</style>