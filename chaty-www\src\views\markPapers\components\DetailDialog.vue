<template>
  <el-dialog
      v-model="visible"
      title="详情信息"
      width="600px"
      @close="onClose"
  >
    <div class="detail-content">
      <p><span class="label">模型名称：</span><span class="value">
        <el-link type="primary" @click="showModelDetail(data.modelLabel)">{{ data.modelLabel }}</el-link>
        <el-icon class="copy-icon" @click="copyText(data.modelLabel)"><CopyDocument /></el-icon></span></p>
      <p>
        <span class="label">修改数量/总数量：</span>
        <span class="value">{{ data.modifiedCount }}/{{ data.totalCount }}<el-icon class="copy-icon" @click="copyText(data.modifiedCount + '/' + data.totalCount)"><CopyDocument /></el-icon></span>
        <el-tooltip placement="top" content="准确率1(批改失败视为本题批改结果为正确) 准确率2(批改失败视为未作答)">
          <span class="highlight">（准确率1：{{ data.accuracy1 }}% 准确率2：{{ data.accuracy2 }}%）<el-icon class="copy-icon" @click="copyText('准确率1：' + data.accuracy1 + '% 准确率2：' + data.accuracy2 + '%')"><CopyDocument /></el-icon></span>
        </el-tooltip>
      </p>
      <p>
        <span class="label">批改失败数量/总数量：</span>
        <span class="value">{{ data.failCount }}/{{ data.totalCount }}<el-icon class="copy-icon" @click="copyText(data.failCount + '/' + data.totalCount)"><CopyDocument /></el-icon></span>
        <span class="highlight">（占比：{{ data.failCountPercentage }}%<el-icon class="copy-icon" @click="copyText(data.failCountPercentage + '%')"><CopyDocument /></el-icon>）</span>
      </p>
      <p><span class="label">纠错时间：</span><span class="value">{{ data.timesStr }}<el-icon class="copy-icon" @click="copyText(data.timesStr)"><CopyDocument /></el-icon></span></p>
      <p v-if="data.durationText"><span class="label">批改总时长：</span><span class="value">{{ data.durationText }}<el-icon class="copy-icon" @click="copyText(data.durationText)"><CopyDocument /></el-icon><span class="tip">（Tip：从开始批改到结束的时间，并非每个请求的耗时之和。受同时批改试卷数量的影响）</span></span></p>
      <p v-if="data.stats"><span class="label">平均时长：</span><span class="value">{{ (data.stats?.averageTimeConsumption || 0.00).toFixed(2) }}s<el-icon class="copy-icon" @click="copyText((data.stats?.averageTimeConsumption || 0.00).toFixed(2) + 's')"><CopyDocument /></el-icon></span></p>
      <p v-if="data.stats"><span class="label">总时长：</span><span class="value">{{ data.stats?.totalTimeConsumption }}s / {{data.stats?.totalCount}}个<el-icon class="copy-icon" @click="copyText(data.stats?.totalTimeConsumption + 's / ' + data.stats?.totalCount + '个')"><CopyDocument /></el-icon></span></p>
      <p v-if="data.stats?.nullCount"><span class="label">无时间记录的数量：</span><span class="value">{{ data.stats?.nullCount }}个<el-icon class="copy-icon" @click="copyText(data.stats?.nullCount + '个')"><CopyDocument /></el-icon></span></p>
      <p ><span class="label">平均分：</span><span class="value">{{ data.averageScore }} / {{data.onePaperTotalScore}} (分)<el-icon class="copy-icon" @click="copyText(data.averageScore + ' / ' + data.onePaperTotalScore + ' (分)')"><CopyDocument /></el-icon></span></p>
      <p ><span class="label">中位数：</span><span class="value">{{ data.medianScore }} / {{data.onePaperTotalScore}} (分)<el-icon class="copy-icon" @click="copyText(data.medianScore + ' / ' + data.onePaperTotalScore + ' (分)')"><CopyDocument /></el-icon></span></p>
      <p ><span class="label">总分之和：</span><span class="value">{{ data.totalScoreSum }} 分/ {{data.studentCount}} 人<el-icon class="copy-icon" @click="copyText(data.totalScoreSum + '分/ ' + data.studentCount + '人')"><CopyDocument /></el-icon></span></p>
    </div>
    <template #footer>
      <el-button type="primary" @click="onClose">关闭</el-button>
    </template>
    <ModelRequestDetailDialog ref="modelRequestDetailDialog"></ModelRequestDetailDialog>
  </el-dialog>
</template>

<script>
import { CopyDocument } from '@element-plus/icons-vue';
import ModelRequestDetailDialog from "@/views/ModelRequest/modelRequestDetailDialog.vue";
import {useUserStore} from "../../../store";

const store = useUserStore();
export default {
  name: 'DetailDialog',
  components: { CopyDocument , ModelRequestDetailDialog},
  data() {
    return {
      visible: false,
      data: {
        modelLabel: '',
        modifiedCount: 0,
        totalCount: 0,
        accuracy: '0.00',
        timesStr: '',
        durationText: '',
        failCount: 0,
        failCountPercentage: '0.00',
        totalScoreSum: 0,
        studentCount: 0,
        averageScore: 0,
        medianScore: 0,
        onePaperTotalScore: 0,
      }
    };
  },
  methods: {
    showModelDetail(label) {
      const opts = Object.values(store.getAimodelOptions)
      const found = opts.find(o => o.label === label)

      this.$refs.modelRequestDetailDialog.show(found);
    },
    onClose() {
      this.visible = false;
    },
    show(data) {
      this.data = data;
      this.visible = true;
    },
    copyText(text) {
      if (!text && text !== 0) return;
      navigator.clipboard.writeText(String(text)).then(() => {
        this.$message.success('已复制');
      }).catch(() => {
        this.$message.error('复制失败，请手动复制');
      });
    }
  }
}
</script>

<style scoped>
/* 整体字体放大 */
.detail-content {
  font-size: 16px;
  line-height: 1.8;
}

/* 每行间距增大 */
.detail-content p {
  margin: 12px 0;
}

/* 标签部分加粗 */
.detail-content .label {
  font-weight: 600;
  font-size: 17px;
}

/* 数值部分稍弱一些对比 */
.detail-content .value {
  font-weight: 400;
}

/* 准确率等重点信息着重显示 */
.detail-content .highlight {
  font-weight: 700;
  font-size: 17px;
  color: #409EFF; /* Element Plus 主色调 */
}

.copy-icon {
  cursor: pointer;
  margin-left: 6px;
  font-size: 16px;
  color: #409EFF;
  vertical-align: middle;
}

.detail-content .tip {
  margin-left: 6px;
}
</style>
