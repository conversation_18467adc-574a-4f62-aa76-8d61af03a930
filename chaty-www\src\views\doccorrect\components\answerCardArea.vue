<template>
    <el-card class="qs-card">
        <template #header>
            <div class="qs-card-header">
                <span>区域{{ index+1 }}</span>

                <el-button text class="right-actions" @click="$emit('copyArea', index)">复制</el-button>
                <el-button text icon="Plus" @click="addQuestion">添加题目</el-button>
                <el-button text icon="Delete" @click="$emit('deleteArea', index)">删除</el-button>
                <el-switch style="margin: 0 10px;" v-model="area.enabled" active-text="启用" />
            </div>
        </template>
        <el-collapse v-model="area.expand">
            <el-collapse-item v-for="(question, qsIdx) in area.questions" :key="qsIdx" :name="qsIdx">
                <template #title>
                    <el-text style="min-width: 80px; text-align: left;">{{ `问题${qsIdx + 1}` }}</el-text>
                    <el-tag style="margin-left: 10px;" v-if="question.scoreType">{{ question.scoreType
                        }}</el-tag>
                    <el-tag v-show="isScore" style="margin-left: 10px;">
                        {{ `分数: ${question.score}` }}</el-tag>
                    <el-tag style="margin-left: 10px;" :type="question.flagArea ?? 'warning'">
                        勾和叉的区域</el-tag>
                    <el-button style="margin-left: 10px;" text @click="copyQs(qsIdx)">复制</el-button>
                </template>
                <el-form class="qs-form" :model="question" label-position="top">
                    <el-form-item v-show="isScore" label="分数：" prop="score">
                        <el-input-number v-model="question.score" :controls="false" />
                    </el-form-item>
                    <el-form-item prop="choiceMode" label="选项类型">
                        <el-select v-model="question.choiceMode" style="width: 200px" placeholder="请选择选项类型">
                            <el-option v-for="item in Object.values(choiceModes)" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="choiceNum" label="选项数量">
                        <el-input-number v-model="question.choiceNum" :controls="false" :min="1" :precision="0" />
                    </el-form-item>
                    <el-form-item prop="answer" label="正确答案位置">
                        <el-select v-model="question.answer" style="width: 200px" allow-create multiple filterable
                            placeholder="请输入答案"></el-select>
                    </el-form-item>
                    <el-form-item prop="scoreType" label="分数类型">
                        <el-select v-model="question.scoreType" style="width: 200px"
                            :disabled="question.isAdditional === 2" placeholder="请选择分数类型">
                            <el-option v-for="item in scoreTypes" :key="item" :label="item" :value="item" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <!-- 勾和叉的区域 -->
                        <el-button
                            v-if="cropOptions.type === 'flag' && cropOptions.areaIdx === index && cropOptions.qsIdx === qsIdx"
                            type="warning" icon="Crop"
                            @click="$emit('crop', 'flag', { areaIdx: index, qsIdx })">勾和叉的位置</el-button>
                        <el-button v-else-if="question.flagArea" type="success" icon="Crop"
                            @click="$emit('crop', 'flag', { area: question.flagArea, areaIdx: index, qsIdx })">勾和叉的位置</el-button>
                        <el-button v-else type="primary" icon="Crop"
                            @click="$emit('crop', 'flag', { areaIdx: index, qsIdx })">勾和叉的位置</el-button>

                        <!-- 选项区域 -->
                        <el-button
                            v-if="cropOptions.type === 'option' && cropOptions.areaIdx === index && cropOptions.qsIdx === qsIdx"
                            type="warning" icon="Crop"
                            @click="$emit('crop', 'option', { areaIdx: index, qsIdx })">选项区域</el-button>
                        <el-button v-else-if="question.optionArea" type="success" icon="Crop"
                            @click="$emit('crop', 'option', { area: question.optionArea, areaIdx: index, qsIdx })">选项</el-button>
                        <el-button v-else type="primary" icon="Crop"
                            @click="$emit('crop', 'option', { areaIdx: index, qsIdx })">选项</el-button>

                        <el-button type="warning" icon="Delete" @click="deleteQs(qsIdx)">删除</el-button>
                    </el-form-item>
                </el-form>
            </el-collapse-item>
        </el-collapse>
    </el-card>
</template>
<script>
export default {
    props: {
        value : {
            type: Object,
            required: true
        },
        index: {
            type: Number,
            required: true
        },
        cropOptions: {
            type: Object,
            required: true
        },
        isScore: {
            type: Boolean,
            default: false
        },
        scoreTypes: {
            type: Array,
            default: () => [],
        }
    },
    data() {
        return {
            area: this.value,
            choiceModes: {
                0: {value: 0, label: '横向'},
                1: {value: 1, label: '竖向'},
            },
        }
    },
    watch: {
        area: {
            handler(val) {
                this.handleAreaChange();
            },
            deep: true
        }
    },
    methods: {
        handleAreaChange() {
            this.$emit('input:value', this.area);
        },
        updateValue() {
            this.area = this.value;
        },
        addQuestion() {
            this.area.questions.push({
                score: 1,
                scoreType: '总分',
                flagArea: null,
                optionArea: null,
                choiceMode: 0,
                choiceNum: 1,
                answer: [],
            });
        },
        copyQs(qsIdx) {
            let qs = JSON.parse(JSON.stringify(this.area.questions[qsIdx]))
            this.area.questions.push(qs)
        },
        deleteQs(qsIdx) {
            if (this.cropOptions.areaIdx === this.index && this.cropOptions.qsIdx === qsIdx) {
                this.$message.error('请取消选择!')
                return
            }
            this.area.questions.splice(qsIdx, 1)
        }
    }
}
</script>

<style lang="scss" scoped>
.qs-card {
    margin-bottom: 20px;

    .qs-card-header {
        display: flex;
        align-items: center;
        margin: -10px 0;

        .right-actions {
            margin-left: auto;
        }

        .card-header-select {
            width: 80px;
            margin-left: 10px;
        }
    }

    .qs-form {
        :deep(.el-form-item--small) {
            margin-bottom: 10px;
        }

        :deep(.el-divider--horizontal) {
            margin: 15px 0;
        }
    }
}
</style>