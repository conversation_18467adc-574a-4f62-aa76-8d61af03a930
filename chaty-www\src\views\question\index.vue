<template>
    <div class="container">
        <el-form class="header-bar" :inline="true">
            <el-form-item :label="`${$t('common.question')}：`">
                <el-input v-model="filter.question" />
            </el-form-item>
            <el-form-item :label="`${$t('common.knowledge')}：`">
                <el-input v-model="filter.knowledge" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="loadQuestions">{{ $t('common.search') }}</el-button>
                <el-button type="primary" @click="handleAdd">{{ $t('common.add') }}</el-button>
            </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="questions" style="width: 100%" :border="true" size="large" 
            class="main-table" :empty-text="$t('common.emptyData')">
            <template v-for="column in columns" :key="column.prop">
                <el-table-column v-bind="column" :label="$t(column.t)">
                    <template v-if="column.prop === 'operations'" #default="scope">
                        <el-button type="primary" @click="handleEdit(scope.row)">{{ $t('common.edit') }}</el-button>
                        <el-button type="danger" @click="handleDelete(scope.row.id)">{{ $t('common.delete') }}</el-button>
                    </template>
                    <template v-else-if="column.type === 'textarea'" #default="scope">
                        <el-scrollbar max-height="200px">
                            <el-text style="white-space: pre-wrap; word-break: break-all;">
                                {{ scope.row[column.prop] }}
                            </el-text>
                        </el-scrollbar>
                    </template>
                </el-table-column>
            </template>
        </el-table>
        <form-dialog :isShow="isShowForm" :title="formTitle" :data="checkedData" @onClose="onFormClose"></form-dialog>
    </div>
</template>

<script>
import FormDialog from './form-dialog.vue'
export default {
    components: {
        FormDialog,
    },
    data() {
        return {
            columns: [
                {
                    prop: "question",
                    label: "题目",
                    type: 'textarea',
                    t: "common.question"
                },
                {
                    prop: "answer",
                    label: "答案",
                    type: 'textarea',
                    t: "common.answer"
                },
                {
                    prop: 'knowledge',
                    label: '知识点',
                    type: 'textarea',
                    t: "common.knowledge"
                },
                {
                    prop: 'operations',
                    label: '操作',
                    t: "common.operations"
                }
            ],
            filter: {
                question: '',
                knowledge: '',
            },
            questions: [],
            loading: false,
            isShowForm: false,
            formTitle: '',
            checkedData: {},
        }
    },
    created() {
        this.loadQuestions()
    },
    methods: {
        loadQuestions() {
            this.loading = true
            let params = {}
            if (this.filter.question && this.filter.question !== '') {
                params.question = this.filter.question
            }
            if (this.filter.knowledge && this.filter.knowledge !== '') {
                params.knowledge = this.filter.knowledge
            }
            this.$axios.get("/api/questionLibrary/findAll", { params }).then(res => {
                this.questions = res.data
            }).finally(() => {
                this.loading = false
            })
        },
        handleAdd() {
            this.formTitle = this.$t('common.add')
            this.isShowForm = true
        },
        onFormClose(submited) {
            this.isShowForm = false
            if (submited) {
                this.checkedData = {}
                this.loadQuestions()
            }
        },
        handleEdit(data) {
            this.formTitle = this.$t('common.edit')
            this.checkedData = data
            this.isShowForm = true
        },
        handleDelete(id) {
            this.$confirm(this.$t('common.confirmDelete'), {
                confirmButtonText: this.$t('common.confirm'),
                cancelButtonText: this.$t('common.cancel'),
                type: 'warning'
            }).then(() => {
                this.$axios.post(`/api/questionLibrary/delete?id=${id}`).then(res => {
                    this.$message.success(this.$t('common.deleteSuccess'))
                    this.loadQuestions()
                })
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .header-bar {
        padding: 10px;
    }

    .main-table {
        flex: 1;
    }
}
</style>