<template>
  <el-dialog
      v-model="isShow"
      title="请选择操作类型"
      width="400px"
      :before-close="onClose"
  >
    <el-form :model="formData" label-position="top" ref="formRef">
      <el-form-item label="请选择类型" prop="selectedType">
        <el-radio-group v-model="formData.selectedType" class="radio-group">
          <el-radio
              v-for="(label, value) in options"
              :key="value"
              :label="value"
              class="export-style"
          >
            {{ label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="onConfirm" :loading="loading">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "TypeSelectorDialog",
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShow: false,
      formData: {
        selectedType: "identifyAndStuNo",
      },
      options: {
        identifyAndStuNo: "重新识别 姓名+学号",
        identify: "重新识别 姓名",
        stuNo: "重新识别 学号",
      },
      taskId: null
    };
  },
  methods: {
    show(taskId) {
      this.taskId = taskId;
      this.isShow = true;
    },
    onClose() {
      this.formData.selectedType = "identifyAndStuNo";
      this.isShow = false;
      this.taskId = null;
    },
    onConfirm() {
      this.$emit("submit", this.taskId, this.formData.selectedType);
      this.onClose();
    },
  },
};
</script>

<style scoped>
.radio-group {
  display: flex;
  flex-wrap: wrap;
}

.export-style {
  width: 200px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
</style>
