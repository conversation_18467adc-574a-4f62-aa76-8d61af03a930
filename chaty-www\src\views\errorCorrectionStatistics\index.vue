<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex; align-items: center; gap: 24px;">
          <div style="display: flex; align-items: center;">
            <el-image src="/icon/16.png" class="left-icon"></el-image>
            <el-text class="title">纠错统计</el-text>
          </div>
          <el-autocomplete
              class="filter-item"
              v-model="filter.docname"
              :fetch-suggestions="remoteSearch"
              placeholder="请输入名称"
              :debounce="300"
              style="width: 200px"
              trigger-on-focus="false"
          />
          <el-switch v-model="filter.hasChange" active-text="过滤未修改试卷" style="margin-left: 20px"></el-switch>
          <el-switch v-model="filter.filterNoChange" active-text="过滤未修改题目" style="margin-left: 20px"></el-switch>
          <el-button type="primary" @click="loadFile" style="margin-left: 20px">搜索</el-button>
        </div>
      </template>
    </el-page-header>
    <div class="main-content">
      <div class="left-content" :style="{ width:  boxWidth + 'px'}">
        <record
            ref="record"
            :records="records"
            :loading-record="loadingRecord"
            @show="recordChangePageIdx"
        />
        <div style="margin-top: 10px;display: flex;align-items: center;flex-direction: row">
          <el-select v-model="page.pageSize" style="width: 100px" @change="loadFile">
            <el-option :key="10" :value="10" label="10个/页">10个/页</el-option>
            <el-option :key="15" :value="15" label="15个/页">15个/页</el-option>
            <el-option :key="20" :value="20" label="20个/页">20个/页</el-option>
            <el-option :key="50" :value="50" label="50个/页">50个/页</el-option>
          </el-select>
          <el-pagination
              background
              layout="prev, pager, next"
              v-model:current-page="page.pageNumber"
              :total="total"
              :page-size="1"
              style="margin-left: 10px"
              @current-change="loadFile"
          />
          <el-tooltip placement="top" content="全部展开">
            <el-button style="margin-left: 10px;width: 35px" icon="Expand" @click="$refs.record.expandAll()"></el-button>
          </el-tooltip>
          <el-tooltip placement="top" content="全部展开">
            <el-button style="margin-left: 10px;width: 35px" icon="Fold" @click="$refs.record.collapseAll()"></el-button>
          </el-tooltip>
        </div>

      </div>
      <el-tooltip placement="top" content="拖拽调整左右栏比例">
        <div class="resize-handle" @mousedown="startResize" ref="yinDaoRef1" id="yinDaoRef1"></div>
      </el-tooltip>
      <div class="right-content" v-if="records.length">

        <cropper v-if="!isResizing"
                 ref="cropper"
                 class="canvas"
                 :id="records?.[nowCropper.paperIdx].taskId"
                 :loading="loadingCropper"
                 :left-top-text="(nowCropper.pageIdx + '/' + records?.length + '('+hasChange+')')"
                 :warning-text="warningText"
                 :is-freeze-keyboard="isFreezeKeyboard"
                 :bottom-center-text="calcScore()"
                 style="margin-top: 15px"
                 @arrow-key-pressed="arrowKeyPressed"
        >
        </cropper>
        <div v-else class="canvas" style="text-align: center">尺寸调整中</div>
        <div class="pagination">
          <div style="display: flex;width: 55px;flex-shrink: 0;margin-left: 10px;align-items: center">
            <el-input v-model="pageIdx" size="small" style="width: 32px;height: 32px;"
                      @change="changePageIdx"></el-input>
            <div style="margin-left: 5px">页</div>
          </div>
          <div style="width: 5px;flex-shrink: 1"></div>
          <el-pagination
              background
              layout="prev, pager, next"
              :total="records?.length"
              v-model:current-page="nowCropper.pageIdx"
              :page-size="1"
              class="right"
              @current-change="currentChange"
          />
        </div>
      </div>
    </div>


    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <img src="/flag.svg" id="flagimg" style="display: none;"/>
    <img src="/wrong.svg" id="wrongImg" style="display: none;"/>
    <img src="/bandui.svg" id="banduiImg" style="display: none;"/>
    <canvas ref="uploadCanvas" id="correct-canvas" style="display: none;"></canvas>
    <img class="cropper-img" :src="imgDataUrl" id="drawImg" style="display: none;">

  </div>
</template>

<script>
import {useUserStore} from "@/store/index";
import {mapActions, mapState} from "pinia";
import Cropper from '@/components/cropper/forPreview.vue';
import Record from "./record.vue";
import * as pdfjsLib from 'pdfjs-dist';
import {More} from "@element-plus/icons-vue";

import {initScoreRange} from "@/utils/scoreRangeItem";

const store = useUserStore();

export default {
  components: {
    More,
    Record,
    Cropper,
  },
  data() {
    return {
      filter: {
        name: "",
        filterNoChange: true,
        hasChange: true
      },
      tasks: [],
      statusOptions: {
        1: {value: 1, label: "待批改", type: "primary"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      refresher: null,
      statusStats: {
        1: 0,
        2: 0,
        3: 0,
        total: 0,
      },
      downloading: false,
      statsDownloading: false,
      resultSaving: {},
      file: null,
      expandRows: [],
      cropperDetail: {},
      nowCropper: {
        // 真实的数组idx
        paperIdx: 0,
        pageIdx: 1,
        docUrl: null
      },
      changePaginationLoading: false,
      autoCorrect: false,
      loadingCropper: false,
      isRefreshingDataAndDontNeedInitCropper: false,
      firstLoadNeedInitCropper: true,
      imgDataUrl: null,
      remoteFileSaveCheckList: [],
      printDialogVisible: false,
      checkedList: [],
      isExportBatch: false,
      localFileDetails: [],
      exportBatchFileDetails: [],
      exportBatchPapers: [],
      imagesList: {},
      loadingImagesList: [],
      base64Images: null,
      allTaskData: {},
      XYOffsetData: {
        xOffset: 0,
        yOffset: 0
      },
      pageIdx: 1,
      boxWidth: 750,
      isResizing: false,
      startX: 0,
      warningText: '',
      useAimodel: '',
      refreshSetTimeout: null,
      folderPath: '',
      isFreezeKeyboard: false,
      isFreezeKeyboardTempSave: false,
      boxWidthInterval: null,
      page: {
        pageNumber: 1,
        pageSize: 10
      },
      records: [],
      configs: {},
      total: 0,
      hasChange: '已修改',
      loadingRecord: true,
      pageSizes: [100, 200, 300, 400]
    }
  },
  created() {
    this.boxWidth = 650;
    this.loadFile();
  },
  watch: {
    "nowCropper.pageIdx"(val) {
      this.nowCropper.paperIdx = val - 1;
      const record = this.records?.[val - 1] ?? null;
      this.nowCropper.docUrl = record?.docurl ?? null;

      this.hasChange = record?.hasChange ? '已修改' : '未修改';
      this.initCropper();
    }
  },
  computed: {
    ...mapState(useUserStore, ["getUser"])
  },
  methods: {
    ...mapActions(useUserStore, ['setXYOffsetData', 'setNeedMarkPaperFileTaskGuide', 'setNowCropperByFileIds']),
    calcScore() {
      if (!this.nowCropper.taskId || !this.allTaskData) {
        return ''
      }
      let configs = this.allTaskData.tasks.map((item) => {
        return this.allTaskData[item.id].config;
      });
      let records = this.allTaskData.tasks.map((item) => {
        return this.allTaskData[item.id].records[this.nowCropper.paperIdx];
      });
      let scored = 0
      let total = 0
      let addScored = 0
      let addTotal = 0
      let addName = configs?.[0].configObj.additionalName || '附加';

      configs.forEach((config, configIdx) => {
        let record = records[configIdx];
        if (!record) return;
        config.areasObj.forEach((area, areaIdx) => {
          // 手写识别
          let areaType = area.areaType
          if (area.enabled) {
            area.questions.forEach((qs, qsIdx) => {
              let isAdd = (qs.isAdditional || 1) !== 1;
              if (qs.isScorePoint === 2 || areaType === 4) {
                // 按照得分点给分
                scored += parseInt(record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.scored || 0)
              } else {
                let isTrue = (record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.isCorrect || 'Y') === 'Y'
                if (record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.isEssay) {
                  scored += isAdd ? 0 : parseFloat(record?.reviewedObj[areaIdx]?.reviewed?.[qsIdx]?.scored)
                } else if (isTrue) {
                  scored += isAdd ? 0 : qs.score
                  addScored += isAdd ? qs.score : 0
                }
              }
              total += isAdd ? 0 : qs.score
              addTotal += isAdd ? qs.score : 0
            })
          }
        })
      })


      if (addTotal > 0) {
        return `总分:${scored}/${total}  ${addName}: ${addScored}/${addTotal}`
      } else {
        return `总分:${scored}/${total}`
      }
    },
    refreshRecordNameOrStudentNumber(e, sameNumberCount) {
      this.isFreezeKeyboard = false;
      this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].identify = e.identify;
      this.allTaskData[this.nowCropper.taskId].records[this.nowCropper.paperIdx].studentNumber = e.studentNumber;
      this.preview();
      if (sameNumberCount && this.$refs.cropper.isFullScreen) {
        // 提醒
        this.warningText = `当前试卷有${sameNumberCount}个重复的学号${e.studentNumber}，请注意！`
        setTimeout(() => {
          this.warningText = '';
        }, 3000)
      }
    },
    startResize(event) {
      this.isResizing = true;
      this.startX = event.clientX;
      document.addEventListener("mousemove", this.onMouseMove);
      document.addEventListener("mouseup", this.onMouseUp);
    },
    onMouseMove(event) {
      if (this.isResizing) {
        const deltaX = event.clientX - this.startX;
        this.boxWidth += deltaX;
        this.startX = event.clientX;
        // 延时更改
        if (this.boxWidthInterval === null) {
          clearTimeout(this.boxWidthInterval);
        }
        this.boxWidthInterval = setTimeout(() => {
          store.setMarkPaperLeftWidth(this.boxWidth);
        }, 1500);

      }
    },
    onMouseUp() {
      this.isResizing = false;
      document.removeEventListener("mousemove", this.onMouseMove);
      document.removeEventListener("mouseup", this.onMouseUp);
      this.$nextTick(() => {
        this.initCropper();
      })
      // this.initCropper()
    },
    recordChangePageIdx(idx) {
      this.nowCropper.pageIdx = idx + 1;
    },
    changePageIdx(e) {
      let nowCropper = JSON.parse(JSON.stringify(this.nowCropper));
      if (e === '0') {
        return this.$message.error('请输入正确的页码');
      } else if (e > this.allTaskData[nowCropper.taskId].records.length) {
        return this.$message.error('页码超出范围');
      }
      nowCropper.pageIdx = parseInt(e);
      this.nowCropper = nowCropper;
    },
    handleXYOffsetDisplacementDialog(data) {
      this.XYOffsetData = data;
    },
    arrowKeyPressed(e) {
      let pageIdx = this.nowCropper.pageIdx;
      if (e === 1) {
        if (pageIdx === this.records.length) {
          // 到头之后再加一页
          this.page.pageNumber = this.page.pageNumber + 1;
          this.loadFile();
          this.nowCropper.pageIdx = 1;
        } else {
          this.nowCropper.pageIdx = e + pageIdx;
        }
      } else if (e === -1) {
        if (pageIdx === 1) {
          // 到头之后不再重新
          this.$message.warning('已经是第一页');
          this.warningText = '已经是第一页'
          setTimeout(() => {
            this.warningText = ''
          }, 2000)
        } else {
          this.nowCropper.pageIdx = e + pageIdx;
        }
      }
    },
    currentChange(e) {
      if (this.expandRows.indexOf(this.nowCropper.taskId) === -1) {
        this.expandRows.push(this.nowCropper.taskId);
        this.$nextTick(() => {
          this.$refs.cropper.setImg(this.base64Images[e - 1]);
          this.preview();
        })
      }
    },
    async loadNextImage(nextDocUrl, nextNextDocUrl) {
      if (!nextDocUrl) return;
      if (nextDocUrl in this.imagesList) return;
      if (this.loadingImagesList.includes(nextDocUrl)) return;
      try {
        this.loadingImagesList.push(nextDocUrl);
        const response = await fetch(this.$fileserver.fileurl(nextDocUrl));
        this.loadingImagesList = this.loadingImagesList.filter(item => item !== nextDocUrl);
        if (!response.ok) {
          throw new Error('Failed to fetch PDF');
        }

        const blob = await response.blob();
        const pdfData = await blob.arrayBuffer();
        const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
        const totalPages = pdfDoc.numPages;
        const images = [];
        const targetDPI = 300;
        const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

        if (totalPages === 1) {
          const page = await pdfDoc.getPage(1);
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          const viewport = page.getViewport({scale: scaleFactor});
          canvas.width = viewport.width;
          canvas.height = viewport.height;

          // 将页面渲染到 canvas 上
          await page.render({
            canvasContext: context,
            viewport: viewport,
          }).promise;

          // 将 canvas 转换为 Base64 编码的图片（JPEG 格式）
          const imgDataUrl = canvas.toDataURL('image/jpeg');
          images.push(imgDataUrl);

          const keys = Object.keys(this.imagesList);
          if (keys.length > 10) {
            const firstKey = keys[0];
            delete this.imagesList[firstKey];
          }
          this.imagesList[nextDocUrl] = imgDataUrl;
        }
        // 演示更新
        setTimeout(() => {
          this.loadNextImage(nextNextDocUrl, '');
        }, 500)
      } catch (error) {
        console.error('加载下一个图像失败', error);
      }
    },
    async convertPdfUrlToBase64(pdfUrl, nextDocUrl, nextNextDocUrl) {
      if (this.isRefreshingDataAndDontNeedInitCropper) return;
      this.loadingCropper = true;
      try {
        // 通过 fetch 下载 PDF 文件
        if (pdfUrl in this.imagesList) {
          this.imgDataUrl = this.imagesList[pdfUrl];
          this.loadingCropper = false;
          this.preview();
          this.loadNextImage(nextDocUrl, nextNextDocUrl)
        } else {
          const response = await fetch(this.$fileserver.fileurl(pdfUrl));
          // 异步提前缓存下一个
          this.loadNextImage(nextDocUrl, nextNextDocUrl)
          if (!response.ok) {
            throw new Error('Failed to fetch PDF');
          }
          const blob = await response.blob();
          const pdfData = await blob.arrayBuffer();
          const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;

          // 获取 PDF 的总页数
          const totalPages = pdfDoc.numPages;
          const images = [];

          // 设置目标 DPI（300 DPI）
          const targetDPI = 300;

          // 获取每英寸的像素数，假设 PDF 使用标准的 72 DPI
          const scaleFactor = targetDPI / 72;  // PDF 默认分辨率通常是 72 DPI

          // 遍历每一页，将其转换为 Base64 图像
          if (totalPages === 1) {
            const page = await pdfDoc.getPage(1);

            // 创建一个 canvas 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 获取页面的渲染视口
            const viewport = page.getViewport({scale: scaleFactor});

            // 设置 canvas 大小为页面大小
            canvas.width = viewport.width;
            canvas.height = viewport.height;

            // 将页面渲染到 canvas 上
            await page.render({
              canvasContext: context,
              viewport: viewport,
            }).promise;
            const imgDataUrl = canvas.toDataURL('image/jpeg');
            images.push(imgDataUrl);
            this.loadingCropper = false;
            this.imgDataUrl = imgDataUrl;
            this.base64Images = images;
            this.imagesList[pdfUrl] = imgDataUrl;
            this.preview();
          }
        }

      } catch (error) {
        console.error('PDF 转换失败', error);
      }
    },
    preview() {
      let detail = this.records[this.nowCropper.paperIdx].cropperDetail
      let answers = [];
      let areas = [];
      let kuangs = [];
      let scopeTypes = ['总分'];
      let scopeTypesScore = {
        '总分': {scored: 0.0, fullMarks: 0.0}
      };
      let idx = 0;
      detail.forEach(area => {
        if (area.flagArea) {
          areas.push(Object.assign({
            color: 'green',
            mark: document.getElementById((area?.isScorePoint === 2 && area.fullMarks !== area.scored) ? 'banduiImg' : (area.isRight ? "flagimg" : "wrongImg")),
            isScorePoint: area.isScorePoint,
            text: area.answer === '无' ? area.scored : area.answer,
          }, area.flagArea))
          let text = area.answer === '无'
              ? `${area.scored}${area.isScorePoint === 2 ? `/${area.fullMarks}` : ''}` +
              `${area.scoredOriginal != null ? `  原分数-${area.scoredOriginal}` : ''}`
              : `${area.answer}` +
              `${area.isCorrectOriginal != null ? `  原判断-${area.isCorrectOriginal === 'Y' ? '正确' : '错误'}` : ''}`;
          if (area?.studentAnswer) {
            text +=  `    识别答案：${area.studentAnswer}`;
          }

          answers.push({
            area: area.flagArea,
            text: text,
            dontShowText: area?.dontShowText ?? false,
            font: '80px Arial'
          })
        }
        if (!scopeTypes.includes(area.scoreType)) {
          scopeTypes.push(area.scoreType)
        }
        let scored = Number(area?.scored ?? 0.0);
        let fullMarks = Number(area?.fullMarks ?? 0.0);
        if (area.scoreType !== '总分') {

          if (!(area.scoreType in scopeTypesScore)) {
            scopeTypesScore[area.scoreType] = {scored: 0.0, fullMarks: 0.0}
          }
          scopeTypesScore[area.scoreType].scored += scored;
          scopeTypesScore[area.scoreType].fullMarks += fullMarks;
        }
        scopeTypesScore['总分'].scored += scored;
        scopeTypesScore['总分'].fullMarks += fullMarks;
      })
      // 将scoreType的总分调整到最后
      scopeTypes = [...scopeTypes.slice(1), scopeTypes[0]];


      // 画框
      const configAreas = this.configs[this.records[this.nowCropper.paperIdx].configId].areasObj;
      if (configAreas && configAreas.length > 0) {
        kuangs.push(...configAreas.map(item => {
          return {
            area: item.area,
            color: item.areaType === 4 ? 'red' : ''
          }
        }))
      }
      console.log('refs', this.$refs.cropper)
      this.$refs.cropper.setImg(this.imgDataUrl, areas, answers, kuangs, false, true);
    },
    refresh() {
      this.loadFile();
    },
    initCropper() {
      this.nowCropper.docUrl = this.records[this.nowCropper.paperIdx].docurl;
      let nextDocUrl = '';
      const nowRecordSize = this.records.length;
      if (this.nowCropper.paperIdx + 1 < nowRecordSize) {
        nextDocUrl = this.records[this.nowCropper.paperIdx + 1].docurl;
      }
      let nextNextDocUrl = '';
      if (this.nowCropper.paperIdx + 2 < nowRecordSize) {
        nextNextDocUrl = this.records[this.nowCropper.paperIdx + 2].docurl;
      }
      this.convertPdfUrlToBase64(this.nowCropper.docUrl, nextDocUrl, nextNextDocUrl).then(res => {
      })
    },
    goBack() {
      this.$router.back();
    },
    async loadFile() {
      this.loadingRecord = true;
      const param = {
        page: this.page,
        docname: this.filter.docname,
        hasChange: this.filter.hasChange ? 1 : 0,
        orderByTaskId: true
      }
      this.$axios.post(`/api/docCorrectRecord/page`, param).then(async res => {
        this.records = res.data.records;
        this.total = res.data.total;
        await this.loadConfigs()
        this.loadingCropperDetail()
        this.getReviewData()
        this.loadingRecord = false;
        // this.refreshOffset();
        this.initCropper();
      })
    },
    getReviewData() {
      let data = this.records;
      let filterNoChange = this.filter.filterNoChange;
      for (let recordIdx = 0; recordIdx < data.length; recordIdx++) {
        const config = this.configs[data[recordIdx].configId];
        let questions = []
        let record = data[recordIdx]
        let hasChangeCount = 0;
        if (config) {
          let reviewed = data[recordIdx].reviewedObj;
          for (let areaIdx = 0; areaIdx < config.areasObj.length; areaIdx++) {
            let area = config.areasObj[areaIdx]
            let areaReviewed = reviewed.find((item) => {
              return item.areaIdx === areaIdx;
            })
            area.areaImg = areaReviewed?.areaImg // 批改图片
            area.status = areaReviewed?.status // 批改状态
            area.error = areaReviewed?.error // 错误
            config.areasObj[areaIdx] = area;
            for (let qsIdx = 0; qsIdx < area.questions.length; qsIdx++) {
              hasChangeCount += areaReviewed?.reviewed?.[qsIdx]?.hasChange === 1 ? 1 : 0;
              if (!(filterNoChange ? record.reviewedObj[areaIdx].reviewed[qsIdx].hasChange === 1 : true)) continue;
              let question = JSON.parse(JSON.stringify(area.questions[qsIdx]))
              let qsReviewed = areaReviewed?.reviewed?.[qsIdx]
              delete question.isCorrect;
              question = Object.assign(question, {
                areaIdx,
                qsIdx,
              }, qsReviewed)
              question.isCorrect = qsReviewed?.isCorrect;

              if (area.status !== '1') {
                question.review = area.error
              }

              if (area.areaType === 3 && question.review) {
                question.review = JSON.stringify(question.review)
              }
              question.areaType = area.areaType;
              question.areaImg = config.areasObj[areaIdx].areaImg;
              questions.push(question)
            }
          }
        }
        data[recordIdx].revirewData = questions;
        data[recordIdx].hasChangeCount = hasChangeCount;
      }
      this.records = data;
    },
    loadingCropperDetail() {
      let records = this.records;
      let filterNoChange = this.filter.filterNoChange;
      records.forEach(record => {
        let config = this.configs[record.configId];
        let detail = []
        for (let i = 0; i < record.reviewedObj.length; i++) {
          let areaIdx = record.reviewedObj[i].areaIdx;
          for (let qsIdx = 0; qsIdx < record.reviewedObj[i].reviewed.length; qsIdx++) {
            if (
                config &&
                config.areasObj &&
                Array.isArray(config.areasObj) &&
                config.areasObj[areaIdx] &&
                config.areasObj[areaIdx].questions &&
                qsIdx < config.areasObj[areaIdx].questions.length &&
                'flagArea' in config.areasObj[areaIdx].questions[qsIdx] &&
                (filterNoChange ? record.reviewedObj[i].reviewed[qsIdx].hasChange === 1 : true)
            ) {
              let scored = 0;
              if (record.reviewedObj[i].reviewed[qsIdx].isScorePoint) {
                scored = record.reviewedObj[i].reviewed[qsIdx]?.scored ?? 0
              } else {
                scored = record.reviewedObj[i].reviewed[qsIdx].isCorrect === 'Y' ? config.areasObj[areaIdx].questions[qsIdx]?.score ?? 0 : 0
              }
              detail.push({
                areaIdx: areaIdx,
                qsIdx: qsIdx,
                isRight: record.reviewedObj[i].reviewed[qsIdx].isCorrect === 'Y',
                flagArea: config.areasObj[areaIdx].questions[qsIdx].flagArea,
                answer: record.reviewedObj[i].reviewed[qsIdx].isEssay ? record.reviewedObj[i].reviewed[qsIdx].scored : config.areasObj[areaIdx].questions[qsIdx].answer,
                isEssay: record.reviewedObj[i].reviewed[qsIdx].isEssay,
                scored: scored,
                isScorePoint: record.reviewedObj[i].reviewed[qsIdx].isScorePoint,
                dontShowText: config.areasObj[areaIdx].questions[qsIdx]?.dontShowText ?? false,
                scoreType: config.areasObj[areaIdx].questions[qsIdx]?.scoreType ?? '总分',
                fullMarks: config.areasObj[areaIdx].questions[qsIdx]?.score ?? 0,
                isCorrectOriginal: record.reviewedObj[i].reviewed[qsIdx]?.isCorrectOriginal,
                scoredOriginal: record.reviewedObj[i].reviewed[qsIdx]?.scoredOriginal,
                studentAnswer: record.reviewedObj[i].reviewed[qsIdx]?.studentAnswer,
              })
            }
          }
        }
        record.cropperDetail = detail;
      })
      this.records = records;
    },
    async loadConfigs() {
      let configs = this.configs;
      let configIds = this.records.map(item => item.configId);
      // 去重
      configIds = [...new Set(configIds)];
      for (let i = 0; i < configIds.length; i++) {
        if (!(configIds[i] in configs)) {
          let response = await this.$axios.get(`/api/docCorrectConfig/get?id=${configIds[i]}`);
          configs[configIds[i]] = response.data;
          configs[configIds[i]].configObj = JSON.parse(configs[configIds[i]].config);
          configs[configIds[i]].areasObj = JSON.parse(configs[configIds[i]].areas);
        }
      }
      this.configs = configs;
    },
    remoteSearch(query, callback) {
      if (!query) {
        // 无输入时不展示任何选项
        return callback([]);
      }
      // 调用后端接口获取匹配名称列表
      const param = {
        page: {
          pageSize: 50,
          pageNumber: 1
        },
        name: query
      }
      this.$axios
          .post('/api/docCorrectTask/page', param)
          .then(res => {
            const results = res.data.records.map(item => ({
              value: item.name
            }));
            callback(results);
          })
          .catch(() => {
            // 出错时返回空列表
            callback([]);
          });
    },
  },
}
</script>

<style lang="scss" scoped>
.el-table {
  table-layout: fixed;
}

:deep(.el-page-header__header) {
  width: 100% !important;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;

  .step {
    width: 1069.83px;
    height: 42px;
    flex-shrink: 0;
    margin-bottom: 10px;
  }

  .header-bar {
    width: 100%;
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    flex-shrink: 0;

    .left-icon {
      width: 28.28px;
      height: 22.89px;
      transform: scaleX(-1);
    }

    .title {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-right: 19px;
    }

    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  .main-content {
    display: flex;
    height: 100vh;
    padding-bottom: 40px;
    width: 100%;
    gap: 10px;
    margin-top: -10px;

    .left-content {
      min-width: 0;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 650px;
    }

    .resize-handle {
      width: 5px;
      height: 100%;
      background-color: #dcdfe6;
      cursor: ew-resize;
      opacity: 0.5;
      flex-shrink: 0;
    }

    .right-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      .canvas {
        height: 100%;
        width: 100%;
      }

      .title {
        margin: 0 10px;
        font-size: 15px;
        position: absolute;
        top: 60px;
        right: 20px;
        display: flex;
      }

      .pagination {
        height: 50px;
        margin-top: 5px;
        display: flex;
        align-items: center;
        width: 100%;

        .left {
          width: 150px;
          flex-shrink: 0;
        }

        .right {
          flex-grow: 1;
        }

        .icon-with-text {
          display: flex;
          align-items: center;
          gap: 5px;
          white-space: nowrap;
          flex-shrink: 1;
        }

        .tooltip-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 120px;
          display: inline-block;
          font-size: 14px;
          color: #007bff;
          flex-shrink: 1;
        }
      }
    }
  }
}
</style>