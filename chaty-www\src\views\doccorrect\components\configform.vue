<template>
  <el-dialog v-model="isShow" title="高级设置" width="1000" :before-close="onClose">
    <el-form :model="form" label-position="right" inline :label-width="120">
      <el-card shadow="hover" style="margin-bottom: 20px;">
        <template #header>
          <span>批改样式设置</span>
        </template>

        <el-row :gutter="20" style="margin-bottom: 12px;">
          <el-col :span="12">
            <el-form-item label="字体大小：">
              <el-input-number v-model="form.fontSize" :controls="false"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="勾的大小：">
              <el-input-number v-model="form.flagSize" :controls="false"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 12px;">
          <el-col :span="12">
            <el-form-item label="勾的颜色：">
              <el-select v-model="form.flagColor" style="width: 150px;">
                <el-option v-for="item in colorOptions" :key="item.val" :label="item.label" :value="item.val" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="叉的颜色：">
              <el-select v-model="form.errorFlagColor" style="width: 150px;">
                <el-option v-for="item in colorOptions" :key="item.val" :label="item.label" :value="item.val" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 12px;">
          <el-col :span="12">
            <el-form-item label="分数颜色：">
              <el-select v-model="form.scoreColor" style="width: 150px;">
                <el-option value="red" label="红色"></el-option>
                <el-option value="green" label="绿色"></el-option>
                <el-option value="byScore" label="红绿"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分数字体大小：">
              <el-input-number v-model="form.scoreFontSize" :controls="false"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 12px;">
          <el-col :span="12">
            <el-form-item label="叉的大小：">
              <el-input-number v-model="form.errorFlagSize" :controls="false"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="叉符号：">
              <el-select v-model="form.errorFlag" style="width: 150px;">
                <el-option value="x" label="叉"></el-option>
                <el-option value="c" label="圈"></el-option>
                <el-option value="d" label="订正"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="勾符号：">
              <el-select v-model="form.correctFlag" style="width: 150px;">
                <el-option value="a" label="勾1"></el-option>
                <el-option value="b" label="勾2"></el-option>
                <el-option value="c" label="勾3"></el-option>
                <el-option value="d" label="勾4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <div style="text-align: right; margin-top: 16px;">
          <el-button type="primary" icon="refresh" @click="onSyncToOtherConfigs" :loading="syncToOtherConfigsLoading">
            同步到其他配置卷
          </el-button>
        </div>
      </el-card>

      <el-row>

        <el-col :span="12">
          <el-form-item label="题目识别-增强ocr">
            <!-- 使用 el-switch 组件 -->
            <el-switch v-model="form.qsOCRPlus" active-text="开启" inactive-text="关闭" :active-value="true"
                       :inactive-value="false"></el-switch>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="姓名旋转-右旋">
            <el-input-number v-model="form.studentNameRightRotation" placeholder="右旋" step="90" min="0"
                             max="360"></el-input-number>
            &nbsp 度
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="学号旋转-右旋">
            <el-input-number v-model="form.studentNumberRightRotation" placeholder="右旋" step="90" min="0"
                             max="360"></el-input-number>
            &nbsp 度
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="等级输出">
            <!-- 使用 el-switch 组件 -->
            <el-switch v-model="form.enableLevelOutput" active-text="开启" inactive-text="关闭" :active-value="true"
                       :inactive-value="false"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <range-item
          ref="rangeItem"
          v-show="form.enableLevelOutput"
          :id="configId"
          :ranges="form.ranges"
          :package-id="packageId"
      />


      <el-row>
        <el-col :span="24">
          <el-form-item label="提示词：">
            <el-input v-model="form.prompt" type="textarea" :autosize="{ minRows: 15, maxRows: 15 }"
                      style="width: 700px"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="题目识别：">
            <el-input v-model="form.qsOcrPrompt" type="textarea" :autosize="{ minRows: 15, maxRows: 15 }"
                      style="width: 700px"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="批改回答：">
            <el-button @click="showJsonDialog('DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1')">查看批改回答JSON数据</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="properties：">
            <el-button @click="showJsonDialog('DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2')">查看批改回答的 Properties
              JSON数据
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="识别回答：">
            <el-button @click="showJsonDialog('EXTRA_QS_RESPFORMAT_SCHEMA')">查看识别回答JSON数据</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-row :gutter="20">
        <el-col :span="4" :offset="20">
          <el-button type="warning" @click="onClose">取消</el-button>
          <el-button type="primary" @click="onSubmit">确认</el-button>
        </el-col>
      </el-row>
    </template>
  </el-dialog>
  <el-dialog v-model="jsonDialogVisible" title="查看 JSON 数据" width="800px">
    <json-viewer :value="currentJson"/>
    <template #footer>
      <el-button @click="jsonDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import {useUserStore} from "@/store";
import {JsonViewer} from "vue3-json-viewer"
import "vue3-json-viewer/dist/index.css";
import RangeItem from "@/views/markPapers/components/rangeItem.vue";

const store = useUserStore();
export default {
  components: {
    RangeItem,
    JsonViewer,
  },
  props: {
    defaultQsOcrPrompt: {
      type: String,
      required: false,
      default: null
    }
  },
  watch: {
    'form.enableLevelOutput': {
      handler(val) {
        if (val) {
          setTimeout(() => {
            if (this.$refs.rangeItem) {
              this.$refs.rangeItem.show()
            }
          }, 500)

        }
      }
    }
  },
  data() {
    return {
      isShow: false,
      configId: '',
      form: {
        name: "",
        docurl: "",
        img: "",
        score: false,
        scoreColor: 'red',
        scoreFontSize: 10,
        fontSize: 10,
        flagSize: 20,
        flagColor: 'red',
        errorFlagColor: 'red',
        errorFlagSize: 20,
        errorFlag: 'x',
        docType: store.getDefaultDocType,
        nameArea: null,
        scoreArea: null,
        additionalName: '附加',
        prompt: null,
        qsOcrPrompt: null,
        correctFlag: 'a',
        qsOCRPlus: true,
        studentNameRightRotation: 0,
        studentNumberRightRotation: 0,
        qsOcrService: '',
        ranges: {},
        enableLevelOutput: false
      },
      colorOptions: [
        {label: '绿色', val: 'green'},
        {label: '红色', val: 'red'},
      ],
      // defaultPrompt: "你是一位对任何事都一步一步思考的阅卷老师，需要帮助一位失去父母、手臂残疾的学生进行作业批改。你的任务是比较学生的手写回答与正确答案，来判断学生手写回答是否正确，批改的准确性对你的职业生涯至关重要。我会提供试卷中的题目、答案的相关信息、正确答案、题目满分，还有一张包含学生手写回答的图片。\n\n在批改过程中，请严格遵守以下规则：\n\n1. 如果学生答案为空白或无法识别，直接返回批改结果为错误，学生答案记为'NAN'。\n\n2. 请勿帮助学生作答，准确反馈学生的手写作答结果会帮助学生更好的进步。\n\n请按照以下步骤逐步批改试卷：\n\n1. 根据题目及相关信息找到学生手写回答的位置。\n\n2. 尽最大努力准确识别学生的手写回答，即使笔迹复杂或难以辨认。请根据题目的内容、上下文信息以及你的知识来辅助识别。如果无法确定，请写出最可能的答案。如果实在无法识别，学生答案记为'NAN'。\n\n3. 比较学生手写的回答与正确答案，给出批改结果，包括学生答案是否正确以及评价。请注意可能的细微差别，考虑答案是否可以接受。\n\n4. 根据题目的得分点为学生打分。如果学生的答案与正确答案接近时，请考虑给予部分分数，并在评价中说明理由。\n\n在批改过程中，请保持一步一步思考，保持耐心和细致，体现对学生的关怀和支持。\n",
      defaultPrompt: '',
      EXTRA_QS_RESPFORMAT_SCHEMA: '',
      DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1: '',
      DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2: '',
      jsonDialogVisible: false,
      currentJson: null,
      packageId: null,
      syncToOtherConfigsLoading: false
    }
  },
  mounted() {
    this.defaultPrompt = JSON.parse(JSON.stringify(store.getDefaultConfigs['DOC_CORRECT_RESPONSE_FORMAT']));
    this.EXTRA_QS_RESPFORMAT_SCHEMA = JSON.parse(JSON.parse(JSON.stringify(store.getDefaultConfigs['EXTRA_QS_RESPFORMAT_SCHEMA'])));
    this.DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1 = JSON.parse(JSON.parse(JSON.stringify(store.getDefaultConfigs['DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1'])));
    this.DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2 = JSON.parse(JSON.parse(JSON.stringify(store.getDefaultConfigs['DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2'])));
  },
  emits: ['submit'],
  methods: {
    onSyncToOtherConfigs() {
      this.syncToOtherConfigsLoading = true
      this.$axios.post(`/api/docCorrectConfigPackage/onSyncToOtherConfigs/${this.packageId ?? this.configId}`, this.form).then((res)=>{
        console.log('res');
        this.$message.success("同步修改成功");
      }).finally((res)=>{
        this.syncToOtherConfigsLoading = false;
      })
    },
    removeRange(index) {
      this.form.ranges[this.form.type].splice(index, 1);
    },
    showJsonDialog(schemaKey) {
      // 根据 schemaKey 动态选择要展示的 JSON 数据
      this.currentJson = this[schemaKey];
      this.jsonDialogVisible = true;
    },
    onClose() {
      this.isShow = false;
      this.form = {
        name: "",
        docurl: "",
        img: "",
        score: false,
        scoreColor: 'red',
        scoreFontSize: 10,
        fontSize: 10,
        flagSize: 20,
        flagColor: 'red',
        errorFlagColor: 'red',
        errorFlagSize: 20,
        errorFlag: 'x',
        docType: store.getDefaultDocType,
        nameArea: null,
        scoreArea: null,
        additionalName: '附加',
        prompt: null,
        qsOcrPrompt: null,
        correctFlag: 'a',
        qsOCRPlus: true,
        studentNameRightRotation: 0,
        studentNumberRightRotation: 0,
        qsOcrService: '',
        ranges: {},
        enableLevelOutput: false,
        packageId: null
      };
    },
    show(config, configId, packageId) {
      this.form = Object.assign(this.form, config, {
        prompt: config.prompt || this.defaultPrompt,
        qsOcrPrompt: config.qsOcrPrompt || this.defaultQsOcrPrompt
      })
      this.configId = configId;
      this.packageId = packageId;
      this.isShow = true;
      setTimeout(() => {
        if (this.$refs.rangeItem) {
          this.$refs.rangeItem.show()
        }
      }, 500)
    },
    onSubmit() {
      if (this.form.prompt === this.defaultPrompt) {
        delete this.form.prompt
      }
      // if (this.form.qsOcrPrompt == this.defaultQsOcrPrompt) {
      //     delete this.form.qsOcrPrompt
      // }
      this.$emit('submit', this.form)
      this.onClose()
    }
  }
}
</script>

<style lang="scss" scoped></style>