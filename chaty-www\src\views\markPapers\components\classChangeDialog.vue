<template>
  <el-dialog v-model="isShow" :title="title" width="500" :before-close="onClose" append-to-body>
    <el-form :model="form" label-width="100">
      <div v-if="className && schoolId && school">初始值：{{school.schoolName}} {{className}}</div>
      <el-form-item label="班级：" prop="classId">
        <el-cascader
            v-model="form.classId"
            :props="cascaderProps"
            placeholder="请选择班级"
        ></el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="doSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      className: "",
      isShow: false,
      title: "修改班级",
      submitLoading: false,
      form: {
        classId: "",
        id: ""
      },
      schoolId: "",
      school: null,
      cascaderProps: {
        lazy: true,
        lazyLoad: (node, resolve) => {
          const { level, data } = node;
          if (level === 0) {
            this.$axios.get('/api/school/all').then(response => {
              if (response.data && response.data.length > 0) {
                const transformed = response.data.map(item => ({
                  id: item.schoolId,
                  name: item.schoolName,
                  leaf: false
                }));
                resolve(transformed);
              } else {
                resolve([]);
              }
            }).catch(() => {
              resolve([]);
            });
          } else if (level === 1) {
            this.$axios.get(`/api/class/by-school/${data.id}`).then(response => {
              if (response.data && response.data.length > 0) {
                const transformed = response.data.map(item => ({
                  id: item.classId,
                  name: item.className,
                  leaf: true
                }));
                resolve(transformed);
              } else {
                resolve([]);
              }
            }).catch(() => {
              resolve([]);
            });
          }
        },
        value: 'id',
        label: 'name',
        emitPath: false
      }
    }
  },
  methods: {
    show(formData, schoolId, className) {
      this.form = Object.assign({}, this.form, formData);
      this.schoolId = schoolId;
      this.className = className;
      this.isShow = true;
      this.loadSchool();
    },
    loadSchool() {
      this.$axios.get('/api/school/all').then(response => {
        if (response.data && response.data.length > 0) {
          this.school = response.data.find(item => String(item.schoolId) === String(this.schoolId));
        } else {
          this.$message.error("获取学校信息失败");
        }
      });
    },
    onClose() {
      this.isShow = false;
      this.form = { classId: "", id: "" };
      this.schoolId = "";
      this.className = "";
      this.school = null;
    },
    doSubmit() {
      this.submitLoading = true;
      const param = { classId: this.form.classId , id: this.form.id };
      this.$axios.post('/api/docCorrectFile/update', param).then(() => {
        this.submitLoading = false;
        this.$message.success("修改成功");
        this.onClose();
        this.$emit('submit')
      }).catch(() => {
        this.submitLoading = false;
        this.$message.error("修改失败");
      });
    }
  }
}
</script>

<style scoped>
</style>
