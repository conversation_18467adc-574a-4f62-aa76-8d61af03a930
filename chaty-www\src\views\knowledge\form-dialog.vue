<template>
    <el-dialog :model-value="isShow" :title="$t(title)" width="600" :before-close="() => beforeClose()">
        <el-form :model="form" label-width="120px">
            <el-form-item :label="`${$t('common.name')}：`" prop="name" :style="{ width }">
                <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item :label="`${$t('common.knowledge')}：`" prop="content" :style="{ width }">
                <el-input type="textarea" rows="5" v-model="form.content" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onsubmit">{{ $t('common.confirm') }}</el-button>
                <el-button type="primary" @click="beforeClose">{{ $t('common.cancel') }}</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            form: {},
            width: '500px',
            isShow: false,
            title: '',
            id: ''
        }
    },
    methods: {
        show(data) {
            this.form = {
                name: '',
                content: '',
            }
            this.id = ''
            if (data) {
                this.form = Object.assign(this.form, data)
                this.id = data.id
                this.title = "common.edit"
            } else {
                this.title = "common.add"
            }
            this.isShow = true
        },
        onsubmit() {
            if (this.id) {
                this.$axios.post("/api/knowledge/update", this.form).then(res => {
                    this.$message.success(this.$t('common.updateSuccess'))
                    this.beforeClose();
                })
            } else {
                this.$axios.post("/api/knowledge/add", this.form).then(res => {
                    this.$message.success(this.$t('common.addSuccess'))
                    this.beforeClose();
                })
            }
        },
        beforeClose() {
            this.isShow = false
            this.$emit('onClose')
        }
    }
}
</script>

<style lang="scss" scoped></style>