<template>
  <el-dialog
      v-model="visible"
      width="1000px"
      :close-on-click-modal="false"
      @close="onClose"
  >
    <template #title>
      <el-icon style="color: #faad14; margin-right: 6px; vertical-align: middle;">
        <InfoFilled/>
      </el-icon>
      <span style="vertical-align: middle;">选择模型与试卷</span>
    </template>
    <div style="margin-bottom: 10px;">
      <span>说明：用于快速进行</span>
      <span style="color: #E6A23C; font-weight: bold;">模型测试</span>
      <br/>
      <span
      >效果：【确认】该试卷已纠错，自动基于原卷和标准卷重新通过所选模型重新批改，并自动基于该试卷批改结果纠错，批改完成的试卷已纠错，在【详情】中查看模型批改结果</span
      >
    </div>
    <div class="form-content">
      <el-form>
        <el-form-item label="原试卷名称">
          <el-input
              v-model="form.basePaperName"
              disabled
              style="width: 300px"
          />
        </el-form-item>
        <el-table
            :data="form.tasks"
            border
            style="width: 100%"
            size="small"
            :row-class-name="rowClassName"
        >
          <el-table-column
              prop="newName"
              label="任务名称"
              align="center"
          >
            <template #default="{ row }">
              <el-input
                  v-model="row.newName"
                  placeholder="请输入任务名称"
              />
            </template>
          </el-table-column>
          <el-table-column
              prop="modelValue"
              label="选择模型"
              align="center"
          >
            <template #default="{ row }">
              <el-select
                  v-model="row.modelValue"
                  placeholder="请选择模型"
                  suffix-icon="Search"
                  :value-key="'label'"
                  style="width: 280px"
              >
                <el-option
                    v-for="item in modelOptions"
                    :key="item.modelRequestId"
                    :label="item.label"
                    :value="item"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="标准卷" width="220" align="center">
            <template #default="{ row }">
              <el-switch
                  v-model="row.needNewConfig"
                  inactive-text="沿用"
                  active-text="复制"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template #default="{ $index }">
              <el-button
                  type="text"
                  @click="removeTask($index)"
                  v-if="form.tasks.length > 1"
              >删除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button
            type="primary"
            @click="addTask"
            style="margin-top: 10px;"
        >添加重测任务
        </el-button
        >
      </el-form>
    </div>
    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="onConfirm" :loading="confirmLoading">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import {useUserStore} from "@/store";
import {InfoFilled} from '@element-plus/icons-vue';

const store = useUserStore();
export default {
  name: "ModelPaperDialog",
  data() {
    return {
      visible: false,
      form: {
        id: "",
        basePaperName: "",
        tasks: [],
      },
      modelOptions: [],
      confirmLoading: false,
      datedBaseName: "",
      colorPalette: [
        '#e0ece0',
        '#f1f6f0',
        '#eef5fb',
        '#ECEFF1'
      ],
    };
  },
  methods: {
    getTodayYYMMDD() {
      const now = new Date();
      const yy = String(now.getFullYear()).slice(-2);
      const mm = String(now.getMonth() + 1).padStart(2, "0");
      const dd = String(now.getDate()).padStart(2, "0");
      return yy + mm + dd;
    },
    computeDatedBaseName(rawName) {
      const dateStr = this.getTodayYYMMDD();
      const suffix = rawName.replace(/^\d{6}-?/, "");
      return `${dateStr}-${suffix}`;
    },
    show(defaultValues = {}) {
      this.$refreshConfig().then(() => {
        this.modelOptions = store.getAimodelOptions;
      });
      this.modelOptions = store.getAimodelOptions;
      const baseName = defaultValues.paperName || "";
      this.form.id = defaultValues.id || "";
      this.form.basePaperName = baseName;
      this.datedBaseName = this.computeDatedBaseName(baseName);

      // 初始化第一条任务，版本2
      this.form.tasks = [
        {
          modelValue: defaultValues.modelValue || null,
          needNewConfig: false,
          newName: `${this.datedBaseName}-v2`
        }
      ];
      this.visible = true;
    },
    addTask() {
      const version = this.form.tasks.length + 2;
      this.form.tasks.push({
        modelValue: null,
        needNewConfig: false,
        newName: `${this.datedBaseName}-v${version}`
      });
    },
    removeTask(index) {
      this.form.tasks.splice(index, 1);
    },
    async onConfirm() {
      const {id, tasks} = this.form;
      // 基本校验
      if (!id) {
        this.$message.error("缺少文件ID");
        return;
      }
      for (let i = 0; i < tasks.length; i++) {
        const {modelValue, newName} = tasks[i];
        if (!modelValue || !modelValue.modelRequestId) {
          this.$message.error(`第${i + 1}项，请选择模型`);
          return;
        }
        if (!newName) {
          this.$message.error(`第${i + 1}项，请填写任务名称`);
          return;
        }
      }

      this.form.tasks.sort((a, b) => {
        // 尝试匹配 "-v数字" 结尾
        const ma = a.newName.match(/-v(\d+)$/);
        const mb = b.newName.match(/-v(\d+)$/);

        if (ma && mb) {
          // 都匹配到版本号，则按数字倒序
          const na = parseInt(ma[1], 10);
          const nb = parseInt(mb[1], 10);
          if (na !== nb) {
            return nb - na;    // 数字大的排前面
          }
          // 数字相等时，继续下面的通用比较
        }

        // 回退：普通字符串升序（启用 numeric 比较）
        return a.newName.localeCompare(
            b.newName,
            undefined,
            {numeric: true, sensitivity: 'base'}
        );
      });


      // 弹出全局加载框
      const loading = this.$loading({
        lock: true,
        text: "加载中，请稍候",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      let body = [];
      this.form.tasks.forEach((task) => {
        body.push({
          fileId: this.form.id,
          modelRequestId: task.modelValue.modelRequestId,
          newName: task.newName,
          needNewConfig: task.needNewConfig
        })
      })

      this.$axios.post('/api/docCorrectFile/oneClickRetest', body).then((res) => {
        this.$message.success(`成功发起 ${tasks.length} 个重测任务`);
        this.onClose();
      }).finally(() => {
        loading.close();
      })

    },
    onClose() {
      this.visible = false;
      this.$emit("close");
    },
    rowClassName({rowIndex}) {
      // 每三行一组，四种颜色交替分布
      const group = Math.floor(rowIndex / 3);
      return `group-color-${group % 4}`;
    },
  }
};
</script>

<style scoped>
.form-content {
  font-size: 16px;
  line-height: 1.8;
}

.el-table {
  margin-bottom: 10px;
}

:deep(.el-table__row.group-color-0) {
  background: #e0ece0 !important;
}

:deep(.el-table__row.group-color-1) {
  background: #f1f6f0 !important;
}

:deep(.el-table__row.group-color-2) {
  background: #eef5fb !important;
}

:deep(.el-table__row.group-color-3) {
  background: #ECEFF1 !important;
}
</style>