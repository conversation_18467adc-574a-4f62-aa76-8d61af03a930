export default {
    title: "Miaomiao",
    common: {
        logout: "Logout",
        progress: "Progress",
        error: "Error",
        save: "Save",
        review: "Review",
        ascOrder: "Ascend Order",
        descOrder: "Descend Order",
        singlePage: "Signle Page",
        reviewing: "Reviewing",
        delete: "Delete",
        download: "Download",
        preview: "Preview",
        document: "Document",
        status: "Status",
        unReview: "Unreviewed",
        reviewed: "Reviewed",
        reviewFail: "Review Failed",
        correctRate: "Correct Rate",
        operations: "Operations",
        settings: "Settings",
        fontSize: "Font Size",
        flagSize: "Flag Size",
        type: "Type",
        upload: "Upload",
        library: "Library",
        confirm: "Confirm",
        cancel: "Cancel",
        saveSuccess: "Save Success",
        question: "Question",
        pleaseEnter: "Please Enter {0}",
        answer: "Answer",
        evaluation: "Evaluation",
        defaultEvaluation: "Default Evaluation",
        search: "Search",
        select: "Select",
        emptyData: "Empty Data",
        deleteSuccess: "Delete Success",
        clear: "Clear",
        config: "Config",
        bigModel: "AI Model",
        all: "All",
        knowledge: "Knowledge",
        name: "Name",
        correctAnswer: "Correct Answer",
        isTrue: "Is True",
        solve: "Solve",
        true: "True",
        downloadPdf: "Download PDF",
        creationTime: "Creation Time",
        problem: "Problem",
        tag: "Tag",
        edit: "Edit",
        confirmDelete: "Confirm Delete {0}?",
        updateSuccess: "Update Success",
        add: "Add",
        addSuccess: "Add Success",
        searchByKeyword: "Please Enter Keyword to Search",
    },
    menu: {
        docReview: "Document Correct",
        solveProblem: "Problem Solving",
        coursenote: "Teaching Materials",
        library: "Library",
        chat: "Chat",
        createDoc: "Create Document",
        fastReview: "Fast Correct",
        modelEvaluation: "Model Evaluation",
        markingExamPapers: "Marking exam papers",
        processingSampleRolls: "Processing sample rolls",
        statistics: "statistics",
        expand: "expand",
        collapse: "collapse",
        languageSwitch: "Language Switch",
        stats: "Statistics",
        essayCorrection: "Essay Correction",
        classConfig: 'Class Config',
        comprehensiveCorrect: 'Comprehensive Correct',
        quickfix: 'Quick Fix',
        gradingCorrect: "Grading Correct",
        errorCorrectionStatistics: "Error correction statistics",
        requestLog: 'Request Log',
        ftpMessage: 'Pending test papers'
    },
    docReview: {
        docConfig: "Document Configuration",
        docAdd: "Add Document",
        reviewRes: "Review Result",
        previewRes: "Preview Result",
        docReport: "Document Report",
        pleaseAddDoc: "Please Add Document",
        studentName: "Student Name",
        addQuestion: "Add Question",
        save2Library: "Save to Library",
        uploadFailed: "Upload Document Failed",
        docName: "Document Name",
        updateDocName: "Update Document Name",
        answerArea: "Answer Area",
        evaluationArea: "Evaluation Area",
        flagArea: "Flag Area",
        reviewWithoutDoc: "Correct",
        reviewWithDoc: "Correct (With Document)",
        statsRes: "Statistical Results",
        score: "Score",
    },
    solve: {
        promotPreview: "Promot Preview",
        solve: "Solve",
        downloadPdf: "Download PDF",
        solveProblem: "Solve",
        modelAnswer: "Model Answer",
        finalAnswer: "Final Answer",
        solveFailed: "Solve Failed",
        define: "Define",
        theorem: "Theorem",
        solveProcess: "Solve Process",
        solveSuccess: "Solve Success",
        pleaseCheckConfig: "Please Check Config",
        pleaseSolve: "Please Solve",
        added2Library: "Added to Library",
    },
    coursenote: {
        docReview: "Document Correct",
        studentAnswer: "Student Answer",
        modelList: "AI Model List",
        outputRes: "Output Result",
        parseFailed: "Parse Failed",
        knowledgeName: "Knowledge Name",
        pleaseAddQuestion: "Please Add Question",
    },
    library: {
        questionLibrary: "Question Library",
        homeworkReview: "Homework Correct",
    },
    docReviewRec: {
        docName: 'Document Name',
    },
    chat: {
        systemPromot: "System Message",
        histiryMessageCount: "History Message Count",
        historyMessageCompress: "History Message Compress",
        historyMessageCompressThreshold: "History Message Compress Threshold",
        clearChat: "Clear Chat",
    },
    login: {
        login: "Sign In",
        logoutSuccess: "Logout Success",
        username: "Username",
        password: "Password",
        loginSuccess: "Login Success",
    },
    home: {
        machine: "Trace correction machine for paper-based assignments",
        tech: "Big Model Technology - AI fully automated corrections become a reality",
        reviewRate: "Critique accuracy rate",
        reviewRes: "Correction effect",
        pointError: "Point out specific errors",
        studentCheck: "Student Diagnostics",
        personAnay: "Personalised analysis",
        docReview: "Document Correct",
        demoVideo: "Demo Video",
    },
    printList: {
        default: "Default Printer",
        AIHardwareDemonstration: "AI+ Hardware Demonstration",
        Teaching9223OnlyA4: "Teaching 9 223 Only A4"
    }
}