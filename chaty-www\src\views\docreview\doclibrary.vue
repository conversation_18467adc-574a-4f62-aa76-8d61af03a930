<template>
    <el-dialog v-model="isShow" :title="$t('common.library')" width="30%" :before-close="handleClose">
        <el-row>
            <el-input v-model="filter.docname" :placeholder="$t('common.pleaseEnter', [$t('docReview.docName')])">
                <template #append>
                    <el-button type="primary" @click="loadData">{{ $t('common.search') }}</el-button>
                </template>
            </el-input>
        </el-row>
        <el-table v-loading="loading" :data="docs" style="width: 100%" height="500" :empty-text="$t('common.emptyData')">
            <template v-for="column in columns" :key="column.prop">
                <el-table-column v-bind="column" :label="$t(column.t)">
                    <template v-if="column.prop === 'operations'" #default="scope">
                        <el-button plain @click="deleteDoc(scope.row.id)">{{ $t('common.delete') }}</el-button>
                        <el-button @click="onSelected(scope.row)">{{ $t('common.select') }}</el-button>
                    </template>
                    <template v-else-if="column.prop === 'docname'" #default="scope">
                        <el-link type="primary" :underline="false" :href="$fileserver.fileurl(scope.row.docurl)"
                            target="_blank">{{ scope.row.docname }}</el-link>
                    </template>
                    <template v-else-if="column.type === 'textarea'" #default="scope">
                        <el-scrollbar max-height="200px">
                            <el-text style="white-space: pre-wrap; word-break: break-all;">
                                {{ scope.row[column.prop] }}
                            </el-text>
                        </el-scrollbar>
                    </template>
                </el-table-column>
            </template>
        </el-table>
    </el-dialog>
</template>

<script>
import { CommonProps } from 'element-plus'

export default {
    data() {
        return {
            isShow: false,
            filter: {
                docname: ''
            },
            loading: false,
            docs: [],
            columns: [
                {
                    prop: "docname",
                    label: "试卷名称",
                    align: 'center',
                    t: 'docReview.docName'
                },
                {
                    prop: 'operations',
                    label: '操作',
                    width: 200,
                    align: 'center',
                    t: "common.operations"
                }
            ],
            selected: null,
        }
    },
    methods: {
        show() {
            this.isShow = true
            this.loadData()
        },
        handleClose() {
            this.isShow = false
            this.$emit("onClose", this.selected)
        },
        loadData() {
            this.loading = true
            let form = {}
            if (this.filter.docname && this.filter.docname !== '') {
                form.docname = this.filter.docname
            }
            this.$axios.post("/api/doclibrary/list", form).then(res => {
                this.docs = res.data
            }).finally(() => {
                this.loading = false
            })
        },
        onSelected(doc) {
            this.selected = doc
            this.handleClose()
        },
        deleteDoc(id) {
            this.$axios.get(`/api/doclibrary/delete?id=${id}`).then(res => { 
                this.$message.success(this.$t('common.deleteSuccess'))
                this.loadData()
            })
        }
    }
}
</script>

<style lang="scss" scoped></style>