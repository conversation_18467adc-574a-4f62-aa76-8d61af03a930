<template>
  <el-dialog
      v-model="isShow"
      :title="title"
      width="800px"
      :before-close="onClose"
  >
    <el-form
        :model="formData"
        :rules="rules"
        label-position="top"
        ref="displacementForm"
    >
      <!-- 交换 -->
      <el-form-item label="是否交换">
        <el-checkbox v-model="formData.isReversed">是否交换</el-checkbox>
      </el-form-item>

      <!-- 旋转 -->
      <el-form-item label="是否旋转">
        <el-checkbox v-model="formData.isRotate">是否旋转</el-checkbox>
      </el-form-item>

      <!-- 等级输出 -->
      <el-form-item label="是否启用等级输出">
        <el-checkbox v-model="formData.enableLevelOutput" @change="onSwitchChange">是否启用等级输出</el-checkbox>
      </el-form-item>

      <!-- 小题分数 -->
      <el-form-item label="是否展示小题分数">
        <el-checkbox v-model="formData.showQsScore" @change="onSwitchChange">是否展示小题分数</el-checkbox>
        <div v-if="formData.showQsScore" style="margin-left: 20px;display:inline-block;">
          仅展示错误小题分数：
          <el-checkbox v-model="formData.onlyShowWrongQsScore">仅展示错误小题分数</el-checkbox>
        </div>
      </el-form-item>

      <!-- 需要旋转页面列表 -->
      <el-form-item label="选择需要旋转180°的页面">
        * 请在每份试卷的 更多按钮 下选择 旋转180度
<!--        <el-row :gutter="10" align="middle">-->
<!--          <el-col :span="8">-->
<!--            <el-select-->
<!--                v-model="selectedPage.orientation"-->
<!--                placeholder="选择面"-->
<!--            >-->
<!--              <el-option-->
<!--                  v-for="i in taskLength"-->
<!--                  :key="i"-->
<!--                  :label="`面${i}`"-->
<!--                  :value="i"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-col>-->
<!--          <el-col :span="8">-->
<!--            <el-input-->
<!--                v-model="selectedPage.pageNumber"-->
<!--                placeholder="输入页码"-->
<!--                @keyup.enter="addRotationPage"-->
<!--            />-->
<!--          </el-col>-->
<!--          <el-col :span="6">-->
<!--            <el-button type="primary" @click="addRotationPage">-->
<!--              添加-->
<!--            </el-button>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-table-->
<!--            :data="formData.needRotationList"-->
<!--            border-->
<!--            style="margin-top: 10px"-->
<!--        >-->
<!--          <el-table-column prop="orientation" label="面"  />-->
<!--          <el-table-column prop="pageNumber" label="页码" />-->
<!--          <el-table-column label="操作">-->
<!--            <template #default="{ $index }">-->
<!--              <el-button-->
<!--                  type="danger"-->
<!--                  size="small"-->
<!--                  @click="removeRotationPage($index)"-->
<!--              >-->
<!--                删除-->
<!--              </el-button>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->
      </el-form-item>

      <!-- 需要交换页面列表 -->
      <el-form-item label="选择需要交换的页面">
        * 请在每份试卷的 更多按钮 下选择 对页交换
<!--        <el-row :gutter="10" align="middle">-->
<!--          <el-col :span="5">-->
<!--            <el-select-->
<!--                v-model="selectedSwapPage.fromOrientation"-->
<!--                placeholder="被交换页 面"-->
<!--            >-->
<!--              <el-option-->
<!--                  v-for="i in taskLength"-->
<!--                  :key="i"-->
<!--                  :label="`面${i}`"-->
<!--                  :value="i"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-col>-->
<!--          <el-col :span="5">-->
<!--            <el-input-->
<!--                v-model="selectedSwapPage.fromPage"-->
<!--                placeholder="被交换页 页码"-->
<!--                @keyup.enter="addSwapPage"-->
<!--            />-->
<!--          </el-col>-->
<!--          <el-col :span="5">-->
<!--            <el-select-->
<!--                v-model="selectedSwapPage.toOrientation"-->
<!--                placeholder="目标页 面"-->
<!--            >-->
<!--              <el-option-->
<!--                  v-for="i in taskLength"-->
<!--                  :key="i"-->
<!--                  :label="`面${i}`"-->
<!--                  :value="i"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-col>-->
<!--          <el-col :span="5">-->
<!--            <el-input-->
<!--                v-model="selectedSwapPage.toPage"-->
<!--                placeholder="目标页 页码"-->
<!--                @keyup.enter="addSwapPage"-->
<!--            />-->
<!--          </el-col>-->
<!--          <el-col :span="4">-->
<!--            <el-button type="primary" @click="addSwapPage">-->
<!--              添加-->
<!--            </el-button>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-table-->
<!--            :data="formData.swapPagesList"-->
<!--            border-->
<!--            style="margin-top: 10px"-->
<!--        >-->
<!--          <el-table-column-->
<!--              prop="fromOrientation"-->
<!--              label="被交换页 面"-->
<!--          />-->
<!--          <el-table-column prop="fromPage" label="被交换页 页码"  />-->
<!--          <el-table-column-->
<!--              prop="toOrientation"-->
<!--              label="目标页 面"-->
<!--          />-->
<!--          <el-table-column prop="toPage" label="目标页 页码" />-->
<!--          <el-table-column label="操作" >-->
<!--            <template #default="{ $index }">-->
<!--              <el-button-->
<!--                  type="danger"-->
<!--                  size="small"-->
<!--                  @click="removeSwapPage($index)"-->
<!--              >-->
<!--                删除-->
<!--              </el-button>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button
          type="primary"
          :loading="submitting"
          @click="onSubmit"
      >
        确认
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "DisplacementDialog",
  props: {
    submitting: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: "设置位移参数",
      isShow: false,
      rules: {}, // 如需校验可在此添加规则
      formData: {
        isPreview: false,
        enableLevelOutput: false,
        ranges: {},
        isReversed: false,
        isRotate: false,
        showQsScore: false,
        onlyShowWrongQsScore: false,
        needRotationList: [],
        swapPagesList: [],
      },
      selectedPage: {
        orientation: "",
        pageNumber: "",
      },
      selectedSwapPage: {
        fromOrientation: "",
        fromPage: "",
        toOrientation: "",
        toPage: "",
      },
      taskLength: 0,
    };
  },
  methods: {
    onSwitchChange(val) {
      // 开关变化回调
    },
    show(config, id, taskLength = 0) {
      // 初始化表单数据
      this.formData.isPreview = config.isPreview;
      this.formData.enableLevelOutput = config.enableLevelOutput;
      this.formData.isReversed = config.isReversed;
      this.formData.isRotate = config.isRotate;
      this.formData.showQsScore = config.showQsScore || false;
      this.formData.onlyShowWrongQsScore = config.onlyShowWrongQsScore || false;
      this.formData.ranges = config.ranges || {};
      this.formData.needRotationList = config.needRotationList || [];
      this.formData.swapPagesList = config.swapPagesList || [];
      this.taskLength = taskLength;
      this.isShow = true;
    },
    onClose() {
      this.$refs.displacementForm.resetFields();
      this.isShow = false;
      this.formData = {
        isPreview: false,
        enableLevelOutput: false,
        ranges: {},
        isReversed: false,
        isRotate: false,
        showQsScore: false,
        onlyShowWrongQsScore: false,
        needRotationList: [],
        swapPagesList: [],
      };
      this.selectedPage = { orientation: "", pageNumber: "" };
      this.selectedSwapPage = {
        fromOrientation: "",
        fromPage: "",
        toOrientation: "",
        toPage: "",
      };
    },
    addRotationPage() {
      const { orientation, pageNumber } = this.selectedPage;
      if (orientation && pageNumber) {
        this.formData.needRotationList.push({ orientation, pageNumber });
        this.selectedPage = { orientation: "", pageNumber: "" };
      }
    },
    removeRotationPage(index) {
      this.formData.needRotationList.splice(index, 1);
    },
    addSwapPage() {
      const {
        fromOrientation,
        fromPage,
        toOrientation,
        toPage,
      } = this.selectedSwapPage;
      if (
          fromOrientation &&
          fromPage &&
          toOrientation &&
          toPage
      ) {
        this.formData.swapPagesList.push({
          fromOrientation,
          fromPage,
          toOrientation,
          toPage,
        });
        this.selectedSwapPage = {
          fromOrientation: "",
          fromPage: "",
          toOrientation: "",
          toPage: "",
        };
      }
    },
    removeSwapPage(index) {
      this.formData.swapPagesList.splice(index, 1);
    },
    onSubmit() {
      this.$refs.displacementForm.validate((valid) => {
        if (valid) {
          const payload = JSON.parse(JSON.stringify(this.formData));
          this.$emit("submit", payload);
          this.onClose();
        } else {
          this.$message.error("请正确填写所有字段");
        }
      });
    },
  },
};
</script>

<style scoped>
/* 如需自定义样式，可在此添加 */
</style>
