<template>
  <el-dialog v-model="isShow" title="样式四-试卷统计设置" width="1000" :before-close="beforeClose">
    <el-form :model="form" label-width="100px">
      <!-- 试卷搜索选择 -->
      <el-form-item label="选择试卷">
        <el-select
            v-model="selectedPaperId"
            placeholder="请输入试卷名称 搜索添加"
            clearable
            filterable
            remote
            :remote-method="loadPaperOptions"
            :loading="loadingPaperOption"
            style="width: 100%; margin-bottom: 10px"
        >
          <el-option
              v-for="item in paperOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          />
        </el-select>
        <el-select
            v-model="selectedPackageId"
            placeholder="或者输入试卷标准卷名称 搜索添加"
            clearable
            filterable
            remote
            :remote-method="loadPackageOptions"
            :loading="loadingPackageOption"
            style="width: 100%"
        >
          <el-option
              v-for="item in packageOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          />
        </el-select>
      </el-form-item>


      <!-- 已选试卷列表 -->
      <el-form-item label="已选试卷">
        <el-table :data="form.fileDetails" border style="width: 100%">
          <el-table-column prop="className" label="班级名称" width="250">
            <template #default="{ row }">
              <el-input v-model="row.className" placeholder="请输入班级名称"/>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="试卷名称" width="250"/>
          <!--          <el-table-column prop="fileId" label="id"/>-->
          <el-table-column prop="teacherName" label="教师名称">
            <template #default="{ row }">
              <el-input v-model="row.teacherName" placeholder="请输入教师名称"/>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ $index }">
              <el-button type="danger" @click="removePaper($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <!-- 合格分和优秀分 -->
      <el-row type="flex" align="middle" style="height: 80px">
        <el-form-item label="合格分">
          <el-input-number v-model="form.hegeScore" :min="0" :max="100"/>
          <el-text style="margin-left: 10px">总分60%</el-text>
        </el-form-item>
        <el-form-item label="优秀分">
          <el-input-number v-model="form.youxiuScore" :min="0" :max="100"/>
          <el-text style="margin-left: 10px">总分85%</el-text>
        </el-form-item>
        <el-form-item label="后X名学生">
          <el-input-number v-model="form.studentNumberLimit" :min="0" placeholder="后X学生"/>
        </el-form-item>
      </el-row>


      <el-row type="flex" align="middle" style="height: 50px">
        <el-form-item label="学科名称">
          <el-input v-model="form.subject" placeholder="请输入学科名称 如英语"/>
        </el-form-item>
        <el-form-item label="年级">
          <el-input v-model="form.gradeName" placeholder="请输入年级 如六年级"/>
        </el-form-item>
      </el-row>


      <el-row
          type="flex"
          align="middle"
          style="height: 50px;margin-left: 28px"
      >
        <el-form-item label="按照学号排序">
          <el-switch v-model="form.orderByStudentNumber" active-text="排序" inactive-text="不排序"></el-switch>
        </el-form-item>
      </el-row>

      <el-row
          type="flex"
          align="middle"
          style="height: 80px;margin-left: 28px"
      >
        <el-form-item label="下载样式三">
          <el-switch v-model="downStyle3" active-text="下载" inactive-text="不下载"></el-switch>
        </el-form-item>
        <el-form-item label="下载样式四">
          <el-switch v-model="downStyle4" active-text="下载" inactive-text="不下载"></el-switch>
        </el-form-item>
        <el-form-item label="下载样式五">
          <el-switch v-model="downStyle5" active-text="下载" inactive-text="不下载"></el-switch>
        </el-form-item>
        <el-form-item label="下载样式六">
          <el-switch v-model="downStyle6" active-text="下载" inactive-text="不下载"></el-switch>
        </el-form-item>
      </el-row>


      <!-- 操作按钮 -->
      <el-form-item>
        <div style="text-align: right">
          <el-button type="primary" @click="onSubmit">提交</el-button>
          <el-button type="danger" @click="beforeClose">取消</el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      isShow: false,
      selectedPaperId: null,
      selectedPackageId: null,
      paperOptions: [],
      packageOptions: [],
      loadingPaperOption: false,
      loadingPackageOption: false,
      form: {
        fileDetails: [],
        youxiuScore: null,
        hegeScore: null,
        subject: "英语",
        gradeName: "六年级",
        studentNumberLimit: 30,
        ranges: [],
        orderByStudentNumber: true,
        realClassNum: 0
      },
      sortTimeout: null,
      downStyle3: false,
      downStyle4: true,
      downStyle5: false,
      downStyle6: false,
      configIds: null
    };
  },
  methods: {
    // 加载试卷选项
    loadPaperOptions(name) {
      if (!name || name === "") {
        this.paperOptions = [];
        return;
      }
      this.loadingPaperOption = true;
      this.loadPapers(name)
          .then((res) => {
            this.paperOptions = res;
          })
          .finally(() => {
            this.loadingPaperOption = false;
          });
    },
    // 加载试卷包选项
    loadPackageOptions(name) {
      if (!name || name === "") {
        this.packageOptions = [];
        return;
      }
      this.loadingPackageOption = true;
      this.loadPackages(name)
          .then((res) => {
            this.packageOptions = res;
          })
          .finally(() => {
            this.loadingPackageOption = false;
          });
    },
    // 调用接口加载试卷
    loadPapers(name) {
      return this.$axios
          .post("/api/docCorrectFile/page", {
            name,
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            return res.data.records;
          });
    },
    // 调用接口加载试卷包
    loadPackages(name) {
      return this.$axios
          .post("/api/docCorrectConfigPackage/page", {
            name,
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            return res.data.records;
          });
    },
    addByPackage(name) {
      this.$axios
          .post("/api/docCorrectConfigPackage/page", {
            name,
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            if (res.data.records && res.data.records.length === 1) {
              this.selectedPackageId = res.data.records[0].id;
              this.onPackageSelect();
            }
          });
    },
    // 处理试卷选择
    onPaperSelect() {
      const selectedPaper = this.paperOptions.find(
          (paper) => paper.id === this.selectedPaperId
      );
      if (selectedPaper) {
        const exists = this.form.fileDetails.some(
            item => item.fileId === selectedPaper.id
        );
        if (!exists) {
          this.form.fileDetails.push({
            fileId: selectedPaper.id,
            name: selectedPaper.name,
            className: this.getClassNameByFileName(selectedPaper.name),
            teacherName: "",  // 新增教师名称字段
            classId: selectedPaper.classId !== undefined ? selectedPaper.classId : null // 新增 classId 字段
          });
        }

        this.selectedPaperId = null;
      }
    },
    // 处理试卷包选择
    onPackageSelect() {
      if (!this.selectedPackageId) return;
      this.$axios
          .get(`/api/docCorrectFile/getDocCorrectFileListByConfigPackageId?id=${this.selectedPackageId}`)
          .then((res) => {
            const fileList = res.data;
            if (fileList && fileList.length) {
              fileList.forEach((file) => {
                const exists = this.form.fileDetails.some(
                    item => item.fileId === file.id
                );
                if (!exists) {
                  this.form.fileDetails.push({
                    fileId: file.id,
                    className: this.getClassNameByFileName(file.name),
                    name: file.name,
                    teacherName: "",  // 新增教师名称字段
                    classId: file.classId !== undefined ? file.classId : null // 新增 classId 字段
                  });
                }

              });
            }
            this.selectedPackageId = null;
          });
    },
    /**
     * 根据文件名提取班级名称
     * @param {string} name 文件名，格式如 "250609_文三_英语_301" 或 "250610-文三-英语-303"
     * @returns {string} 如果末尾是数字，则返回"科目+班号"，否则返回原始 name
     */
    getClassNameByFileName(name) {
      if (typeof name !== 'string') {
        return '';
      }

      // 按下划线或连字符拆分
      const parts = name.split(/[_-]/);
      if (parts.length < 2) {
        // 无法拆出至少两个部分，直接返回原始值
        return name;
      }

      const last = parts[parts.length - 1];
      // 判断最后一段是否全为数字
      if (/^\d+$/.test(last)) {
        return last;
      }

      return name;
    },
    removePaper(index) {
      this.form.fileDetails.splice(index, 1);
    },
    onSubmit() {
      if (!this.form.fileDetails.length) {
        this.$message.error("请至少选择一个试卷！");
        return;
      }
      if (!this.form.hegeScore || !this.form.youxiuScore) {
        this.$message.error("请输入合格分和优秀分！");
        return;
      }
      if (!this.form.subject) {
        this.$message.error("请输入学科名称！");
        return;
      }
      if (!this.form.gradeName) {
        this.$message.error("请输入年级！");
        return;
      }
      if (!this.form.studentNumberLimit) {
        this.$message.error("请输入学生数量界限！");
        return;
      }
      // 检查是否有重命名
      let names = {};
      for (let i = 0; i < this.form.fileDetails.length; i++) {
        let item = this.form.fileDetails[i];
        if (item.className in names || names[item.className]) {
          this.$message.error("班级名称重复，请重新输入！");
          return;
        }
        names[item.className] = true;
      }
      if (this.downStyle4) {
        this.$axios
            .post("/api/docCorrectFile/excelStyle4", this.form)
            .then((res) => {
              // 提交成功后将结果通过事件传递出去
              let data = {
                url: res.data.url,
                sheetNames: this.form.fileDetails.map(item => item.className),
                orderByStudentNumber: this.form.orderByStudentNumber,
                realClassNum: this.form.realClassNum
              };
              this.$emit('submitStyle4', data);
            })
            .finally(() => {
              this.beforeClose();
            });
      }

      if (this.downStyle3) {
        this.$axios
            .post("/api/docCorrectFile/classStatisticData", this.form)
            .then((res) => {
              let data = {
                sheetData: res.data.sheetData,
                sheetNames: this.form.fileDetails.map(item => item.className),
                url: res.data.url,
                orderByStudentNumber: this.form.orderByStudentNumber,
                gradeName: this.form.gradeName,
                subjectName: this.form.subjectName
              }
              this.$emit('submitStyle3', data)
            })
            .finally(() => {
              this.beforeClose();
            });
      }

      if (this.downStyle5) {
        this.$axios
            .post("/api/docCorrectFile/excelStyle5", this.form)
            .then((res) => {
              // 提交成功后将结果通过事件传递出去
              let data = {
                url: res.data.url,
                sheetNames: this.form.fileDetails.map(item => item.className),
                orderByStudentNumber: this.form.orderByStudentNumber,
                realClassNum: this.form.realClassNum
              };
              this.$emit('submitStyle5', data);
            })
            .finally(() => {
              this.beforeClose();
            });
      }

      if (this.downStyle6) {
        this.$axios
            .post("/api/docCorrectFile/excelStyle6", this.form)
            .then((res) => {
              // 提交成功后将结果通过事件传递出去
              let data = {
                url: res.data.url,
                sheetNames: this.form.fileDetails.map(item => item.className),
                orderByStudentNumber: this.form.orderByStudentNumber,
                realClassNum: this.form.realClassNum
              };
              this.$emit('submitStyle6', data);
            })
            .finally(() => {
              this.beforeClose();
            });
      }

      if (!this.downStyle3 && !this.downStyle4 && !this.downStyle5 && !this.downStyle6) {
        this.beforeClose();
      }
    },
    // 关闭弹窗
    beforeClose() {
      this.isShow = false;
      this.form = {
        fileDetails: [],
        youxiuScore: null,
        hegeScore: null,
        subject: "英语",
        gradeName: "六年级",
        studentNumberLimit: 30,
        ranges: [],
        orderByStudentNumber: true,
        realClassNum: 0
      };
      this.selectedPaperId = null;
      this.selectedPackageId = null;
      this.paperOptions = [];
      this.packageOptions = [];
      this.downStyle3 = false
      this.downStyle4 = true
      this.downStyle5 = false
      this.downStyle6 = false
    },
    // 显示弹窗
    show(data, fileId, ranges, configIds, downStyle3 = false, realClassNum = 0) {
      let totalScore = data.scoreTypesMaxScore.pop();
      this.form.hegeScore = (totalScore * 0.6).toFixed(1);
      this.form.youxiuScore = (totalScore * 0.85).toFixed(1);
      this.form.ranges = ranges;
      this.form.realClassNum = realClassNum;
      this.isShow = true;
      this.downStyle3 = downStyle3;

      this.configIds = configIds;
      this.addByPackageIds();
    },
    addByPackageIds() {
      this.$axios
          .post("/api/docCorrectConfigPackage/page", {
            config: JSON.stringify(this.configIds),
            page: {
              pageNumber: 1,
              pageSize: -1,
            },
          })
          .then((res) => {
            if (res.data.records.length === 0) {
              this.$message.error(`没有找到${this.initPaper.packageName}配置卷`);
            } else if (res.data.records.length === 1) {
              this.selectedPackageId = res.data.records[0].id;
            } else {
              this.$message.warning(`找到多个相似名称：${this.initPaper.packageName}配置卷，采用第一个`);
              this.selectedPackageId = res.data.records[0].id;
            }
          });
    },
    addByFiledId(id) {
      this.$axios
          .get("/api/docCorrectFile/getById?id=" + id)
          .then((res) => {
            this.form.fileDetails.push({
              fileId: res.data.id,
              name: res.data.name,
              className: this.getClassNameByFileName(res.data.name),
              teacherName: "",  // 新增教师名称字段
              classId: res.data.classId !== undefined ? res.data.classId : null // 新增 classId 字段
            });
          });
    },
  },
  watch: {
    'form.fileDetails': {
      handler(newVal) {
        if (this.sortTimeout) {
          clearTimeout(this.sortTimeout);
        }
        // 判断当前数组是否已经有序，如果已经有序则不执行排序操作
        this.sortTimeout = setTimeout(() => {
          let isSorted = true;
          for (let i = 1; i < newVal.length; i++) {
            if (newVal[i - 1].className > newVal[i].className) {
              isSorted = false;
              break;
            }
          }
          if (!isSorted) {
            this.form.fileDetails.sort((a, b) => a.className.localeCompare(b.className));
            this.$message.success("排序成功");
          }
        }, 500);
      },
      deep: true
    },
    // 监听试卷选择
    selectedPaperId(newVal) {
      if (newVal) {
        this.onPaperSelect();
      }
    },
    // 监听试卷包选择
    selectedPackageId(newVal) {
      if (newVal) {
        this.onPackageSelect();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dialog .el-card {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}

.text-center {
  text-align: center;
}
</style>
