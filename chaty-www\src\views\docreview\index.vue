<template>
    <div class="main-wrapper">
        <div class="header-bar">
            <div class="header-title">
                {{ this.docname || $t('common.document') }}
                <el-button type="warning" text icon="Edit" style="padding: 0;" @click="renameTitle(this.docname || $t('common.document'))"></el-button>
                <el-switch
                    style="margin-left: 20px;"
                    v-model="isPreview"
                    @change="onPreviewChange"
                    :active-text="$t('common.preview')">
                </el-switch>
                <el-button style="margin-left: 5px;" icon="Refresh" text v-show="isPreview" @click="refreshPreview"></el-button>
    
                <el-switch
                    style="margin-left: 20px;"
                    :active-text="$t('docReview.score')"
                    v-model="isScore">
                </el-switch>
            </div>
            <div class="header-action">
                <el-dropdown class="header-action-setting" trigger="click" placement="bottom-start" :hide-on-click="false">
                    <span class="el-dropdown-link">
                        <el-button type="warning" icon="Tools">{{ $t('common.settings') }}</el-button>
                    </span>
                    <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item>
                            <span style="width: 100px; float: left;">{{ `${$t('common.fontSize')}：` }}</span>
                            <el-input-number class="content" v-model="fontSize"></el-input-number>
                        </el-dropdown-item>
                        <el-dropdown-item>
                            <span style="width: 100px; float: left;">{{ `${$t('common.flagSize')}：` }}</span>
                            <el-input-number class="content" v-model="signSize"></el-input-number>
                        </el-dropdown-item>
                        <el-dropdown-item>
                            <span style="width: 100px; float: left;">{{ `${$t('common.type')}：` }}</span>
                            <el-select style="width: 150px;" v-model="docType">
                                <el-option label="A4" value="A4"></el-option>
                                <el-option label="A3" value="A3"></el-option>
                            </el-select>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-button v-if="docIdArea && cropData.type !== 'docId'" type="success" icon="check"
                    @click="crop('docId', docIdArea)">{{ $t('docReview.studentName') }}</el-button>
                <el-button v-else-if="cropData.type !== 'docId'" plain icon="Crop"
                    @click="crop('docId', docIdArea)">{{ $t('docReview.studentName') }}</el-button>
                <el-button v-else-if="cropData.type === 'docId'" type="warning" icon="check"
                    @click="doCrop">{{ $t('docReview.studentName') }}</el-button>

                <template v-if="isScore">
                    <el-button v-if="scoreArea && cropData.type !== 'score'" type="success" icon="check"
                        @click="crop('score', scoreArea)">{{ $t('docReview.score') }}</el-button>
                    <el-button v-else-if="cropData.type !== 'score'" plain icon="Crop"
                        @click="crop('score', scoreArea)">{{ $t('docReview.score') }}</el-button>
                    <el-button v-else-if="cropData.type === 'score'" type="warning" icon="check"
                        @click="doCrop">{{ $t('docReview.score') }}</el-button>
                </template>

                <el-upload class="header-action-upload" :action="$fileserver.pdf2imgUrl" accept=".pdf" :with-credentials="true"
                    :show-file-list="false" :on-success="uploadDoc">
                    <el-button v-loading="uploadLoading" plain icon="Plus">{{ $t('common.upload') }}</el-button>
                </el-upload>
                <el-button plain @click="addQuestion" icon="Plus">{{ $t('docReview.addQuestion') }}</el-button>
                <el-button plain @click="save2Library">{{ $t('docReview.save2Library') }}</el-button>
                <el-button plain @click="showDoclibrary">{{ $t('common.library') }}</el-button>
                <el-button type="primary" @click="confim">{{ $t('common.confirm') }}</el-button>
            </div>
        </div>

        <div class="content-wrapper">
            <!-- cropper -->
            <div class="content-cropper-wrapper">
                <div class="content-cropper">
                    <img v-if="docurl" class="img" id="img" crossorigin="anonymous">
                    <el-upload v-show="!docurl || docurl === ''" class="content-cropper-empty" :with-credentials="true"
                        :action="$fileserver.pdf2imgUrl" accept=".pdf" :show-file-list="false" :on-success="uploadDoc">
                        <el-icon size="30">
                            <Plus />
                        </el-icon>
                    </el-upload>
                </div>
            </div>
            <div class="content-board">
                <el-scrollbar height="100%">
                    <div class="content-questions">
                        <question-card :ref="`question_${index}`" v-for="(question, index) in questions" :key="index"
                            :data="questions[index]" :index="index" @update:data="(data) => questions[index] = data"
                            @doCrop="doCrop" @doDelete="() => deleteQuestion(index)" :isScore="isScore"
                            @crop="(type, area, options) => crop(type, area, index, options)"></question-card>
                    </div>
                </el-scrollbar>
            </div>
        </div>

        <doclibrary ref="doclibrary" @onClose="refreshDoc"></doclibrary>

        <canvas ref="uploadCanvas" style="display: none;"></canvas>
    </div>
</template>

<script>
import Cropper from 'cropperjs'
import QuestionCard from './components/question-card.vue'
import { ElMessageBox } from 'element-plus'
import Doclibrary from './doclibrary.vue'
import {useUserStore} from "@/store";

const store = useUserStore();
export default {
    components: {
        QuestionCard,
        Doclibrary
    },
    data() {
        return {
            cropper: null,
            docurl: '',
            docpath: '',
            questions: [
                { name: '', question: '', correctAnswer: '', score: 0, answerArea: null, reviewArea: null, checkArea: null, questionInfo: '', defaultReview: '', crop: '', isDefaultReview: false },
            ],
            cropData: {
                type: '', // identify answer review
                qsIdx: -1, // index of question
            },
            uploadLoading: false,
            docIdArea: null,
            id: null,
            docname: null,
            fontSize: 10,
            signSize: 40,
            docType: store.getDefaultDocType,
            isPreview: false,
            imageUrl: '',
            isScore: false,
            scoreArea: null,
        }
    },
    mounted() {
    },
    beforeRouteLeave(to, from) {
        if (to.name === 'docreview-list') {
            to.meta.docContext = {
                questions: this.questions,
                docIdArea: this.docIdArea,
                docname: this.docname,
                fontSize: this.fontSize,
                signSize: this.signSize,
                docType: this.docType,
                docurl: this.docurl,
                libraryId: this.id,
                isScore: this.isScore,
                scoreArea: this.scoreArea
            }
        }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            if (vm.cropper) {
                vm.cropper.setDragMode('move')
            }
        })
    },
    watch: {
        isScore(val) {
            if (!val) {
                if (this.cropData.type === 'score') {
                    this.unCrop()
                }
            }
        }
    },
    methods: {
        crop(type, area, index, options) {
            if (!this.cropper) {
                this.$message.warning(this.$t('docReview.pleaseAddDoc'));
                return
            }

            this.unCrop() // 取消之前的裁剪
            this.cropData.type = type
            this.cropData.qsIdx = (index || index === 0) ? index : -1
            this.cropData.options = options
            if (index >= 0) {
                this.questions[index].crop = type
            }
            this.cropper.setDragMode('crop')
            // this.cropper.enable()
            if (area) {
                this.cropper.crop()
                this.cropper.setData(area)
            }
        },
        unCrop() {
            // 取消之前的裁剪
            const { type, qsIdx } = this.cropData
            if (type && type !== '') {
                if (type === 'answer' || type === 'review' || type === 'check' || type === 'question' || type === 'correctAnswer') {
                    this.questions[qsIdx].crop = ''
                }
            }
            this.cropData = {
                type: '',
                qsIdx: -1,
            }
            this.cropper.clear()
            this.cropper.setDragMode('move')
            // this.cropper.reset()
            // this.cropper.disable()
        },
        confim() {
            this.$router.push({ name: 'docreview-list' })
        },
        addQuestion() {
            this.questions.push({ name: '', question: '', correctAnswer: '', score: 0, answerArea: null, reviewArea: null, checkArea: null, defaultReview: '', crop: '', isDefaultReview: false, })
        },
        uploadDoc(response) {
            if (response.code !== 200) {
                this.$message.error(this.$t('docReview.uploadFailed'))
                return
            }

            this.docpath = response.data.docpath
            this.docurl = this.$fileserver.fileurl(response.data.url)
            this.id = null
            this.docname = null
            this.isPreview = false
            this.resetPreviewImg(true).then(() => {
                this.refreshCropper()
            })
        },
        refreshCropper(next) {
            if (this.cropper) {
                this.cropper.destroy()
            }
            let _this = this
            this.$nextTick(() => {
                this.cropper = new Cropper(document.getElementById('img'), {
                    autoCrop: false,
                    dragMode: 'move',
                    movable: true,
                    zoomable: true,
                    background: false,
                    toggleDragModeOnDblclick: false,
                    ready() {
                        _this.cropper.clear()
                        _this.cropper.setDragMode('move')
                        if (next) {
                            next()
                        }
                    }
                })
            });
        },
        doCrop() {
            const { type, qsIdx, options } = this.cropData
            let areaData = this.cropper.getData(true)
            let chain = Promise.resolve()
            if (type === 'answer') {
                this.questions[qsIdx].answerArea = areaData
            }
            if (type === 'review') {
                this.questions[qsIdx].reviewArea = areaData
            }
            if (type === 'check') {
                this.questions[qsIdx].checkArea = areaData
            }
            if (type === 'question') {
                this.$refs[`question_${qsIdx}`][0].updateOptions('loadingQuestion', true)
                let ocr = options.ocr
                this.$nextTick(() => {
                    this.cropAndOcr(areaData, ocr).then(res => {
                        console.log("set question")
                        this.questions[qsIdx].question = res
                    }).finally(() => {
                        this.$refs[`question_${qsIdx}`][0].updateOptions('loadingQuestion', false)
                    })
                })
            }
            if (type === 'correctAnswer') {
                this.$refs[`question_${qsIdx}`][0].updateOptions('loadingCorrectAnswer', true)
                let ocr = options.ocr
                this.$nextTick(() => {
                    this.cropAndOcr(areaData, ocr).then(res => {
                        console.log("set answer")
                        this.questions[qsIdx].correctAnswer = res
                    }).finally(() => {
                        this.$refs[`question_${qsIdx}`][0].updateOptions('loadingCorrectAnswer', false)
                    })
                })
            }
            if (type === 'docId') {
                this.docIdArea = areaData
            }
            if (type === 'score') {
                this.scoreArea = areaData
            }
            chain.then(() => {
                console.log("uncrop")
                this.unCrop()
            })
        },
        deleteQuestion(index) {
            if (this.cropData.qsIdx === index) {
                this.unCrop()
            }
            this.questions.splice(index, 1)
        },
        cropAndOcr(area, service="mathPixOCRService") {
            return this.$axios.post(`/api/docreview/ocrCropArea?docurl=${this.docpath}&service=${service}`, area).then(res => {
                return res.data.text
            })
        },
        save2Library() {
            if (!this.docurl) {
                this.$message.warning(this.$t('docReview.pleaseAddDoc'))
                return
            }
            let chain;
            if (this.docname) {
                chain = Promise.resolve(this.docname)
            } else {
                chain = ElMessageBox.prompt('', this.$t('docReview.docName'), {
                    confirmButtonText: this.$t('common.confirm'),
                    cancelButtonText: this.$t('common.cancel'),
                })
            }
            chain.then(res => {
                this.docname = this.docname || res.value
                let form = {
                    id: this.id,
                    docurl: this.docurl,
                    docpath: this.docpath,
                    docname: this.docname,
                    questions: JSON.stringify(this.questions),
                    extConf: JSON.stringify({
                        fontSize: this.fontSize,
                        signSize: this.signSize,
                        docType: this.docType,
                        isScore: this.isScore,
                        scoreArea: this.scoreArea
                    })
                }
                if (this.docIdArea) {
                    form.idArea = JSON.stringify(this.docIdArea)
                }
                let chain1;
                if (this.id) {
                    chain1 = this.$axios.post("/api/doclibrary/update", form)
                } else {
                    chain1 = this.$axios.post("/api/doclibrary/add", form).then(res => {
                        this.id = res.data.id
                    })
                }
                chain1.then(() => {
                    this.$message.success(this.$t('common.saveSuccess'))
                })
            })
        },
        showDoclibrary() {
            this.$refs.doclibrary.show()
        },
        refreshDoc(selected) {
            if (selected) {
                const { id, docname, docurl, docpath, idArea, questions, extConf } = selected
                this.id = id
                this.docname = docname
                this.docurl = docurl
                this.docpath = docpath
                this.docIdArea = (idArea && idArea !== '') ? JSON.parse(idArea) : null
                this.questions = JSON.parse(questions).map(question => {
                    return Object.assign(question, {
                        crop: ''
                    })
                })
                if (extConf && extConf !== '') {
                    let extConfObj = JSON.parse(extConf)
                    this.fontSize = extConfObj.fontSize || 10
                    this.signSize = extConfObj.signSize || 40
                    this.docType = extConfObj.docType || 'A4'
                    this.isScore = extConfObj.isScore || false
                    this.scoreArea = extConfObj.scoreArea
                }
                this.isPreview = false
                this.resetPreviewImg(true).then(() => {
                    this.refreshCropper()
                })
            }
        },
        renameTitle(raw) {
            this.$prompt(this.$t('docReview.updateDocName'), '', {
                confirmButtonText: this.$t('common.confirm'),
                cancelButtonText: this.$t('common.cancel'),
                inputValue: raw
            }).then(res => {
                this.docname = res.value
            })
        },
        drawRect() {
            const img = document.getElementById("img");
            const canvas = this.$refs.uploadCanvas
            const ctx = canvas.getContext('2d')

            // 设置canvas大小
            canvas.width = img.width;
            canvas.height = img.height;
            
            // 绘制图片
            ctx.drawImage(img, 0, 0);

            this.questions.forEach(question => {
                let rects = [question.answerArea, question.reviewArea, question.checkArea]
                rects.forEach(rect => {
                    if (!rect) {
                        return
                    }
                    let {x, y, width, height} = rect
                    // 绘制方框
                    ctx.strokeStyle = 'black'; // 设置边框颜色
                    ctx.lineWidth = 2; // 设置边框宽度
                    ctx.strokeRect(x, y, width, height); // 绘制方框
                })
            })

            img.src = canvas.toDataURL('image/jpeg');
            this.refreshCropper()
        },
        onPreviewChange(val) {
            if (!this.docurl || this.docurl === '') {
                this.$message.warning(this.$t('docReview.pleaseAddDoc'))
                this.isPreview = !val
                return
            }
            if (val) {
                this.drawRect()
            } else {
                this.isPreview = false 
                this.resetPreviewImg().then(() => {
                    this.refreshCropper()
                })
            }
        },
        resetPreviewImg(reload) {
            if (reload || !this.imageUrl || this.imageUrl === '') {
                return fetch(this.docurl)
                    .then(res => res.blob())
                    .then(blob => {
                        this.imageUrl = URL.createObjectURL(blob);
                        const img = document.getElementById("img");
                        img.src = this.imageUrl
                    })
            } else {
                img.src = this.imageUrl
                return Promise.resolve()
            }
        },
        refreshPreview() {
            this.resetPreviewImg().then(() => {
                this.refreshCropper(this.drawRect)
            })
        }
    }
}
</script>

<style lang="scss" scope>
.main-wrapper {
    flex-direction: column;
    height: 100%;

    .header-bar {
        display: flex;
        align-items: center;
        padding: 0 20px;
        height: 60px;
        // border-bottom: 1px solid var(--el-border-color);

        .header-title {
            font-size: 20px;
            font-weight: 700;
        }

        .header-action {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 10px;

            .el-button+.el-button {
                margin-left: 0;
            }
        }
    }

    .content-wrapper {
        height: calc(100% - 60px);
        display: flex;

        .content-cropper-wrapper {
            width: 45%;
            height: 100%;
            padding: 10px;
            border: 1px solid var(--el-border-color);
            border-radius: var(--el-border-radius-large);
            background-color: var(--el-bg-color-page);
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .content-cropper {
            width: 100%;
            height: 100%;

            .img {
                display: block;
                object-fit: contain;
                width: 100%;
                max-height: 100%;
            }

            .content-cropper-empty {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .content-board {
            flex: 1;
            padding: 20px;
            overflow: hidden;

            .content-questions {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
        }
    }
}
</style>