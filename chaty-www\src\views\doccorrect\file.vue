<template>
  <div class="main-wrapper">
    <div class="header-bar">
      <el-image src="/icon/16.png" class="left-icon"></el-image>
      <el-text class="title">样卷列表</el-text>
      <el-input class="filter-item" v-model="filter.name" placeholder="请输入名称"/>
      <el-select class="filter-item" v-model="filter.status" placeholder="请选择状态" clearable>
        <el-option v-for="item in Object.values(statusOptions)" :key="item.value" :label="item.label"
                   :value="item.value"/>
      </el-select>
      <el-button type="primary" @click="loadData">查询</el-button>
      <div class="right"/>
    </div>
    <div class="start-config">
      <div class="start-button" type="primary" icon="EditPen" @click="onUpload">
        <el-image src="/icon/paperStart.svg" class="icon" ></el-image>
        <el-text class="text">上传试卷</el-text>
      </div>
    </div>
    <div class="main-content">
      <el-table v-loading="loading" :data="data" style="height: 100%" empty-text="无数据" :border="false" >
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center" :fixed="column.prop === 'operations' ? 'right':''">
          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-space>
              <el-button type="primary" text size="small" @click="onDelete(scope.row.id)">删除</el-button>
              <el-dropdown>
                  <span>
                    <el-icon class="el-icon--right">
                      <more/>
                    </el-icon>
                  </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="scope.row.status !== 2" @click="onCorrect(scope.row.id)">批改
                    </el-dropdown-item>
                    <el-dropdown-item v-if="scope.row.status === 3" @click="onStatsDownloadSubmit(scope.row)" divided>统计结果</el-dropdown-item>
                    <el-dropdown-item v-if="scope.row.status === 3" @click="onDownloadSubmit(scope.row)">批改含原卷</el-dropdown-item>
                    <el-dropdown-item v-if="scope.row.status === 3" @click="onDownloadSubmit(scope.row, {isPreview: false})">批改不含原卷</el-dropdown-item>
                    <el-dropdown-item @click="onEditTask(scope.row)" divided>编辑</el-dropdown-item>
                    <el-dropdown-item @click="onDeleteTask(scope.row.id)" >删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-space>
          </template>
          <template v-else-if="column.prop === 'status'" v-slot="scope">
            <el-tag :type="statusOptions[scope.row.status]?.type">
              {{ statusOptions[scope.row.status]?.label }}
            </el-tag>
          </template>
          <template v-else-if="column.prop === 'name'" v-slot="scope">
            <el-link type="primary" :underline="false" @click="toDetail(scope.row.id)">{{ scope.row.name }}</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer-bar">
      <el-pagination background layout="prev, pager, next" v-model:page-size="pageSize"
                     v-model:current-page="pageNumber" :total="total" @current-change="loadData"/>
    </div>

    <!-- 文件上传表单 -->
    <file-form ref="fileForm" @onClose="loadData"/>
  </div>
</template>
<script>
import FileForm from './fileForm.vue'

export default {
  components: {
    FileForm
  },
  data() {
    return {
      filter: {
        name: "",
        status: null,
      },
      loading: false,
      data: [],
      columns: [
        {
          prop: "name",
          label: "试卷名称",
        },
        {
          prop: "status",
          label: "状态",
        },
        {
          prop: "operations",
          label: "操作",
          width: 110
        },
      ],
      pageSize: 10,
      pageNumber: 1,
      total: 0,
      statusOptions: {
        1: {value: 1, label: "待批改"},
        2: {value: 2, label: "批改中", type: "warning"},
        3: {value: 3, label: "批改完成", type: "success"},
      },
      timer: null,
    }
  },
  created() {
    this.loadData()
    this.timer = setInterval(() => {
      this.loadData()
    }, 10 * 1000);
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    loadData() {
      this.loading = true
      this.$axios.post("/api/docCorrectFile/page", {
        page: {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize
        },
        name: this.filter.name,
        status: this.filter.status
      }).then(res => {
        this.data = res.data.records
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },
    onUpload() {
      this.$refs.fileForm.show()
    },
    onDelete(id) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(res => {
        this.$axios.post(`/api/docCorrectFile/delete?id=${id}`).then(res => {
          this.$message.success("删除成功")
          this.loadData()
        })
      })
    },
    onCorrect(id) {
      this.$axios.post(`/api/docCorrectFile/correct`, {
        id
      }).then(() => {
        this.$message.success("批改中...")
        this.loadData()
      })
    },
    toDetail(id) {
      this.$router.push({path: `/docfile/${id}`})
    },
    onStatsDownloadSubmit(file) {
      let form = {
        fileId: file.id,
        fontSize: 20,
      }
      let loadingMessage = this.$message({
        message: "正在生成统计结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/download/stats", form).then(res => {
        return this.downloadFile(res.data.fileUrl, `${file.name + " " || ""}统计结果.pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
    downloadFile(url, name) {
      // 使用fetch获取文件内容
      return fetch(this.$fileserver.fileurl(url))
          .then(response => response.blob())
          .then(blob => {
            // 如果需要下载，可以使用前面提到的下载代码
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = URL.createObjectURL(blob);
            a.download = name;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(a.href);
          })
          .catch(error => {
            console.error('发生错误:', error);
          });
    },
    onDownloadSubmit(file, options) {
      console.log(file)
      let form = {
        isPreview: true,
        scoreMerge: true,
        showQsScore: true,
        fileId: file.id
      }
      Object.assign(form, options);
      let loadingMessage = this.$message({
        message: "正在生成批改结果...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      this.$axios.post("/api/docCorrectTask/download/reviewed", form).then(res => {
        return this.downloadFile(res.data.fileUrl, `${file.name + " " || ""}批改结果(${form.isPreview ? '含原卷' : '不含原卷'}).pdf`);
      }).finally(() => {
        loadingMessage.close()
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .left-icon {
      width: 28.28px;
      height: 22.89px;
      transform: scaleX(-1);
    }
    .title {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-right: 19px;
    }
    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }
  .start-config {
    height: 135px;
    width: 100%;
    border-top: 2px solid #f5f5f5;
    border-bottom: 2px solid #f5f5f5;
    padding: 16px 0;
    display: flex;
    align-items: center;

    .start-button {
      width: 253px;
      height: 103px;
      background: #e1f2ff;
      border-radius: 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .icon {
        width: 55.89px;
        height: 57px;
      }
      .text {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        margin-top: 4.5px;
      }
    }
  }

  .main-content {
    flex: 1;
    margin-top: 15px;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>