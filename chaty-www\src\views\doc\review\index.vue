<template>
    <div class="review-wrapper">
        <div class="header-bar">
            <el-select class="header-option" v-model="aimodel">
                <el-option label="gpt-4-vision" value="gpt-4-vision-preview" />
                <el-option label="gpt-4-turbo" value="gpt-4-turbo" />
                <el-option label="gpt-4" value="gpt-4" />
                <el-option label="gpt-3.5-turbo" value="gpt-3.5-turbo" />
                <el-option label="ERNIE-Bot" value="ERNIE-Bot" />
            </el-select>
            <el-select class="header-option" v-model="ocr">
                <el-option label="MathPix OCR" value="mathPixOCRService" />
                <el-option label="Tencent OCR" value="tencentOCRService" />
                <el-option label="gpt-4-vision" value="openaiService" />
            </el-select>
            <el-dropdown class="header-option" trigger="click" placement="bottom-start" :hide-on-click="false">
                <span class="el-dropdown-link">
                    <el-button type="warning" icon="Tools">设置</el-button>
                </span>
                <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item>
                        <span style="width: 100px; float: left;">字体大小：</span>
                        <el-input-number class="content" v-model="fontSize"></el-input-number>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <span style="width: 100px; float: left;">勾和叉的大小：</span>
                        <el-input-number class="content" v-model="signSize"></el-input-number>
                    </el-dropdown-item>
                </el-dropdown-menu>
                </template>
            </el-dropdown>
            <el-button style="margin-left: auto;" type="primary" :icon="reviewDownloading ? 'Loading': 'Download'" @click="downloadReviewed">下载批改结果</el-button>
            <el-button style="margin-right: 10px;" type="primary" :icon="previewDownloading ? 'Loading': 'Download'" 
                    @click="downloadReviewed({preview: true, filename: '预览文件.pdf', loading: 'previewDownloading'})">预览结果</el-button>
            <el-upload style="margin-right: 10px;" :action="$fileserver.uploadUrl" accept=".pdf" multiple
                :show-file-list="false" :on-success="uploadDoc">
                <el-button type="primary" icon="Plus">添加试卷</el-button>
            </el-upload>
            <el-button type="primary" :icon="reviewing ? 'Loading': ''" @click="review()">批改</el-button>
        </div>
        <div class="content-wrapper">
            <el-table :data="docs" :border="true" height="100%" empty-text="请添加试卷">
                <el-table-column v-for="column in columns" v-bind="column" :key="column.prop">
                    <template v-if="column.prop === 'doc'" v-slot="scope">
                        <el-link type="primary" :underline="false" :href="$fileserver.fileurl(scope.row.fileurl)"
                            target="_blank">{{ scope.row.filename }}</el-link>
                    </template>
                    <template v-else-if="column.prop === 'operations'" v-slot="scope">
                        <el-button text v-if="!reviewedIds.has(scope.row.id)" size="small"
                            @click="review(scope.row.id)">批改</el-button>
                        <el-button text v-else size="small">批改中...</el-button>
                        <el-button text size="small" @click="doDelete(scope.row.id)">删除</el-button>
                        <el-button text size="small" v-if="scope.row.status === 3" @click="doPreview(scope.row)">预览</el-button>
                        <el-button text size="small" v-if="scope.row.status === 3" @click="docReviewed(scope.row)">下载</el-button>
                        <!-- <el-link style="margin-left: 10px" type="primary" :underline="false" v-if="scope.row.status === 3"
                            :href="$fileserver.fileurl(scope.row.reviewedDoc)" target="_blank">下载</el-link> -->
                        <!-- <el-button text size="small" v-if="scope.row.status === 3" style="margin-left: 10px" @click="docPreview(scope.row.id)">预览</el-button> -->
                    </template>
                    <template v-else-if="column.type === 'dic'" v-slot="scope">
                        <el-tag :type="column.options[scope.row[column.prop]].type">
                            {{ column.options[scope.row[column.prop]].label }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            aimodel: 'gpt-4-vision-preview',
            ocr: 'mathPixOCRService',
            docs: [],
            columns: [
                { label: '试卷', prop: 'doc', align: 'center' },
                {
                    label: '状态', prop: 'status', align: 'center', type: 'dic', options: {
                        1: { label: '未批改' },
                        2: { label: '批改中', type: 'warning' },
                        3: { label: '已批改', type: 'success' },
                        4: { label: '批改失败', type: 'danger' },
                    }
                },
                { label: '操作', prop: 'operations', align: 'center' },
            ],
            reviewedIds: new Set(),
            batchId: '',
            refresher: null,
            fontSize: 8,
            signSize: 30,
            reviewDownloading: false,
            previewDownloading: false,
            reviewing: false,
        }
    },
    methods: {
        uploadDoc(response, file) {
            this.$axios.post("/api/doc/reviewed/add", {
                filename: file.name,
                fileurl: response.data.url,
                status: 1,
            }).then(res => {
                this.docs.push(res.data)
            })
        },
        doDelete(id) {
            this.docs = this.docs.filter(doc => doc.id !== id)
        },
        review(id) {
            let ids = []
            let isBatch = true
            if (id) {
                this.reviewedIds.add(id)
                ids = [id]
                isBatch = false
            } else {
                ids = this.docs.map(doc => doc.id)
                this.reviewing = true
            }
            this.$axios.post("/api/doc/reviewed/review", {
                aimodel: this.aimodel,
                ocrService: this.ocr,
                ids,
                signSize: this.signSize,
                fontSize: this.fontSize,
            }).then(res => {
                if (isBatch) {
                    this.batchId = res.data.batchId
                }
                this.refreshDoc()
            })
        },
        refreshDoc() {
            if (!this.refresher) {
                this.refresher = setInterval(() => {
                    let form = {}
                    if (this.batchId) {
                        form.batchId = this.batchId
                    } else {
                        form.ids = [...this.reviewedIds]
                    }
                    this.$axios.post("/api/doc/reviewed/list", form).then(res => {
                        let map = {}
                        let finished = true
                        res.data.forEach(doc => {
                            map[doc.id] = doc
                            if (doc.status === 2) {
                                finished = false
                            }
                        })
                        this.docs.forEach(doc => {
                            if (map[doc.id]) {
                                Object.assign(doc, map[doc.id])
                            }
                        })
                        if (finished) {
                            clearInterval(this.refresher)
                            this.refresher = null
                            this.batchId = ''
                            this.reviewedIds.clear()
                            this.reviewing = false
                            this.$message.success("批改完成")
                        }
                    })
                }, 3 * 1000);
            }
        },
        downloadReviewed(options) {
            let defaultOptions = {
                preview: false,
                filename: '批改文件.pdf',
                loading: 'reviewDownloading'
            }
            options = Object.assign(defaultOptions, options)

            if (this.docs.length === 0) {
                this.$message.warning("请添加试卷进行批改")
                return
            }
            if (this.reviewedIds.size > 0 || this.batchId || this.batchId !== '') {
                this.$message.warning("请等待批改完成")
                return
            }
            let unreviewed = this.docs.find(doc => doc.status != 3)
            if (unreviewed) {
                this.$message.warning("请确认所有试卷都已批改完成")
                return
            }

            this[options.loading] = true
            this.$axios.post("/api/doc/reviewed/batchReviewDoc", {
                docs: this.docs,
                signSize: this.signSize,
                fontSize: this.fontSize,
                preview: options.preview
            }).then(res => {
                this.downloadFile(this.$fileserver.fileurl(res.data.fileUrl), options.filename)
            }).finally(() => this[options.reviewDownloading] = false)
        },
        downloadFile(url, name) {
            // 使用fetch获取文件内容
            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    // 如果需要下载，可以使用前面提到的下载代码
                    const a = document.createElement("a");
                    a.style.display = "none";
                    a.href = URL.createObjectURL(blob);
                    a.download = name;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                })
                .catch(error => {
                    console.error('发生错误:', error);
                });
        },
        doPreview(doc) {
            this.$axios.post("/api/doc/reviewed/batchReviewDoc", {
                docs: [doc],
                signSize: this.signSize,
                fontSize: this.fontSize,
                preview: true,
            }).then(res => {
                this.downloadFile(this.$fileserver.fileurl(res.data.fileUrl), `${doc.filename.substring(0, row.filename.lastIndexOf('.'))}_预览.pdf`)
            })
        },
        docReviewed(doc) {
            this.downloadFile(this.$fileserver.fileurl(doc.reviewedDoc), `${doc.filename.substring(0, row.filename.lastIndexOf('.'))}_批改.pdf`)
        }
    }
}
</script>

<style lang="scss" scoped>
.review-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header-bar {
        display: flex;
        align-items: center;
        height: 50px;

        .header-option {
            width: 150px;

            &:not(:first-child) {
                margin-left: 10px;
            }
        }
    }

    .content-wrapper {
        flex: 1;
    }
}
</style>