<template>
    <div class="container-wrapper">
        <el-card class="left-bar">
            <template #header>
                <div class="card-header">
                    <span>设置</span>
                </div>
            </template>
            <el-scrollbar>
                <el-form ref="configForm" :model="config" class="form-config" label-position="top" :rules="configRules">
                    <el-form-item label="支持模型列表：" prop="aimodel">
                        <el-select v-model="config.aimodel" style="width: 100%;">
                            <el-option label="gpt-4-turbo" value="gpt-4-turbo" />
                            <el-option label="gpt-4" value="gpt-4" />
                            <el-option label="gpt-3.5-turbo" value="gpt-3.5-turbo" />
                            <el-option label="ERNIE-Bot" value="ERNIE-Bot" />
                            <!-- <el-option label="ERNIE-Bot-turbo" value="ERNIE-Bot-turbo" />
                            <el-option label="CPM hackthon" value="CPM hackthon" />
                            <el-option label="BLOOMZ-7B" value="BLOOMZ-7B" /> -->
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="question">
                        <template #label>
                            <span>题目：</span>
                            <el-button style="margin-left: 120px;" @click="selectLibrary(0)">
                                知识库
                            </el-button>
                        </template>
                        <el-input v-model="config.question" :autosize="{ minRows: 5, maxRows: 7 }" type="textarea"
                            placeholder="请输入题目">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="答案：" prop="answer">
                        <el-input v-model="config.answer" :autosize="{ minRows: 5, maxRows: 7 }" type="textarea"
                            placeholder="请输入正确答案" />
                    </el-form-item>
                    <el-form-item label="知识点：" prop="knowledge">
                        <el-input v-model="config.knowledge" :autosize="{ minRows: 5, maxRows: 7 }" type="textarea"
                            placeholder="请输入知识点" />
                    </el-form-item>
                    <el-form-item prop="related">
                        <template #label>
                            <span>相关题目：</span>
                            <el-button :loading="ocrloading" style="margin-left: 100px;" @click="selectLibrary(1)">
                                知识库
                            </el-button>
                        </template>
                        <el-input v-model="config.related" :autosize="{ minRows: 5, maxRows: 7 }" type="textarea"
                            placeholder="请输入相关题目">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="提示预览：" prop="promot">
                        <el-input v-model="config.promot" :autosize="{ minRows: 5, maxRows: 7 }" type="textarea"
                            @change="onPromotChange" />
                    </el-form-item>
                </el-form>
            </el-scrollbar>
        </el-card>
        <el-card class="right-content">
            <template #header>
                <div class="card-header">
                    <span>内容</span>
                    <div>
                        <el-upload ref="upload" style="display: inline-block; margin-right: 12px;" :show-file-list="false"
                            class="button" :action="$fileserver.uploadUrl" :multiple="true" :on-success="onImgUploadSuccess"
                            accept="image/*" :auto-upload="true">
                            <template #trigger>
                                <el-button type="primary">添加文件</el-button>
                            </template>
                        </el-upload>
                        <el-button class="button" type="primary" @click="addAnswer">添加答案</el-button>
                        <el-button class="button" type="primary" @click="batchAdd2Library"
                            :loading="batchAdd2LibraryLoading">保存到知识库</el-button>
                        <el-button class="button" type="primary" @click="batchCheck"
                            :loading="batchReviewLoading">批改</el-button>
                        <el-button class="button" type="primary" @click="clearData">清空</el-button>
                    </div>
                </div>
            </template>
            <div class="result-wrapper" v-loading="loading">
                <el-table :data="tableData" style="width: 100%; user-select: text;" class="main-table" empty-text="请添加文件">
                    <template v-for="column in columns" :key="column.prop">
                        <el-table-column v-bind="column">
                            <template v-if="column.prop === 'file'" #default="scope">
                                <div class="file-wrapper" v-if="scope.row.fileurl">
                                    <el-text truncated style="margin-bottom: 10px;">{{ scope.row.filename }}</el-text>
                                    <div style="display: flex; align-items: center">
                                        <el-image :src="scope.row.fileurl" :preview-src-list="[scope.row.fileurl]"
                                            preview-teleported style="width: 180px; max-height: 200px;"
                                            fit="contain"></el-image>
                                    </div>
                                </div>
                            </template>
                            <template v-else-if="column.prop === 'status'" #default="scope">
                                <el-text style="white-space: pre-wrap; word-break: break-all;"
                                    :type="statusType(scope.row.state)">{{ scope.row.status }}</el-text>
                            </template>
                            <template v-else-if="column.prop === 'operations'" #default="scope">
                                <el-button type="primary" style="width: 80px; margin-left: 0" plain size="small"
                                    @click="doCheck(scope.row)">批改</el-button>
                                <el-button type="primary" style="width: 80px; margin-left: 0" plain size="small"
                                    @click="delData(scope.$index)">删除</el-button>
                                <el-dropdown v-if="scope.row.state === 2" style="margin-left: 0"
                                    @command="(v) => createAndDownloadPDF(scope.row, v)">
                                    <el-button :loading="loadingPDF" style="width: 80px; " plain size="small"
                                        @click="createAndDownloadPDF(scope.row)" type="primary">
                                        下载PDF
                                        <el-icon class="el-icon--right"><arrow-down /></el-icon>
                                    </el-button>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item command="v1">V1</el-dropdown-item>
                                            <el-dropdown-item command="v2">V2</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                                <el-button v-if="scope.row.state === 2" type="primary" style="width: 80px; margin-left: 0"
                                    plain size="small" @click="add2Library(scope.row)">添加到知识库</el-button>
                            </template>
                            <template v-else-if="column.prop === 'isTrue'" #default="scope">
                                <el-text style="white-space: pre-wrap; word-break: break-all;"
                                    :type="scope.row.isTrue === 1 ? 'success' : 'danger'">{{ scope.row.trueText }}</el-text>
                            </template>
                            <template v-else-if="column.type === 'textarea'" #default="scope">
                                <el-scrollbar max-height="200px">
                                    <el-text :type="column.textType || ''" style="white-space: pre-wrap; word-break: break-all;">
                                        {{ scope.row[column.prop] }}
                                    </el-text>
                                </el-scrollbar>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </el-card>
        <library-dialog :isShow="isShowLibrary" @onClose="onLibraryClose" @onSelected="onLibrarySelected"></library-dialog>
        <answer-dialog :isShow="isShowAnswer" @onClose="onAnswerClose"></answer-dialog>
    </div>
    <!-- <pdfviewer id="pdf-dom" :data="selectedData" v-show="showPDFViewer"></pdfviewer> -->
    <!-- <latex-viewer></latex-viewer> -->
</template>

<script>
import Pdfviewer from './pdfviewer.vue'
import { getPdf } from '../../utils/htmlToPdf'
import { nextTick } from 'vue'
import LatexViewer from './latexviewer.vue'
import LibraryDialog from './library-dialog.vue'
import AnswerDialog from './answer-dialog.vue'
import { ElMessageBox } from 'element-plus'
export default {
    components: {
        Pdfviewer,
        LatexViewer,
        LibraryDialog,
        AnswerDialog
    },
    data() {
        return {
            config: {
                aimodel: 'gpt-4',
                question: '',
                answer: '',
                promot: "语言必须使用中文，数学公式用Latex，请扮演数学老师帮助学生批改作业，我会提供题目、正确答案与学生答案，请忽略学生答案中由于图片转文字出现的识别错误，关注是否数学上是否正确，比较正确答案与学生答案，然后判断学生答案是否完全正确，如果有部分错误视为错误。如果学生答案错误，请指出具体错误的步骤；结合问题以及学生答案，对学生答案进行评价。\n" +
                    "请根据下面的内容，给出你的批改结果：\n" +
                    "题目: ${question} \n" +
                    "正确答案: ${answer} \n" +
                    "学生答案: ${myanswer}",
                knowledge: '',
                related: '',
            },
            configRules: {
                aimodel: [
                    { required: true, message: '请选择支持模型', trigger: 'blur' }
                ],
                question: [
                    { required: true, message: '请输入题目', trigger: 'blur' }
                ],
                answer: [
                    { required: true, message: '请输入正确答案', trigger: 'blur' }
                ],
                promot: [
                    { required: true, message: '请输入提示信息', trigger: 'blur' }
                ],
            },
            columns: [
                {
                    prop: 'file',
                    label: '文件',
                    width: 150,
                },
                {
                    prop: 'ocrText',
                    label: '我的答案',
                    type: 'textarea',
                },
                {
                    prop: 'errText',
                    label: '错误',
                    type: 'textarea',
                    textType: 'danger',
                },
                {
                    prop: 'comment',
                    label: '评价',
                    type: 'textarea',
                },
                {
                    prop: 'status',
                    label: '状态',
                    type: 'textarea',
                    width: 100,
                },
                {
                    prop: 'isTrue',
                    label: '是否正确',
                    width: 100,
                },
                {
                    prop: 'operations',
                    label: '操作',
                    align: 'center',
                    width: 120,
                }
            ],
            tableData: [],
            checkIndex: 0,
            selectedData: {},
            showPDFViewer: false,
            loading: false,
            isShowLibrary: false,
            libraryType: '', // 知识库赋值类型 0-题目 1-相关题目
            isShowAnswer: false,
            ocrloading: false,
            batchAdd2LibraryLoading: false,
            batchReviewLoading: false,
        }
    },
    created() {
        let gradePromot = localStorage.getItem("gradePromot")
        if (gradePromot) {
            this.config.promot = gradePromot
        }
    },
    methods: {
        onImgUploadSuccess(response, uploadFile) {
            this.tableData.push({
                filename: uploadFile.name,
                fileurl: this.$fileserver.fileurl(response.data.url),
                // fileurl: 'https://ocr-demo-1254418846.cos.ap-guangzhou.myqcloud.com/general/GeneralAccurateOCR/GeneralAccurateOCR2.jpg',
                ocrText: "",
                aiText: "",
                status: "",
                state: 0, // 0: 未批改， 1: 批改中， 2: 批改完成  3: 批改失败
            })
        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
        clearData() {
            this.tableData = []
        },
        delData(index) {
            this.tableData.splice(index, 1)
        },
        doCheck(data, next) {
            data = Object.assign(data, {
                state: 0,
                status: "",
                aiText: "",
                pdfUrl: '',
            })
            this.$refs.configForm.validate((valid, fields) => {
                if (valid) {
                    let op;
                    if (data.ocrText) {
                        data.status = "正在AI批改..."
                        op = Promise.resolve();
                    } else {
                        data.state = 1
                        data.status = "正在ocr识别..."
                        op = this.ocrForText(data).then(res => {
                            data.ocrText = res.data
                            data.status = "正在AI批改..."
                        }, err => {
                            data.state = 3
                            data.status = `ocr识别失败: ${err.message}`
                            return Promise.reject(err)
                        })
                    }
                    op.then(() => {
                        return this.aicheck(data).then(res => {
                            data.isTrue = -1

                            const { $response, $function_call } = res.data
                            if ($function_call && $function_call.name === 'validate_answer') {
                                let paramStr = $function_call.arguments.replaceAll('\\', '\\\\')
                                let param = JSON.parse(paramStr)
                                data.isTrue = param.isTrue ? 1 : 0
                                data.errText = param.error
                                data.comment = param.review
                                data.trueText = param.isTrue ? '正确' : '错误'
                            }

                            console.log(data)
                            data.status = "批改完成"
                            data.state = 2
                        }, err => {
                            data.state = 3
                            data.status = `AI批改失败: ${err.message}`
                        })
                    }).finally(() => {
                        if (next) {
                            next();
                        }
                    })
                } else {
                    this.$message.error('请完善设置信息')
                }
            })
        },
        matchAIText(text, regex) {
            const matches = text.match(regex);
            if (matches && matches.length >= 2) {
                return matches[1]; // 匹配到的内容位于索引为 1 的位置
            } else {
                return ""; // 如果未匹配到则返回空字符串
            }
        },
        ocrForText(data) {
            // return Promise.resolve({ data: "这是ocr识别的结果" })
            // data.fileurl = 'http://110.40.186.52:10001/static/40687642e4564956b6742824f5106384.jpg';
            return this.$axios.post(`/api/ocr/url2text?url=${data.fileurl}&service=mathPixOCRService`);
        },
        /* aicheck(data) {
            let form = [
                {
                    role: 'user',
                    content: this.replaceText(this.config.promot, {
                        question: this.config.question,
                        answer: this.config.answer,
                        myanswer: data.ocrText,
                    })
                }
            ]
            return this.$axios.post(`/api/openai/completionForMessage?model=${this.config.aimodel}&temperature=1`, form);
        }, */
        aicheck(data) {
            let form = {
                model: this.config.aimodel,
                messages: [
                    {
                        role: 'user',
                        content: this.replaceText(this.config.promot, {
                            question: this.config.question,
                            answer: this.config.answer,
                            myanswer: data.ocrText,
                            knowledge: this.config.knowledge,
                        })
                    },
                ],
                temperature: 0.1,
                functions: [
                    {
                        name: 'validate_answer',
                        description: '分析学生提交的答案的结果',
                        parameters: {
                            type: "object",
                            properties: {
                                isTrue: {
                                    type: "boolean",
                                    description: "学生提交的答案是否完全正确",
                                },
                                error: {
                                    type: "string",
                                    description: "学生答案具体错误部分,涉及到的数学公式用Latex格式",
                                },
                                review: {
                                    type: "string",
                                    description: "学生答案的评价,涉及到的数学公式用Latex格式",
                                },
                            }
                        },
                        required: ["isTrue", "review"]
                    }
                ]
            }
            return this.$axios.post(`/api/chat/completion`, form);
        },
        replaceText(text, ctx) {
            return text.replace(/\${(\w+)}/g, function (match, key) {
                return ctx[key] || match;
            });
        },
        statusType(state) {
            if (state === 1) {
                return "info"
            } else if (state === 2) {
                return "success"
            } else if (state === 3) {
                return "danger"
            }
        },
        batchCheck() {
            this.$refs.configForm.validate((valid, fields) => {
                if (valid) {
                    this.$confirm('批量批改作业?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.batchReviewLoading = true
                        this.checkIndex = 0
                        this.doBatchCheck()
                    })
                } else {
                    this.$message.error('请完善设置信息')
                }
            })
        },
        doBatchCheck() {
            this.doCheck(this.tableData[this.checkIndex], () => {
                this.checkIndex++
                if (this.checkIndex < this.tableData.length) {
                    this.doBatchCheck()
                } else {
                    this.batchReviewLoading = false
                    this.$message.success("批改完成")
                }
            }, err => {
                this.batchReviewLoading = false
            })
        },
        showPDF(data) {
            this.selectedData = Object.assign({}, data)
            this.showPDFViewer = true
            nextTick(() => {
                getPdf("批改结果", 'pdf-dom')
                this.showPDFViewer = false
            })
        },
        createAndDownloadPDF(data, version = 'v2') {
            let filename = this.getFilename(data)
            ElMessageBox.prompt('', '文件名称', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                inputValue: filename,
            }).then(res => {
                const filename = `${res.value}.pdf`

                this.loading = true
                this.$axios.post(`/api/pdf/createPDF`, Object.assign({ version }, data, this.config)).then(res => {
                    let url = this.$fileserver.fileurl(res.data.fileUrl);
                    data.pdfUrl = url;
                    this.downloadFile(data.pdfUrl, filename)
                }).finally(() => {
                    this.loading = false
                });
            })
        },
        selectLibrary(type) {
            this.libraryType = type
            this.isShowLibrary = true
        },
        onLibrarySelected(library) {
            if (this.libraryType === 0) {
                this.config.question = library.question
                this.config.answer = library.answer
                this.config.knowledge = library.knowledge
            }
            if (this.libraryType === 1) {
                this.config.related = library.question
            }
            this.onLibraryClose()
        },
        onLibraryClose() {
            this.isShowLibrary = false
        },
        addAnswer() {
            this.isShowAnswer = true
        },
        onAnswerClose(answer) {
            if (answer) {
                let data = {
                    ocrText: answer,

                    aiText: "",
                    status: "",
                    state: 0, // 0: 未批改， 1: 批改中， 2: 批改完成  3: 批改失败
                }
                this.tableData.push(data)
            }
            this.isShowAnswer = false;
        },
        downloadFile(url, name) {
            // 使用fetch获取文件内容
            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    // 如果需要下载，可以使用前面提到的下载代码
                    const a = document.createElement("a");
                    a.style.display = "none";
                    a.href = URL.createObjectURL(blob);
                    a.download = name;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                })
                .catch(error => {
                    console.error('发生错误:', error);
                });
        },
        add2Library(data) {
            let filename = this.getFilename(data)

            return ElMessageBox.prompt('', '名称', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                inputValue: filename,
            }).then(res => {
                this.doAdd2Library(res.value, data)
            })
        },
        doAdd2Library(name, data) {
            let form = {
                name,
                question: this.config.question,
                correctAnswer: this.config.answer,
                knowledge: this.config.knowledge,
                answer: data.ocrText,
                isTrue: data.isTrue,
                errText: data.errText,
                comment: data.comment,
                aiContent: data.aiText,
            }
            return this.$axios.post("/api/review/add", form).then(res => {
                this.$message.success("已添加到知识库")
            })
        },
        batchAdd2Library() {
            ElMessageBox.confirm('确认批量保存到知识库？', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
            }).then(() => {
                this.batchAdd2LibraryLoading = true
                let tableData = Object.assign([], this.tableData)
                let chain = Promise.resolve()
                tableData.forEach((item) => {
                    let filename = this.getFilename(item)
                    if (item.state === 2 && filename && filename !== '') {
                        chain = chain.then(() => {
                            return this.doAdd2Library(filename, item)
                        })
                    }
                })
                chain.finally(() => {
                    this.$message.success("批量保存到知识库成功")
                    this.batchAdd2LibraryLoading = false
                })
            })
        },
        getFilename(data) {
            let filename = '';
            if (data.filename) {
                filename = data.filename.split('.')[0]
            }
            return filename
        },
        onPromotChange(promot) {
            localStorage.setItem("gradePromot", promot)
        }
    }
}
</script>

<style lang="scss" scoped>
.container-wrapper {
    width: auto !important;
    display: flex;
    column-gap: 20px;
    height: 100%;

    .left-bar {
        width: 300px;
        height: 100%;

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        :deep(.el-scrollbar) {
            height: calc(100vh - 210px);
        }
    }

    .right-content {
        flex: 1;
        max-height: calc(100vh - 100px);

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

}

.result-wrapper {
    .file-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px;
    }

    .main-table {
        height: calc(100vh - 210px);
    }
}
</style>