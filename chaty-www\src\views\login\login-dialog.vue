<template>
    <el-dialog class="login-dialog" width="500px" v-model="isShow" :before-close="onClose" :show-close="false">
        <div class="login-panel" @keyup.enter="doLogin">
            <div class="panel-header">
                <el-image class="logo" src="/cat1.png" />
                <div class="title">{{ $t('title') }}</div>
            </div>
            <el-form class="login-form" ref="loginForm" :model="form" :rules="rules">
                <el-form-item prop="username">
                    <el-input class="login-form-input" v-model="form.username" :placeholder="$t('common.pleaseEnter', [$t('login.username')])"></el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input class="login-form-input" v-model="form.password" type="password"
                    :placeholder="$t('common.pleaseEnter', [$t('login.password')])"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button class="login-form-btn" style="width: 100%" type="primary" @click="doLogin">{{ $t('login.login') }}</el-button>
                </el-form-item>
            </el-form>
        </div>
    </el-dialog>
</template>

<script>
import { mapState, mapActions } from 'pinia'
import { useUserStore } from '@/store/index'

export default {
    data() {
        return {
            form: {
                username: "",
                password: "",
            },
            rules: {
                username: [
                    {required: true, message: this.$t('common.pleaseEnter', [this.$t('login.username')]), trigger: "blur"}
                ],
                password: [
                    {required: true, message: this.$t('common.pleaseEnter', [this.$t('login.password')]), trigger: "blur"}
                ]
            },
            isShow: false,
        }
    },
    computed: {
        ...mapState(useUserStore, ['isShowLogin']),
    },
    watch: {
        isShowLogin(val) {
            this.isShow = val
        }
    },
    methods: {
        ...mapActions(useUserStore, ['showLogin', 'setUser']),
        doLogin() {
            this.$refs.loginForm.validate(valid => {
                if (valid) {
                    this.$axios.post("/api/login", this.form, {
                        headers: { 'content-type': 'application/x-www-form-urlencoded' },
                    }).then(res => {
                        this.$message.success(this.$t('login.loginSuccess'))
                        this.showLogin(false)
                        this.setUser(res.data)
                        location.reload();
                    })
                }
            })
        },
        onClose() {
            this.showLogin(false)
        }
    }
}
</script>

<style lang="scss" scoped>

.login-panel {
    width: 460px;
    height: 300px;
    padding: 30px;
    // border: 1px solid var(--el-border-color);
    border-radius: 4px;
    display: flex;
    align-items: center;
    flex-direction: column;

    .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 40px;
        padding-right: 35px;

        .logo {
            width: 40px;
            margin-right: 10px;
        }

        .title {
            font-size: 25px;
            font-weight: 700;
        }
    }

    .login-form {
        width: 300px;

        .login-form-input {
            height: 35px;
        }

        .login-form-btn {
            height: 35px;
        }
    }
}
</style>