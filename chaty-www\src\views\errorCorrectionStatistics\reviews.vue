<template>
  <div>
    <el-table :data="data" size="small" border>
      <el-table-column type="index" label="序号" width="50" align="center"/>
      <el-table-column v-for="column in columns" :key="column.prop"
                       v-bind="column" align="center" :width="column.width || ''">
        <template v-if="column.prop === 'area'" v-slot="scope">
          <el-link v-if="scope.row.areaImg" type="primary"
                   :href="$fileserver.fileurl(scope.row.areaImg)" target="_blank">
            {{ `区域${scope.row.areaIdx + 1}` }}
          </el-link>
          <el-text v-else>{{ `区域${scope.row.areaIdx + 1}` }}</el-text>
        </template>
        <template v-else-if="column.prop === 'isCorrect'" v-slot="scope">
          <el-text v-if="scope.row.isScorePoint === 2 || scope.row.isEssay">{{
              `${scope.row.scored} /
                        ${scope.row.score}`
            }}
          </el-text>
          <el-tag v-else-if="scope.row.isCorrect" :type="scope.row.isCorrect === 'Y' ? 'primary' : 'danger'">
            {{ scope.row.isCorrect === 'Y' ? '正确' : '错误' }}
          </el-tag>
        </template>
        <template v-else-if="column.prop === 'hasChange'" v-slot="scope">
          <el-tag :type="scope.row.hasChange === 1 ? 'success' : 'primary'">{{
              scope.row.hasChange === 1 ?
                  '已修改' : '未修改'
            }}
          </el-tag>
        </template>
        <template v-else-if="column.prop === 'score'" v-slot="scope">
          <el-text v-if="scope.row.isScorePoint === 2">{{
              `${scope.row.scored} / ${scope.row.score}`
            }}
          </el-text>
          <el-text v-else>{{
              `${scope.row.isCorrect === 'Y' ? scope.row.score : 0} / ${scope.row.score}`
            }}
          </el-text>
        </template>
        <template v-else-if="column.prop === 'answer'" v-slot="scope">
          <el-text v-if="Array.isArray(scope.row.answer) ">{{ scope.row.answer?.join(",") }}</el-text>
          <el-text v-else>{{ scope.row.answer }}</el-text>
        </template>
        <template v-else-if="column.prop === 'studentAnswer'" v-slot="scope">
          <el-text v-if="Array.isArray(scope.row.studentAnswer) ">{{
              scope.row.studentAnswer?.join(",")
            }}
          </el-text>
          <el-text v-else>{{ scope.row.studentAnswer }}</el-text>
        </template>
        <template v-else-if="column.prop === 'question'" v-slot="scope">
          <el-text v-if="scope.row.isEssay">作文题</el-text>
          <el-text>{{ scope.row.question }}</el-text>
        </template>
      </el-table-column>
    </el-table>

    <review-form ref="reviewForm"/>
  </div>

</template>

<script>
import ReviewForm from '@/views/doccorrect/components/reviewform.vue'

export default {
  components: {ReviewForm},
  props: {
    data: {
      type: Array,
      required: []
    }
  },
  data() {
    return {
      columns: [
        {
          prop: "area",
          label: "图片",
          simpleMode: true,
          width: 60
        },
        {
          prop: "question",
          label: "问题",
          width: 100,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "answer",
          label: "正确答案",
          width: 100,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "studentAnswer",
          label: "学生答案",
          width: 100,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "isCorrect",
          label: "是否正确",
          width: 80,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "hasChange",
          label: "是否修改",
          simpleMode: true,
          width: 90
        }
      ],
    }
  },
  methods: {
  }
}
</script>

<style lang="scss"></style>