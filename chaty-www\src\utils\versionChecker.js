import { ElMessage } from 'element-plus';

let currentVersion = null;

/**
 * 初始化并启动版本检查
 */
export async function initVersionChecker() {
    try {
        const initial = await fetch('/version.json', { cache: 'no-cache' });
        if (!initial.ok) {
            console.warn('[Update] Failed to fetch initial version.json:', initial.status);
            return;
        }
        const data = await initial.json();
        currentVersion = data.version;
        console.info(`[Update] Initial version loaded: ${currentVersion}`);
    } catch (err) {
        console.warn('[Update] Error while fetching initial version.json:', err);
        return;
    }

    setInterval(async () => {
        try {
            const res = await fetch('/version.json', { cache: 'no-cache' });
            if (!res.ok) {
                console.warn('[Update] Periodic fetch failed with status:', res.status);
                return;
            }
            const { version: latestVersion } = await res.json();
            if (latestVersion !== currentVersion) {
                console.info(`[Update] Detected version change: ${latestVersion} (was: ${currentVersion})`);
                currentVersion = latestVersion;

                // 倒计时提示：10 秒内每秒弹出一次
                let remaining = 10.0;
                const countdownInterval = setInterval(() => {
                    ElMessage.warning(`检测到新版本:请停止操作,${remaining}s后刷新页面 !! 如果在配置试卷中，请及时保存`);
                    remaining = remaining - 0.5;
                    if (remaining < 0) {
                        clearInterval(countdownInterval);
                        // 倒计时结束后刷新页面
                        window.location.reload(true);
                    }
                }, 500);
            }
        } catch (err) {
            console.warn('[Update] Periodic version check failed:', err);
        }
    }, 60 * 1000);
}
