<template>
  <el-dialog v-model="isShow" :title="title" width="400" :before-close="onClose">
    <el-form :model="formData" label-position="top" ref="configSelectionForm">
      <el-form-item label="选择配置卷" prop="selectedConfig">
        <el-radio-group v-model="formData.selectedConfig">
          <el-radio :label="null">不使用（自己配置）</el-radio>
          <el-radio v-for="config in configList" :key="config.id" :label="config.id">
            {{ config.name }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button type="primary" @click="onSubmit" :loading="submitting">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "ConfigSelectionDialog",
  props: {
    submitting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      title: "同一名称检测到多个配置卷，选择使用那套/不使用（自己配置）？",
      isShow: false,
      formData: {
        selectedConfig: null
      },
      configList: []
    }
  },
  methods: {
    show(configList) {
      this.configList = configList;
      this.isShow = true;
    },
    onClose() {
      if (this.$refs.configSelectionForm) {
        this.$refs.configSelectionForm.resetFields()
      }
      this.isShow = false;
    },
    onSubmit() {
      this.$emit("submit", this.formData.selectedConfig);
      this.onClose();
    }
  }
}
</script>

<style scoped>
</style>
