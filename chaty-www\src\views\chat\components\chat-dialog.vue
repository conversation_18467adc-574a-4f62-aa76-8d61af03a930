<template>
    <div class="chat-dialog" :style="dialogStyle">
        <div class="chat-dialog-avatar">
            <el-avatar :size="35" :src="dialog.avatarImg" />
            <div class="chat-dialog-role">{{ dialog.role }}</div>
        </div>
        <div class="chat-dialog-content" @mouseenter="contentHover = true" @mouseleave="contentHover = false">
            <v-md-preview :text="dialog.content"></v-md-preview>
            <div class="content-fixed-tr">
                <slot name="content-fixed-tr">
                    <el-button v-show="copyTr" class="copy-btn" text @click="copy2Clipboard"
                        icon="CopyDocument"></el-button>
                </slot>
            </div>
            <div class="content-fixed-tl">
                <slot name="content-fixed-tl">
                    <el-button v-show="copyTl" class="copy-btn" text @click="copy2Clipboard"
                        icon="CopyDocument"></el-button>
                </slot>
            </div>
        </div>
    </div>
</template>

<script>


export default {
    props: {
        dialog: {
            type: Object,
            default: () => {
                return {}
            }
        },
        copyTr: {
            type: Boolean,
            default: false
        },
        copyTl: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            contentHover: false,
        }
    },
    computed: {
        dialogStyle() {
            let res = {}
            if (this.dialog.rowReverse) {
                res = Object.assign(res, {
                    flexDirection: 'row-reverse',
                    paddingLeft: '150px',
                })
            } else {
                res = Object.assign(res, {
                    flexDirection: 'row',
                    paddingRight: '150px',
                })
            }
            return res;
        },
    },
    methods: {
        copy2Clipboard() {
            // 拷贝 this.dialog.content 到剪贴板
            var textarea = document.createElement('textarea');
            textarea.value = this.dialog.content;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            this.$message({
                message: '已复制',
                type: 'success'
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.chat-dialog {
    display: flex;
    width: 100%;

    .chat-dialog-avatar {
        width: 80px;
        text-align: center;

        .chat-dialog-role {
            font-size: 12px;
            color: var(--el-color-text-secondary);
        }
    }

    .chat-dialog-content {
        min-width: 200px;
        max-width: calc(100% - 80px);
        padding: 10px;
        border-radius: 10px;
        background-color: var(--el-bg-color-page);
        box-shadow: 0 0 10px var(--el-shadow-color);
        position: relative;

        :deep(.github-markdown-body) {
            padding: 0;
            font-size: 14px;
        }

        .content-fixed-tr {
            position: absolute;
            top: 0;
            right: 0;

            .copy-btn {
                margin-right: -20px;
                padding-left: 20px;
                margin-top: -5px;
            }
        }

        .content-fixed-tl {
            position: absolute;
            top: 0;
            left: 0;

            .copy-btn {
                margin-left: -20px;
                padding-right: 20px;
                margin-top: -5px;
            }
        }
    }

}
</style>