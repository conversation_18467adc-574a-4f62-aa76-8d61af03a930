<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchForm" :model="searchForm" class="header-form">
          <el-form-item label="名称">
            <el-input
                v-model="searchForm.name"
                placeholder="请输入题型名称"
                clearable
                style="width: 220px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="loadData">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="success" @click="openDialog()">新增</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <!-- 表格 -->
    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          empty-text="无数据"
      >
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="name" label="题型名称" width="240" />
        <el-table-column prop="description" label="描述">
          <template #default="{ row }">
            <el-tooltip effect="dark" :content="row.description || ''" placement="top">
              <span>{{ (row.description || '').length > 40 ? (row.description || '').slice(0, 40) + '...' : (row.description || '-') }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="默认模型" width="280">
          <template #default="{ row }">
            <span v-if="!row.defaultModelRequestId">-</span>
            <span v-else class="simple-link" @click="openModel(row.defaultModelRequestId)">
              {{ getModelName(row.defaultModelRequestId) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="200" />
        <el-table-column prop="updateTime" label="更新时间" width="200" />
        <el-table-column label="操作" fixed="right" width="220">
          <template #default="{ row }">
            <el-link type="primary" @click="openDialog(row)">编辑</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
        :title="isEdit ? '编辑题型' : '新增题型'"
        v-model="dialogVisible"
        width="700px"
    >
      <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogRules" label-width="140px">
        <el-form-item label="题型名称" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入题型名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dialogForm.description" placeholder="请输入描述" />
        </el-form-item>

        <el-divider content-position="left">默认模型绑定</el-divider>
        <el-form-item label="默认模型">
          <div style="display:flex; gap:8px; width:100%;">
            <el-autocomplete
                v-model="dialogForm.defaultModelLabel"
                :fetch-suggestions="fetchModelSuggestions"
                placeholder="输入模型名称关键字搜索"
                style="flex:1;"
                @select="onModelSelected"
                clearable
                @clear="clearSelectedModel"
            />
            <el-button @click="clearSelectedModel">清除绑定</el-button>
          </div>
          <div v-if="dialogForm.defaultModelRequestId" class="desc-id">
            当前绑定ID：{{ dialogForm.defaultModelRequestId }}
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submitDialog">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'QuestionTypeManager',
  data() {
    return {
      searchForm: { name: '' },
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      loading: false,

      dialogVisible: false,
      isEdit: false,
      dialogLoading: false,
      dialogFormRef: null,
      dialogForm: {
        id: null,
        name: '',
        description: '',
        defaultModelRequestId: null,
        defaultModelLabel: '' // 仅用于展示/搜索
      },
      dialogRules: {
        name: [{ required: true, message: '题型名称不能为空', trigger: 'blur' }]
      },

      // 模型信息缓存：{ [id]: { id, name, modelValue, ... } }
      modelCache: {}
    };
  },
  methods: {
    goBack() { this.$router.back(); },

    // 加载列表（兼容空结果）
    loadData() {
      this.loading = true;
      this.$axios.post('/api/questionType/list', null, {
        params: {
          name: this.searchForm.name || '',
          pageNo: this.pageNumber,
          pageSize: this.pageSize
        }
      }).then(res => {
        const d = res?.data || {};
        this.tableData = d.records || [];
        this.total = d.total || 0;
        // 预加载默认模型名称（可选，懒加载也可）
        const ids = Array.from(new Set((this.tableData || [])
            .map(r => r.defaultModelRequestId)
            .filter(Boolean)));
        ids.forEach(id => this.ensureModelLoaded(id));
      }).finally(() => {
        this.loading = false;
      });
    },

    reset() {
      this.searchForm = { name: '' };
      this.pageNumber = 1;
      this.loadData();
    },

    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },

    // 打开新增/编辑
    openDialog(row = null) {
      if (row) {
        this.isEdit = true;
        this.dialogForm = {
          id: row.id,
          name: row.name || '',
          description: row.description || '',
          defaultModelRequestId: row.defaultModelRequestId || null,
          defaultModelLabel: ''
        };
        if (row.defaultModelRequestId) {
          this.ensureModelLoaded(row.defaultModelRequestId).then(m => {
            this.dialogForm.defaultModelLabel = m?.name || m?.label || '';
          }).catch(() => {});
        }
      } else {
        this.isEdit = false;
        this.dialogForm = {
          id: null,
          name: '',
          description: '',
          defaultModelRequestId: null,
          defaultModelLabel: ''
        };
      }
      this.dialogVisible = true;
    },

    // 清除绑定
    clearSelectedModel() {
      this.dialogForm.defaultModelRequestId = null;
      this.dialogForm.defaultModelLabel = '';
    },

    // 远程搜索模型（按名称）
    fetchModelSuggestions(queryString, cb) {
      // 允许空输入时返回最近/热门也行，这里空则返回空
      if (!queryString) {
        cb([]);
        return;
      }
      this.$axios.post('/api/model-request/selectPage', {
        page: { pageNumber: 1, pageSize: 20, searchCount: false },
        name: queryString,
        remark: ''
      }).then(res => {
        const list = res?.data?.records || [];
        const items = list.map(r => ({
          value: r.name, // 展示名
          id: r.id,
          name: r.name,
          modelValue: r.modelValue
        }));
        cb(items);
      }).catch(() => cb([]));
    },

    // 选择了某个模型
    onModelSelected(item) {
      this.dialogForm.defaultModelRequestId = item.id;
      this.dialogForm.defaultModelLabel = item.name || item.value || '';
      // 写入缓存
      this.modelCache[item.id] = { id: item.id, name: item.name || item.value, modelValue: item.modelValue };
    },

    // 懒加载模型详情（仅取名称用于展示）
    async ensureModelLoaded(id) {
      if (!id) return null;
      if (this.modelCache[id]) return this.modelCache[id];
      try {
        const res = await this.$axios.get('/api/model-request/detail', { params: { id } });
        const m = res?.data || {};
        const name = m.name || m.label || `ID#${id}`;
        this.modelCache[id] = { id, name, modelValue: m.modelValue };
        return this.modelCache[id];
      } catch {
        this.modelCache[id] = { id, name: `ID#${id}`, modelValue: '' };
        return this.modelCache[id];
      }
    },

    // 表格显示模型名称
    getModelName(id) {
      if (!id) return '-';
      const m = this.modelCache[id];
      if (m) return m.name || `ID#${id}`;
      // 如果还没加载，触发异步加载，但先返回占位
      this.ensureModelLoaded(id);
      return `ID#${id}`;
    },

    // 点击模型名称（可扩展打开模型详情）
    openModel(id) {
      // 这里留接口：你可以对接现有“模型详情”弹窗组件
      // 例如：this.$refs.modelDetail.show({...})
      this.$message.info(`模型ID：${id}`);
    },

    // 提交
    submitDialog() {
      this.$refs.dialogFormRef.validate(valid => {
        if (!valid) return;
        this.dialogLoading = true;
        const api = this.isEdit ? '/api/questionType/update' : '/api/questionType/add';
        const payload = {
          id: this.dialogForm.id,
          name: this.dialogForm.name,
          description: this.dialogForm.description,
          defaultModelRequestId: this.dialogForm.defaultModelRequestId
        };
        this.$axios.post(api, payload)
            .then(() => {
              this.$message.success(this.isEdit ? '更新成功' : '新增成功');
              this.dialogVisible = false;
              this.loadData();
            })
            .finally(() => { this.dialogLoading = false; });
      });
    },

    // 删除
    onDelete(id) {
      this.$confirm('确认删除该题型？', '警告', { type: 'warning' })
          .then(() => this.$axios.get('/api/questionType/delete', { params: { id } }))
          .then(() => {
            this.$message.success('删除成功');
            this.loadData();
          })
          .catch(() => {});
    }
  },
  created() {
    this.loadData();
  }
};
</script>

<style lang="scss" scoped>
.simple-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
}
.simple-link:hover { color: #40a9ff; }

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content { flex: 1; }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
