<template>
  <div class="page-container">
    <div class="top-area">
      <div class="left-item">
        <el-image src="/logo.png" class="icon"></el-image>
        <el-text class="title">纸质作业留痕批改机器</el-text>
      </div>
      <div class="right-item">
        <el-image src="/icon/miaomiaoCaoZuo.png" class="icon" style="cursor: pointer;"
          @click="redirectToExternalLink"></el-image>
        <SchoolSelector style="margin-left: 20px" />
        <el-image src="/icon/avatar.png" class="avatar"></el-image>
        <div v-if="getUser" class="name">
          <el-dropdown @command="onUserActions">
            <el-text type="primary" style="font-size: 18px; font-weight: 700; cursor: pointer;">{{
              getUser.username
            }}
            </el-text>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">{{ $t("common.logout") }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div v-else class="name">
          <el-button type="primary" text="primary" style="font-size: 18px;" link @click="doLogin">{{
            $t('login.login')
            }}
          </el-button>
        </div>
      </div>
    </div>

    <div class="middle-area">
      <div class="top-item">
        <el-text class="title">
          <span class="left-text">纸质作业留痕</span>
          <span class="right-text">批改机器</span>
        </el-text>
        <el-text class="subtitle">1分钟自动批改100份纸质作业<br>
          大模型技术——AI全自动批改成为现实
        </el-text>
      </div>

      <div class="middle-item">
        <div class="item" @click="$router.push('/markPapers')">
          <el-text class="title">新建批改</el-text>
          <el-text class="subtitle">开始新建批改试卷</el-text>
          <el-image src="/icon/homeMiddleIcon1.png" class="icon-big"></el-image>
        </div>

        <div class="item" style="background-image: linear-gradient(180deg, #EBF4FF 0%, #D1E5FF 50%, #B9D7FF 100%);"
          @click="$router.push('/correctConfigPackages')">
          <el-text class="title">配置标准卷</el-text>
          <el-text class="subtitle">处理相关配套试卷</el-text>
          <el-image src="/icon/homeMiddleIcon2.png" class="icon-big"></el-image>
        </div>

        <div class="item" style="background-image: linear-gradient(180deg, #F8F9FF 0%, #E6E9FE 100%);"
          @click="$router.push('/markPapers')">
          <el-text class="title">学情统计</el-text>
          <el-text class="subtitle">学情统计</el-text>
          <el-image src="/icon/homeMiddleIcon3.png" class="icon-little"></el-image>
        </div>

        <div class="item" style="background-image: linear-gradient(180deg, #FFFDF5 0%, #F9F3D4 100%);"
          @click="$router.push('/class/config')">
          <el-text class="title">班级配置</el-text>
          <el-text class="subtitle">管理班级相关配置</el-text>
          <el-image src="/icon/homeMiddleIcon4.png" class="icon-little"></el-image>
        </div>
      </div>

      <div class="bottom-item">
        <div class="item" @click="$router.push('/markPapers')">
          <el-text class="title">题库</el-text>
          <el-text class="subtitle">查看自己所配置的题库</el-text>
        </div>

        <div class="item" @click="$router.push('/markPapers')">
          <el-text class="title">组卷</el-text>
          <el-text class="subtitle">查看自己所创建的组卷</el-text>
        </div>

        <div class="item" @click="$router.push('/markPapers')">
          <el-text class="title">高级功能</el-text>
          <el-text class="subtitle">进行高级功能管理设置</el-text>
        </div>
      </div>
    </div>
    <page-footer class="page-footer" />
    <el-image class="bottom-right" src="/icon/homeBottom.png" />
  </div>
</template>

<script>
import pageFooter from '../../components/layout/page-footer.vue';
import {mapState, mapActions} from "pinia";
import { useUserStore } from "@/store";
import SchoolSelector from '../../components/SchoolSelector.vue';
import {refreshConfig} from "../../utils/refreshConfig";

export default {
  components: {
    pageFooter,
    SchoolSelector
  },
  data() {
    return {
      asideMenuItems: [
        // { index: '/review', label: '作业批改' },
        {
          index: '/markPapers',
          label: '批改试卷',
          t: 'menu.markingExamPapers',
          icon: 'DocumentAdd',
          subtitle: '处理相关配套试卷'
        },
        {
          index: '/correctConfigPackages',
          label: '配置样卷',
          t: 'menu.processingSampleRolls',
          icon: 'EditPen',
          subtitle: '处理相关试卷配置'
        },
        {index: '/stats', label: "统计数据", t: 'menu.stats', icon: 'DataLine', subtitle: '网站数据统计显示'},
        {index: '/docfile', label: '快速批改', t: 'menu.fastReview', icon: 'Edit', subtitle: '快速发起批改试卷'},
        {index: '/doccorrect', label: '试卷批改', t: 'menu.docReview', icon: 'Document', subtitle: '处理相关单个试卷'},
        {index: '/solve', label: '模型评估', t: "menu.modelEvaluation", icon: 'PieChart', subtitle: '进行模型对比分析'},
      ],
    }
  },
  computed: {
    ...mapState(useUserStore, ['getUser']),
  },
  mounted() {
    this.$refreshConfig();
  },
  methods: {
    ...mapActions(useUserStore, ['setUser', 'setDefaultConfigs']),
    redirectToExternalLink() {
      window.location.href = 'https://scnduqrj7624.feishu.cn/wiki/LTCmwHSp1ieUNAk600icuuJFnch';
    },
    doLogin() {
      this.$router.push({path: '/login'})
    },
    doLogout() {
      this.$axios.post("/api/logout").then(res => {
        this.$message.success(this.$t('login.logoutSuccess'))
        this.setUser(null)
        this.$router.push("/login")
      })
    },
    changeCollapse(val) {
      this.isCollapse = val
    },
    onUserActions(command) {
      if (command === 'logout') {
        this.doLogout()
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url("https://pic2.ziyuan.wang/user/tanfuhua/2024/12/homeBac_0ae30efcfe37a.png");
  background-size: 100% 100%;
  width: 100vw;
  height: 100vh;

  .top-area {
    width: 100vw;
    height: 62px;
    padding: 14px 26px;
    justify-content: space-between;
    display: flex;
    align-items: center;
    .left-item {
      display: flex;

      .icon {
        width: 40px;
        height: 40px;
      }

      .title {
        font-weight: 700;
        font-size: 18px;
        color: #333333;
        letter-spacing: 0;
        margin-left: 11px;
      }
    }

    .right-item {
      display: flex;
      align-items: center;

      .icon {
        width: 194.75px;
        height: 30px;
        align-items: center;
      }

      .avatar {
        width: 26px;
        height: 26px;
        border-radius: 50%;
        margin-left: 42px;
      }

      .name {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        margin-left: 4.5px;
      }
    }
  }

  .middle-area {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    margin-top: 10vh;
    .top-item {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      width: 764px;
      height: 181px;
      background: url("/icon/homeTop.png");
      background-size: 100% 100%;
      padding: 33px 38px;
      .title {
        width: 100%;
        .left-text {
          font-weight: bolder;
          font-size: 32px;
          color: #FFFFFF;
          letter-spacing: 2px;
        }
        .right-text {
          font-weight: bolder;
          font-size: 32px;
          letter-spacing: 2px;
          background-image: linear-gradient(to bottom, #FAFEC5, #FAE980);
          background-clip: text;
          -webkit-background-clip: text; /* 兼容Webkit内核浏览器 */
          color: transparent;
          margin-left: 2px;
        }
      }

      .subtitle {
        width: 100%;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        letter-spacing: 0;
        line-height: 28px;
        margin-top: 11px;
      }
    }

    .middle-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 174px;
      width: 764px;
      gap: 12px;
      margin-top: 21.25px;
      .item {
        cursor: pointer;
        width: 183px;
        height: 174px;
        background-image: linear-gradient(180deg, #EFFFE1 0%, #CFE7BE 59%, #BBE3A8 100%);
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        padding: 29.5px 0 0 16px;
        .title {
          width: 100%;
          font-weight: bolder;
          font-size: 16px;
          color: #333333;
          letter-spacing: 0;
        }
        .subtitle {
          width: 100%;
          font-weight: 400;
          font-size: 13px;
          color: #999999;
          letter-spacing: 0;
        }
        .icon-big {
          position: relative;
          width: 93.89px;
          height: 83px;
          top: 15px;
          left: 70px;
          float: right;
        }
        .icon-little {
          width: 28px;
          height: 26px;
          top: 55px;
          left: 120px;
        }
      }
    }

    .bottom-item {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      width: 764px;
      height: 101px;
      gap: 7px;
      margin-top: 20px;
      .item {
        cursor: pointer;
        width: 250px;
        height: 101px;
        background-image: linear-gradient(180deg, #FAFAFA 0%, #DDDDDD 100%);
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        padding: 16px 18px;
        .title {
          width: 100%;
          font-weight: bolder;
          font-size: 16px;
          color: #333333;
          letter-spacing: 0;
        }
        .subtitle {
          width: 100%;
          font-weight: 400;
          font-size: 13px;
          color: #999999;
          letter-spacing: 0;
          margin-top: 4px;
        }
      }
    }
  }

  .page-footer {
    position: fixed;
    bottom: 10px;
  }
  .bottom-right {
    position: fixed;
    bottom: 58px;
    width: 320px;
    height: 67.43px;
    right: 25px;
  }
}
</style>