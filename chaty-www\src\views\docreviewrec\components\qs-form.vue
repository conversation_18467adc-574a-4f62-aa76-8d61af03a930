<template>
    <el-dialog :model-value="isShow" :title="$t(title)" width="600" :before-close="() => beforeClose()">
        <el-form :model="form" label-width="150px">
            <el-form-item :label="`${$t('common.question')}：`" prop="name" :style="{ width }">
                <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item :label="`${$t('common.problem')}：`" prop="question" :style="{ width }">
                <el-input type="textarea" rows="5" v-model="form.question" />
            </el-form-item>
            <el-form-item :label="`${$t('common.correctAnswer')}：`" prop="correctAnswer" :style="{ width }">
                <el-input type="textarea" rows="5" v-model="form.correctAnswer" />
            </el-form-item>
            <el-form-item :label="`${$t('common.isTrue')}：`" prop="isTrue" :style="{ width }">
                <el-select v-model="form.isTrue">
                    <el-option :label="正确" :value="true"></el-option>
                    <el-option :label="错误" :value="false"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item :label="`${$t('common.evaluation')}：`" prop="review" :style="{ width }">
                <el-input type="textarea" rows="5" v-model="form.review" />
            </el-form-item>
            <el-form-item :label="`${$t('common.tag')}：`" prop="tag" :style="{ width }">
                <el-input v-model="form.tag" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onsubmit">{{ $t('common.confirm') }}</el-button>
                <el-button type="primary" @click="beforeClose">{{ $t('common.cancel') }}</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<script>
export default {
    data() {
        return {
            title: "common.edit",
            isShow: false,
            form: {
                name: "",
                question: "",
                correctAnswer: "",
                isTrue: false,
                review: "",
                tag: "",
            },
            qsIdx: 0,
            row: {},
        }
    },
    methods: {
        show({ row, qsIdx }) {
            this.isShow = true
            this.form = Object.assign(this.form, row.reviewedObj[qsIdx])
            this.row = row
            this.qsIdx = qsIdx
        },
        beforeClose(isSubmit) {
            this.isShow = false
            this.form = {
                name: "",
                question: "",
                correctAnswer: "",
                isTrue: false,
                review: "",
                tag: "",
            }
            this.row = {}
            this.qsIdx = 0
            this.$emit("onClose", isSubmit)
        },
        onsubmit() {
            let reviewed = JSON.parse(this.row.reviewed)
            reviewed[this.qsIdx].status = true
            reviewed[this.qsIdx].name = this.form.name
            reviewed[this.qsIdx].question = this.form.question
            reviewed[this.qsIdx].correctAnswer = this.form.correctAnswer
            reviewed[this.qsIdx].review = {
                isTrue: this.form.isTrue,
                review: this.form.review
            }
            reviewed[this.qsIdx].tag = this.form.tag
            let formData = {
                id: this.row.docReviewId,
                reviewed: JSON.stringify(reviewed)
            }
            this.$axios.post("/api/docreview/update", formData).then(res => {
                this.$message.success(this.$t('common.updateSuccess'))
                this.beforeClose(true)
            })
        }
    }
}
</script>
<style lang="scss" scoped></style>