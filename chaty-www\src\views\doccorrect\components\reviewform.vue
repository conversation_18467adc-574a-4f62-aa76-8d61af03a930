<template>
    <el-dialog v-model="isShow" :title="title" width="500" :before-close="onClose" append-to-body>
        <el-form :model="form" :label-width="100">
            <el-form-item label="名称：" prop="name">
                <el-input v-model="form.name" disabled />
            </el-form-item>
            <el-form-item label="是否正确：">
                <el-switch v-model="form.isCorrect" active-value="Y" inactive-value="N" active-text="正确"
                    inactive-text="错误"></el-switch>
            </el-form-item>
            <el-form-item label="分数：">
                <el-input-number v-model="form.scored" :controls="false"
                    :min="0" :max="form.score" />
            </el-form-item>
            <el-form-item label="学生答案：" prop="studentAnswer">
                <el-input type="textarea" v-model="form.studentAnswer"></el-input>
            </el-form-item>
            <el-form-item label="评价：" prop="review">
                <el-input type="textarea" v-model="form.review"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" @click="onClose">取消</el-button>
            <el-button type="primary" @click="onSubmit" :loading="submitLoading">确认</el-button>
        </template>
    </el-dialog>
</template>
<script>
export default {
    data() {
        return {
            isShow: false,
            title: "问题",
            form: {
                name: "",
                isCorrect: 'N',
                studentAnswer: "",
                review: ""
            },
            submitLoading: false,
        }
    },
    methods: {
        show({ data }) {
            this.isShow = true;
            if (data) {
                this.form = Object.assign(this.form, data)
            }
        },
        onClose() {
            this.isShow = false
            this.form = {
                name: "",
                isCorrect: 'N',
                studentAnswer: "",
                review: ""
            }
            this.submitLoading = false
            this.$emit("onClose");
        },
        onSubmit() {
            this.$emit("updateQs", this.form)
        },
    }
}
</script>
<style lang="sass">
    
</style>