<template>
    <div class="main-wrapper">
        <div class="header-bar">
            <el-button class="header-action" text icon="Back" @click="goBack">Back</el-button>
            <el-input class="filter-item" v-model="filter.name" placeholder="请输入名称" />
            <el-select class="filter-item" v-model="filter.status" placeholder="请选择状态" clearable></el-select>
            <el-button type="primary" @click="loadData">查询</el-button>
            <div class="right" />
            <el-button type="primary" class="header-action" icon="Setting" @click="toConfig()">试卷配置</el-button>
            <template v-if="corrected">
                <el-button class="header-action" type="primary" icon="Download" @click="onStatsDownloadSubmit">统计结果</el-button>
                <el-button class="header-action" type="primary" icon="Download" @click="onDownloadSubmit()">批改含原卷</el-button>
                <el-button class="header-action" type="primary" icon="Download" @click="onDownloadSubmit({isPreview: false})">批改不含原卷</el-button>
            </template>
        </div>
        <div class="main-content">
            <el-table v-loading="taskLoading" :data="tasks" style="height: 100%" empty-text="无数据" border>
                <el-table-column v-for="column in columns" :key="column.prop" v-bind="column" align="center">
                    <template v-if="column.prop === 'operations'" v-slot="scope">
                        <el-space :size="5">
                            <el-button v-if="scope.row.status !== 2" type="primary" text size="small"
                                @click="correctTask(scope.row.id)">批改</el-button>
                            <el-button v-if="scope.row.status === 3" type="primary" text size="small"
                                :loading="resultSaving[scope.$index]"
                                @click="saveResult(scope.row.id, scope.$index)">保存结果</el-button>
                            <el-dropdown v-if="scope.row.status !== 2" size="small" @command="(cmd) => doCorrectName(cmd, scope.row.id)">
                                <el-button type="primary" text size="small"
                                    @click="correctName(scope.row.id)">
                                    姓名校正
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="sync">同步姓名</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                            <el-button type="primary" text size="small" @click="onEditTask(scope.row)">编辑</el-button>
                            <el-button type="primary" text size="small" @click="onDeleteTask(scope.row.id)">删除</el-button>
                        </el-space>
                    </template>
                    <template v-else-if="column.prop === 'record'" v-slot="scope">
                        <el-link type="primary" @click="toRecord(scope.row.id)">查看</el-link>
                    </template>
                    <template v-else-if="column.prop === 'status'" v-slot="scope">
                        <el-tag :type="statusOptions[scope.row.status].type">{{ statusOptions[scope.row.status].label
                            }}</el-tag>
                    </template>
                    <template v-else-if="column.prop === 'configName'" v-slot="scope">
                        <el-link v-if="scope.row.configId" type="primary" @click="toConfig(scope.row.configId)">
                            {{ scope.row.configName }}</el-link>
                    </template>
                    <template v-else-if="column.prop === 'progress'" v-slot="scope">
                        <el-text v-if="scope.row.status === 2">{{ `${scope.row.finishedCount ||
                            0}/${scope.row.recordCount || 0}` }}</el-text>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <task-form ref="taskForm" @onClose="loadData" />
    </div>
</template>

<script>
import { useUserStore } from "@/store/index";
import { mapState } from "pinia";
import TaskForm from './components/taskform.vue';

export default {
    components: {
        TaskForm
    },
    data() {
        return {
            fileId: this.$route.params.fileId,
            filter: {
                name: "",
                status: "",
            },
            tasks: [],
            columns: [
                { label: "名称", prop: "name" },
                { label: "试卷配置", prop: "configName" },
                { label: "试卷", prop: "record" },
                { label: "状态", prop: "status", width: 200 },
                { label: "进度", prop: "progress", width: 200 },
                { label: "操作", prop: "operations", width: 320 },
            ],
            taskLoading: false,
            statusOptions: {
                1: { value: 1, label: "未批改" },
                2: { value: 2, label: "批改中", type: "warning" },
                3: { value: 3, label: "批改完成", type: "success" },
            },
            refresher: null,
            statusStats: {
                1: 0,
                2: 0,
                3: 0,
                total: 0,
            },
            downloading: false,
            statsDownloading: false,
            resultSaving: {},
            timer: null,
            file: null,
        }
    },
    created() {
        this.loadData();
        this.loadFile();
        this.timer = setInterval(() => {
            this.loadData();
        }, 10 * 1000);
    },
    beforeUnmount() {
        if (this.timer) {
            clearInterval(this.timer);
        }
        this.timer = null;
    },
    computed: {
        ...mapState(useUserStore, ["getUser"]),
        corrected() {
            if (this.tasks.length > 0) {
                return !this.tasks.find(item => item.status !== 3)
            }
            return false
        }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            vm.loadData()
        })
    },
    methods: {
        onEditTask(data) {
            this.$refs.taskForm.show({ title: "编辑", data });
        },
        onDeleteTask(id) {
            this.$confirm("确认删除该任务吗？", "", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.$axios.post(`/api/docCorrectTask/delete?id=${id}`).then(res => {
                    this.$message.success("删除成功");
                    this.loadData();
                });
            });
        },
        loadData() {
            this.taskLoading = true
            let form = {
                page: {
                    pageNumber: 1,
                    pageSize: -1,
                },
                countRecord: true,
                fileId: this.fileId,
                orderByName: true
            }
            form = Object.assign(form, this.filter);
            this.$axios.post("/api/docCorrectTask/page", form).then(res => {
                this.tasks = res.data.records;
            }).finally(() => {
                this.taskLoading = false;
            })
        },
        toRecord(id) {
            this.$router.push(`/docrecord/${id}`);
        },
        toConfig(configId) {
            let query = {}
            if (configId) {
                query.id = configId
            }
            this.$router.push({
                name: "docconfig",
                query,
            });
        },
        correctTask(taskId) {
            const store = useUserStore();
            let form = {
                id: taskId,
                aimodel: store.getDefaultCorrectModal,
                ocrType: '2',
                responseFormat: true,
                jsonobject: store.getDefaultJsonObject,
                jsonschema: store.getDefaultJsonSchema
            }
            this.$axios.post("/api/docCorrectTask/execute", form).then(res => {
                this.$message.success("提交成功")
                this.loadData()
            })
        },
        correctName(id) {
            this.$axios.post("/api/docCorrectTask/correctName", {
                id,
            }).then(res => {
                this.$message.success("校正中，请稍后查看...")
            })
        },
        doCorrectName(command, id) {
            if (command === 'sync') {
                // 同步另一张试卷的姓名
                this.$refs.taskSelector.show({ title: "同步试卷姓名", data: {
                    id,
                }})
            }
        },
        doSyncDocName(formData) {
            this.$message.success("同步中，请稍后查看...")
            this.$axios.post("/api/docCorrectTask/syncDocName", formData).then(() => {
                this.$message.success("同步成功")
            })
        },
        saveResult(taskId, rowIdx) {
            this.resultSaving[rowIdx] = true
            this.$axios.post(`/api/docCorrectResult/saveTask?taskId=${taskId}`).then(res => {
                this.$message.success("保存成功")
            }).finally(() => {
                this.resultSaving[rowIdx] = false
            })
        },
        goBack() {
            this.$router.back();
        },
        onStatsDownloadSubmit() {
            let form = {
                taskIds: this.tasks.map(task => task.id),
                fontSize: 20,
            }
            let loadingMessage = this.$message({
                message: "正在生成统计结果...",
                icon: "Loading",
                type: "warning",
                duration: 0,
            })
            this.$axios.post("/api/docCorrectTask/download/stats", form).then(res => {
                return this.downloadFile(res.data.fileUrl, `${this.file?.name + " " || ""}统计结果.pdf`);
            }).finally(() => {
                loadingMessage.close()
            })
        },
        downloadFile(url, name) {
            // 使用fetch获取文件内容
            return fetch(this.$fileserver.fileurl(url))
                .then(response => response.blob())
                .then(blob => {
                    // 如果需要下载，可以使用前面提到的下载代码
                    const a = document.createElement("a");
                    a.style.display = "none";
                    a.href = URL.createObjectURL(blob);
                    a.download = name;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                })
                .catch(error => {
                    console.error('发生错误:', error);
                });
        },
        onDownloadSubmit(options) {
            let form = {
                isPreview: true,
                scoreMerge: true,
                showQsScore: true,
                taskIds: this.tasks.map(task => task.id)
            }
            Object.assign(form, options);
            let loadingMessage = this.$message({
                message: "正在生成批改结果...",
                icon: "Loading",
                type: "warning",
                duration: 0,
            })
            this.$axios.post("/api/docCorrectTask/download/reviewed", form).then(res => {
                return this.downloadFile(res.data.fileUrl, `${this.file?.name + " " || ""}批改结果(${form.isPreview ? '含原卷' : '不含原卷'}).pdf`);
            }).finally(() => {
                loadingMessage.close()
            })
        },
        loadFile() {
            if (!this.fileId) {
                return
            }
            this.$axios.get(`/api/docCorrectFile/getById?id=${this.fileId}`).then(res => {
                this.file = res.data
            })
        }
    },
} 
</script>

<style lang="scss" scoped>
.main-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header-bar {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .filter-item {
            margin-right: 10px;
            width: 200px;
        }

        .right {
            margin-left: auto !important;
        }

        .header-action {
            margin-right: 10px;

            :deep(.el-upload) {
                display: inline-block;
            }

            &.header-stats {
                width: 60px;
                text-align: center;
            }
        }

        .el-button+.el-button {
            margin-left: 0;
        }
    }

    .main-content {
        flex: 1;
    }

    .footer-bar {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
}
</style>