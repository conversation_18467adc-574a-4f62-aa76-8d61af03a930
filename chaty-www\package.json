{"name": "chaty-www", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build && node scripts/generate-version.js", "build:hk": "vite build --mode hk", "build:new": "vite build --mode new --base=/new/", "preview": "vite preview", "deploy:cloudbase": "cloudbase hosting:deploy dist", "update": "node update.js"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@kangc/v-md-editor": "next", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^13.1.0", "axios": "^1.5.0", "canvas-confetti": "^1.9.3", "cropperjs": "^1.6.1", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.3.12", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jszip": "^3.10.1", "latex.js": "^0.12.6", "pdfjs-dist": "^2.16.105", "pinia": "^2.1.7", "swiper": "^11.1.15", "vue": "^3.3.4", "vue-draggable-next": "^2.2.1", "vue-i18n": "^9.10.1", "vue-router": "^4.2.4", "vue3-json-viewer": "^2.2.2", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@types/canvas-confetti": "^1.9.0", "@types/vue3-json-viewer": "^2.2.0", "@vitejs/plugin-vue": "^4.3.1", "sass": "^1.66.1", "vite": "^4.4.9", "vite-svg-loader": "^5.1.0"}}