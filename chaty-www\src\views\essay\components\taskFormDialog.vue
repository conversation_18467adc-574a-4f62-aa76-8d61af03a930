<template>
  <el-dialog v-model="isShow" :title="title" width="1000" :before-close="onClose">
    <task-form :initialData="form"
               :loadConfigs="loadConfigs"
               :submitLoading="submitLoading"
               @submit="onSubmit" />
    <template #footer>
      <el-button type="warning" @click="onClose">取消</el-button>
      <el-button type="primary" @click="onSubmit" :loading="submitLoading">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import TaskForm from './taskform.vue';

export default {
  components: {
    TaskForm
  },
  data() {
    return {
      isShow: false,
      title: "",
      form: {
        name: "",
        configId: "",
        grade: "",
        essayType: "unit",
        questionType: "topic",
        essayTitle: "",
        scoringScheme: "A/B/C",
        essayStyle: "argumentative",
        wordCount: 0,
        materials: "",
      },
      isEdit: true,
      submitLoading: false,
    };
  },
  methods: {
    show({title, data}) {
      this.isShow = true;
      this.title = title;
      this.form = {...data};
    },
    onClose() {
      this.isShow = false;
      this.$emit('close');
    },
    onSubmit(form) {
      this.submitLoading = true;
      const url = this.form.id ? "/api/docCorrectTask/update" : "/api/docCorrectTask/add";
      const method = this.form.id ? 'post' : 'post';
      this.$axios[method](url, form).then(() => {
        this.submitLoading = false;
        this.$message.success(this.form.id ? '更新成功' : '新增成功');
        this.onClose();
      }).catch(() => {
        this.submitLoading = false;
        this.$message.error('操作失败');
      });
    },
    loadConfigs(query) {
      if (query === '') return;
      this.$emit('loadConfigs', query); // 将加载配置的请求转发到父组件
    },
  },
};
</script>
