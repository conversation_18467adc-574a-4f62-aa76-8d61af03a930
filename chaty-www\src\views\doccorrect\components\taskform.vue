<template>
    <el-dialog v-model="isShow" :title="title" width="500" :before-close="onClose">
        <el-form :model="form" :label-width="100">
            <el-form-item label="名称：" prop="name">
                <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="试卷配置：" prop="configId">
                <el-select v-model="form.configId" filterable remote
                    :loading="configLoading"
                    :remote-method="loadConfigs"
                    placeholder="请选择试卷配置" >
                    <el-option
                        v-for="item in configOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="warning" @click="onClose">取消</el-button>
            <el-button type="primary" @click="onSubmit" :loading="submitLoading">确认</el-button>
        </template>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            isShow: false,
            title: "",
            id: "",
            form: {
                name: "",
                configId: "",
            },
            submitLoading: false,
            configOptions: [],
            configLoading: false,
        }
    },
    methods: {
        show({ title, data }) {
            this.isShow = true;
            this.title = title;
            if (data) {
                this.id = data.id
                this.form = Object.assign(this.form, data)
                if (data.configId && data.configId !== '') {
                    this.configOptions = [{ id: data.configId, name: data.configName }]
                }
            }
        },
        onClose() {
            this.isShow = false
            this.form = {
                name: "",
                configId: "",
            }
            this.id = ""
            this.submitLoading = false
            this.title = ""
            this.$emit("onClose");
        },
        onSubmit() {
            this.submitLoading = true
            if (this.id) {
                this.$axios.post("/api/docCorrectTask/update", this.form).then(res => {
                    this.submitLoading = false
                    this.$message.success("更新成功")
                    this.onClose()
                })
            } else {
                this.form.status = 1
                this.$axios.post("/api/docCorrectTask/add", this.form).then(res => {
                    this.submitLoading = false
                    this.$message.success("新增成功")
                    this.onClose()
                })
            }
        },
        loadConfigs(query) {
            if (query === '') {
                return
            }

            this.configLoading = true
            this.$axios.post("/api/docCorrectConfig/page", {
                name: query,
                page: {
                    pageNumber: 1,
                    pageSize: 10,
                    searchCount: false,
                }
            }).then(res => {
                this.configOptions = res.data.records
            }).finally(() => {
                this.configLoading = false
            })
        }
    }
}
</script>

<style lang="scss" scoped></style>