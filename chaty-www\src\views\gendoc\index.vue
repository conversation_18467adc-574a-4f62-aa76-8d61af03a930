<template>
    <div class="container-wrapper">
        <el-card class="left-bar">
            <template #header>
                <div class="card-header">
                    <span>题库</span>
                    <el-button text size="small" plain icon="Plus" @click="addQuestion">新增</el-button>
                </div>
            </template>
            <div class="card-content">
                <doc-list ref="doclist" @onEdit="edieQuestion" @onCheck="onQuestionCheck" v-show="left === 1"></doc-list>
                <question-form ref="questionForm" @onClose="onQuestionFormClose" v-show="left === 2"></question-form>
            </div>
        </el-card>
        <el-card class="right-content">
            <template #header>
                <div class="card-header">
                    <span>试卷</span>
                    <el-button type="primary" size="small" icon="Document" @click="docGenerate">生成试卷</el-button>
                </div>
            </template>
            <div class="card-content">
                <doc-page ref="docpage"></doc-page>
            </div>
        </el-card>
    </div>
</template>

<script>
import DocList from './components/doclist.vue'
import QuestionForm from './components/question-form.vue'
import DocPage from './components/docpage.vue'
export default {
    components: {
        DocList,
        QuestionForm,
        DocPage
    },
    data() {
        return {
            left: 1,
        }
    },
    methods: {
        addQuestion() {
            this.$refs.questionForm.show({
                title: '新增题目',
            })
            this.left = 2
        },
        edieQuestion(question) {
            this.$refs.questionForm.show({
                title: `编辑题目 ${question.id}`,
                data: question
            })
            this.left = 2
        },
        onQuestionFormClose() {
            this.left = 1
            this.$refs.doclist.loadQuestions()
        },
        onQuestionCheck(question) {
            this.$refs.docpage.add(question)
        },
        docGenerate() {
            this.$refs.docpage.docGenerate()
        }
    }
}
</script>

<style lang="scss" scoped>
.container-wrapper {
    width: auto !important;
    display: flex;
    column-gap: 20px;

    .left-bar {
        width: 500px;
        height: calc(100vh - 100px);
    }

    .right-content {
        flex: 1;
        height: calc(100vh - 100px);
    }

    .card-header {
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .card-content {
        height: 100%;
    }

    :deep(.el-card__body) {
        height: calc(100% - 66.8px);
    }

}
</style>