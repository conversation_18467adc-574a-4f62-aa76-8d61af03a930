<template>
    <div class="content-wrapper">
        <div class="search-bar">
            <el-input class="search-input" v-model="query" @change="loadQuestions" placeholder="请输入" suffix-icon="Search" />
        </div>
        <el-scrollbar class="card-list" v-loading="loading">
            <el-card class="doc-card" v-for="question in questions" :key="question.id">
                <template #header>
                    <div class="card-header">
                        <span>{{ `题目 ${question.id}` }}</span>
                        <el-button style="margin-left: auto;" text size="small" plain icon="Edit"
                            @click="$emit('onEdit', question)"></el-button>
                        <el-popconfirm title="确认删除?" confirm-button-text="确认" cancel-button-text="取消"
                            @confirm="doDelete(question.id)">
                            <template #reference>
                                <el-button text size="small" plain icon="Delete"></el-button>
                            </template>
                        </el-popconfirm>
                        <el-button text size="small" plain icon="Check" @click="$emit('onCheck', question)"></el-button>
                    </div>
                </template>
                <div class="card-content">
                    <el-text class="content-label" type="primary" tag="p">题目：</el-text>
                    <el-text class="content-text" tag="p">{{ question.question }}</el-text>
                    <el-text class="content-label" type="primary" tag="p">正确答案：</el-text>
                    <el-text class="content-text" tag="p">{{ question.correctAnswer }}</el-text>
                    <el-text class="content-label" type="primary" tag="p">
                        高度：
                        <el-text class="content-text">{{ question.height }}</el-text>
                    </el-text>
                    
                </div>
            </el-card>
            <el-empty v-show="!questions || questions.length === 0" description="请添加问题" />
        </el-scrollbar>
    </div>
</template>

<script>
export default {
    data() {
        return {
            query: '',
            questions: [],
            loading: false,
        }
    },
    created() {
        this.loadQuestions();
    },
    methods: {
        loadQuestions() {
            this.loading = true
            let form = {}
            if (this.query && this.query !== '') {
                form.query = this.query
            }
            this.$axios.post("/api/doc/question/list", form).then(res => {
                this.questions = res.data
            }).finally(() => {
                this.loading = false
            })
        },
        doDelete(id) {
            this.$axios.get(`/api/doc/question/delete?id=${id}`).then(res => {
                this.$message.success('删除成功')
                this.loadQuestions()
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.content-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-bar {

        .search-input {
            height: 40px;
            line-height: 40px;
        }
    }

    .card-list {
        padding-top: 10px;

        .doc-card {
            margin-bottom: 10px;

            :deep(.el-card__header) {
                padding: 5px 10px;
            }

            .card-header {
                display: flex;
                align-items: center;
            }

            .card-content {
                .content-text {
                    min-height: 30px;
                }
            }
        }
    }
}
</style>