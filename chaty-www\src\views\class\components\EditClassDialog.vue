<template>
  <el-dialog
      v-model="dialogVisible"
      title="修改班级名称"
      :before-close="handleClose"
      width="500px"
      :close-on-click-modal="false"
  >
    <el-form
        :model="form"
        ref="formRef"
        label-width="120px"
        class="form-container"
    >
      <el-form-item
          label="选择班级"
          prop="classId"
          :rules="[{ required: true, message: '请选择班级', trigger: 'change' }]"
      >
        <el-select
            v-model="form.classId"
            placeholder="请选择班级"
            filterable
            clearable
            :loading="loadingClasses"
        >
          <el-option
              v-for="cls in classList"
              :key="cls.classId"
              :label="cls.className"
              :value="cls.classId"
          />
        </el-select>
      </el-form-item>

      <el-form-item
          label="新班级名称"
          prop="newClassName"
          :rules="[
          { required: true, message: '请输入新的班级名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]"
      >
        <el-input
            v-model="form.newClassName"
            placeholder="请输入新的班级名称"
        />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {mapState} from "pinia";
import {useUserStore} from "@/store";
import {ElMessage, ElMessageBox} from "element-plus";

export default {
  name: "EditClassDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      form: {
        classId: "",
        newClassName: "",
      },
      classList: [],
      loadingClasses: false,
      schoolId: null
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.fetchClasses();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit("update:visible", false);
        this.$refs.formRef.resetFields();
      }
    },
  },
  computed: {
    ...mapState(useUserStore, ["getUser"]),
  },
  methods: {
    openDialog(e) {
      this.dialogVisible = true;
      this.schoolId = e;
      this.fetchClasses()
    },
    handleClose() {
      this.dialogVisible = false;
      this.$emit('cancel')
    },
    fetchClasses() {
      this.loadingClasses = true;
      this.$axios
          .get("/api/class/by-school/" + this.schoolId) // 假设有一个获取所有班级的接口
          .then((res) => {
            this.classList = res.data;
          })
          .catch((error) => {
            ElMessage.error("获取班级列表失败");
            console.error(error);
          })
          .finally(() => {
            this.loadingClasses = false;
          });
    },
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          const {classId, newClassName} = this.form;
          ElMessageBox.confirm(
              `确定将班级名称修改为 "${newClassName}" 吗？`,
              "确认修改",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
          )
              .then(() => {
                this.$axios
                    .put(`/api/class/update/${classId}`, {
                      className: newClassName,
                      classId: classId,
                    })
                    .then((res) => {
                      ElMessage.success("班级名称修改成功");
                      this.dialogVisible = false;
                      this.$emit("fetchClasses"); // 通知父组件刷新班级列表
                    })
                    .catch((error) => {
                      ElMessage.error("修改班级名称失败");
                      console.error(error);
                    });
              })
              .catch(() => {
                // 用户取消操作
              });
        } else {
          console.log("表单验证失败");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.form-container {
  padding: 20px;
}

.el-dialog__footer {
  text-align: right;
}

.el-button {
  margin-left: 10px;
}

.el-button[type="primary"] {
  background-color: #3981ff;
  border-color: #3981ff;
}
</style>
