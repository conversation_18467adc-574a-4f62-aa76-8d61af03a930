<template>
    <div class="container-wrapper">
        <el-card class="left-card">
            <template #header>
                <div class="card-header">
                    <span>{{ $t('common.config') }}</span>
                </div>
            </template>
            <div class="card-content">
                <el-form ref="modelForm" class="model-form" :model="model" label-position="top" :rules="modelRules">
                    <el-form-item :label="`${$t('common.bigModel')}：`" prop="aimodel">
                        <el-select v-model="model.aimodel" style="width: 100%;">
                            <el-option label="gpt-4-turbo" value="gpt-4-turbo" />
                            <el-option label="gpt-4" value="gpt-4" />
                            <el-option label="gpt-3.5-turbo" value="gpt-3.5-turbo" />
                            <el-option label="Claude 3 Opus" value="Claude 3 Opus" />
                            <el-option label="Claude 3 Sonnet" value="Claude 3 Sonnet" />
                            <el-option label="qwen-turbo" value="qwen-turbo" />
                            <el-option label="qwen-plus" value="qwen-plus" />
                            <el-option label="qwen-max" value="qwen-max" />
                            <el-option label="ERNIE-Bot" value="ERNIE-Bot" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="`${$t('chat.systemPromot')}：`">
                        <el-input v-model="model.systemMessage" type="textarea" placeholder="请输入系统消息" rows="5" />
                    </el-form-item>
                    <el-form-item label="Temperature：">
                        <el-input-number v-model.number="model.temperature" :min="0" :max="1" :step="0.1" />
                    </el-form-item>
                    <el-form-item :label="`${$t('chat.histiryMessageCount')}：`">
                        <el-input v-model.number="model.historyMessageCount" />
                    </el-form-item>
                    <el-form-item :label="`${$t('chat.historyMessageCompress')}：`">
                        <el-switch v-model="model.compressMessage" />
                    </el-form-item>
                    <el-form-item :label="`${$t('chat.historyMessageCompressThreshold')}：`">
                        <el-input v-model.number="model.compressThreshold" />
                    </el-form-item>
                </el-form>
            </div>
        </el-card>
        <el-card class="main-card" body-style="height: calc(100% - 60px);">
            <template #header>
                <div class="card-header">
                    <span>{{ $t('menu.chat') }}</span>
                    <div>
                        <el-button type="primary" @click="clearChat">
                            {{ $t('chat.clearChat') }}
                        </el-button>
                    </div>
                </div>
            </template>
            <div class="card-content">
                <div class="chat-box">
                    <el-scrollbar height="100%" ref="chatScroll">
                        <chat-dialog class="chat-box-dialog" :dialog="defaultDialog" />
                        <div ref="chatScrollInner">
                            <chat-dialog class="chat-box-dialog" v-for="(dialog, index) in dialogs" :key="index"
                                :copyTr="dialog.role === 'assistant'"
                                :copyTl="dialog.role === 'user'"
                                :dialog="dialog" />
                        </div>
                    </el-scrollbar>
                </div>
                <dic class="chat-input" @keydown="handleKeydown">
                    <custom-input type="textarea" :autosize="{ minRows: 5 }" v-model="inputContent" :disabled="answering">
                        <template #fixed-br>
                            <div class="chat-input-brops">
                                <v-md-preview :text="dialog?.content"></v-md-preview>
                                <el-button class="chat-input-btn" type="primary" @click="submitContent" :loading="answering"
                                    :disabled="answering" icon="Promotion"></el-button>
                            </div>
                        </template>
                        <template #fixed-poper>
                            <transition name="el-fade-in-linear">
                                <div class="chat-input-poper" v-show="answering">
                                    <el-button class="stop-btn" type="primary" plain @click="stopChat">Stop generating</el-button>
                                </div>
                            </transition>
                        </template>
                    </custom-input>
                </dic>
            </div>
        </el-card>
    </div>
</template>

<script>
import { postFetchEventSource } from '@/utils/sse.js'
import CustomInput from '@/components/form/custom-input.vue'
import ChatDialog from './components/chat-dialog.vue'

export default {
    components: {
        CustomInput,
        ChatDialog
    },
    data() {
        return {
            model: {
                aimodel: 'gpt-3.5-turbo',
                systemMessage: 'You are an AI assistant that helps people find information.',
                historyMessageCount: 6,
                compressMessage: true,
                temperature: 0.7,
                compressThreshold: 2000,
            },
            modelRules: {},
            inputContent: '',
            defaultDialog: {
                role: "assistant",
                content: "好的，我会按照您的要求回答!",
                avatarImg: 'https://avatars.githubusercontent.com/u/3236542?v=4',
            },
            dialogs: [],
            answering: false,
            assisantAvatarImg: 'https://avatars.githubusercontent.com/u/3236542?v=4',
            userAvatarImg: 'https://avatars.githubusercontent.com/u/3236542?v=4',
            summarizedContext: '',
            lastMsIndex: 0,
            stream: null,
        }
    },
    computed: {
    },
    methods: {
        submitContent() {
            this.answering = true;

            // 用户
            this.inputContent = this.inputContent.trim();
            this.dialogs.push({
                role: 'user',
                content: this.inputContent,
                rowReverse: true,
                avatarImg: this.userAvatarImg,
            })
            // 助理
            this.dialogs.push({
                role: 'assistant',
                content: '',
                avatarImg: this.assisantAvatarImg,
            })
            let answerIndex = this.dialogs.length - 1;
            // 滚动到底部
            this.chatscroll();

            const messages = this.createMessagePayload();

            try {
                this.stream = postFetchEventSource("/api/chat/completion/stream", {
                    model: this.model.aimodel,
                    messages,
                    temperature: this.model.temperature,
                    stream: true,
                }, (data) => {
                    // console.log(data)
                    this.dialogs[answerIndex].content += data.$content;
                }, () => {
                    this.stream = null;
                    this.answering = false;
                    this.compressMessage();
                }, (err) => {
                    this.$message.error(err);
                    this.answering = false;
                })
            } catch (error) {
                console.error(error)
                this.answering = false;
            }
        },
        handleKeydown(event) {
            if (event.key === 'Enter') {
                if (event.ctrlKey || event.shiftKey) {
                    event.preventDefault();
                    this.inputContent += '\n'; // 只手动添加换行
                } else {
                    event.preventDefault();
                    if (this.inputContent && this.inputContent !== '') {
                        this.submitContent();
                        this.inputContent = '';
                    }
                }
            }
        },
        chatscroll() {
            if (this.answering) {
                let interval = setInterval(() => {
                    this.$refs.chatScroll.setScrollTop(this.$refs.chatScrollInner.clientHeight)
                    if (!this.answering) {
                        clearInterval(interval);
                    }
                }, 500);
            }
        },
        createMessagePayload() {
            let context = this.model.systemMessage;
            if (this.model.compressMessage && this.summarizedContext !== '') {
                context = this.summarizedContext;
            }
            let messages = [
                {
                    role: 'user',
                    content: context,
                }
            ];
            if (['ERNIE-Bot', 'Claude 3 Opus', 'Claude 3 Sonnet'].includes(this.model.aimodel)) {
                messages.push({
                    role: 'assistant',
                    content: '好的，我会按照您的要求回答!',
                })
            }
            if (['qwen-turbo', 'qwen-plus', 'qwen-max'].includes(this.model.aimodel)) {
                messages[0].role = 'system'
            }
            // 历史消息
            const dialogLength = this.dialogs.length;
            this.dialogs.slice(Math.max(0, dialogLength - (this.model.historyMessageCount + 2)), dialogLength - 1).forEach((dialog) => {
                messages.push({
                    role: dialog.role,
                    content: dialog.content,
                })
            })
            return messages;
        },
        async compressMessage() {
            let contentLength = 0;
            let messages = this.dialogs.slice(this.lastMsIndex).map(dialog => {
                contentLength += dialog.content.length;
                return {
                    role: dialog.role,
                    content: dialog.content,
                }
            })
            if (contentLength < 1000) {
                return;
            }

            this.lastMsIndex = this.dialogs.length;
            messages.push({
                role: 'user',
                content: '简要总结一下对话内容，用作后续的上下文提示 prompt，控制在 200 字以内',
            })
            this.$axios.post("/api/chat/completion", {
                model: 'gpt-3.5-turbo',
                messages,
                temperature: 0.1,
            }).then(res => {
                this.summarizedContext = res.data.$response;
            }).catch(err => {
                console.error(err)
            })
        },
        clearChat() {
            this.dialogs = [],
            this.lastMsIndex = 0;
            this.summarizedContext = '';
        },
        stopChat() {
            if (this.stream) {
                this.stream.stop();
                this.answering = false;
                this.stream = null;
                this.compressMessage();
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.container-wrapper {
    width: auto !important;
    display: flex;
    column-gap: 20px;
    height: calc(100vh - 100px);

    .left-card {
        width: 300px;   

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

    }

    .main-card {
        flex: 1;

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }


        .card-content {
            display: flex;
            flex-direction: column;
            height: 100%;

            .chat-box {
                flex: 1;
                overflow: hidden;

                .chat-box-dialog {
                    margin-bottom: 20px;
                }
            }

            .chat-input {
                align-items: flex-end;
            }

            .chat-input-brops {
                margin: 0 10px 10px 0;
            }

            .chat-input-btn {
                border-radius: 10px;
            }

            .chat-input-poper {
                margin: -20px 10px 0 0;
            }
        }
    }
}
</style>
