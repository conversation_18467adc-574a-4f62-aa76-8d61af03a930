<template>
  <div>
    <el-table :data="data" size="small" border>
      <el-table-column type="index" label="序号" width="50" align="center">
        <template #default="scope">
          <span>{{ scope.$index + 1 }}</span>
          <el-button type="text" icon="CopyDocument" @click.stop="copyAreaInfo(data[scope.$index])" style="margin-left:2px;padding:0" :title="'复制'" />
        </template>
      </el-table-column>
      <el-table-column v-for="column in columns" :key="column.prop"
                       v-bind="column" align="center" :width="column.width || ''">
        <template v-if="column.prop === 'operations'" v-slot="scope">
          <el-button type="warning" style="background: #f56c6c;color: white;font-weight: normal;font-size: 13px"
                     size="small" @click="onCorrect(scope.$index)">纠错
          </el-button>
          <el-button type="primary"
                     style="background: #2e9efc;color: white;font-weight: normal;font-size: 13px"
                     size="small">编辑
          </el-button>
        </template>
        <template v-else-if="column.prop === 'area'" v-slot="scope">
          <el-link v-if="scope.row.areaImg" type="primary"
                   :href="$fileserver.fileurl(scope.row.areaImg)" target="_blank">
            {{ `区域${scope.row.areaIdx + 1}` }}
          </el-link>
          <el-tooltip v-else placement="top" :content="scope.row.review">
            <el-text style="text-decoration: underline" @click="showError(scope.row?.review)">{{ `区域${scope.row.areaIdx + 1}` }}</el-text>
          </el-tooltip>
        </template>
        <template v-else-if="column.prop === 'isCorrect'" v-slot="scope">
          <el-text v-if="scope.row.isScorePoint === 2 || scope.row.isEssay">{{
              `${scope.row.scored} /
                        ${scope.row.score}`
            }}
          </el-text>
          <el-tooltip v-else-if="scope.row.isCorrect" placement="top" :content="'可信度：' + (scope.row?.credibility ?? '无')">
            <el-tag :type="scope.row.isCorrect === 'Y' ? 'primary' : 'danger'">
              {{ scope.row.isCorrect === 'Y' ? '正确' : '错误' }}
            </el-tag>
          </el-tooltip>

        </template>
        <template v-else-if="column.prop === 'hasChange'" v-slot="scope">
          <el-tag :type="scope.row.hasChange === 1 ? 'success' : 'primary'">{{
              scope.row.hasChange === 1 ?
                  '已修改' : '未修改'
            }}
          </el-tag>
        </template>
        <template v-else-if="column.prop === 'score'" v-slot="scope">
          <el-text v-if="scope.row.isScorePoint === 2">{{
              `${scope.row.scored} / ${scope.row.score}`
            }}
          </el-text>
          <el-text v-else>{{
              `${scope.row.isCorrect === 'Y' ? scope.row.score : 0} / ${scope.row.score}`
            }}
          </el-text>
        </template>
        <template v-else-if="column.prop === 'answer'" v-slot="scope">
          <el-text v-if="Array.isArray(scope.row.answer) ">{{ scope.row.answer?.join(",") }}</el-text>
          <el-text v-else>{{ scope.row.answer }}</el-text>
        </template>
        <template v-else-if="column.prop === 'studentAnswer'" v-slot="scope">
          <el-text v-if="Array.isArray(scope.row.studentAnswer) ">{{
              scope.row.studentAnswer?.join(",")
            }}
          </el-text>
          <el-text v-else>{{ scope.row.studentAnswer }}</el-text>
        </template>
        <template v-else-if="column.prop === 'question'" v-slot="scope">
          <el-text v-if="scope.row.isEssay">作文题</el-text>
          <el-text>{{ scope.row.question }}</el-text>
        </template>
      </el-table-column>
    </el-table>

    <review-form ref="reviewForm"/>
  </div>

</template>

<script>
import ReviewForm from '@/views/doccorrect/components/reviewform.vue'

export default {
  components: {ReviewForm},
  props: {
    data: {
      type: Array,
      required: []
    },
    paperName: {
      type: String,
      required: true
    },
    pageLabel: {
      type: String,
      required: true
    },
    paperIndex: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      columns: [
        {
          prop: "area",
          label: "图片",
          simpleMode: true,
          width: 80
        },
        {
          prop: "question",
          label: "问题",
          width: 100,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "answer",
          label: "答案",
          width: 60,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "studentAnswer",
          label: "学生",
          width: 60,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "isCorrect",
          label: "是否正确",
          width: 80,
          "show-overflow-tooltip": true,
          simpleMode: true,
        },
        {
          prop: "operations",
          label: "操作",
          width: 140,
          simpleMode: true,
        },
        {
          prop: "hasChange",
          label: "是否修改",
          simpleMode: true,
          width: 90
        }
      ]
    }
  },
  methods: {
    showError(e) {
      navigator.clipboard.writeText(String(e)).then(() => {
        this.$message.success('已复制');
      }).catch(() => {
        this.$message.error('复制失败，请手动复制');
      });
      this.$confirm(e);
    },
    onEdit(data) {
      this.$refs.reviewForm.show({data})
    },
    onCorrect(rightIndex) {
      this.$emit('errorCorrectionFromTable', rightIndex)
    },
    copyAreaInfo(row) {
      // 只复制 试卷名-页号-本页第几份试卷
      const text = `${this.paperName}-${this.pageLabel}_${this.paperIndex.toString().padStart(3, '0')}`;
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('复制成功');
        }, () => {
          this.$message.error('复制失败');
        });
      } else {
        // fallback
        const input = document.createElement('input');
        input.value = text;
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
        this.$message.success('复制成功');
      }
    }
  }
}
</script>

<style lang="scss"></style>