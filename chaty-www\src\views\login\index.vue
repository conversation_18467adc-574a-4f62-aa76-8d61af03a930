<template>
  <div class="page-container">
    <div class="left-content">
      <div class="header">
        <el-image class="image" src="/logo.png" />
        <div class="left-title">纸质作业</div>
        <div class="right-title">留痕批改机</div>
      </div>
      <swiper
          slides-per-view="auto"
          :space-between="30"
          :centered-slides="true"
          :autoplay="{ delay: 5000, disableOnInteraction: false }"
          :pagination="{ clickable: true }"
          :modules="[Autoplay]"
          class="swiper-container"
          @slideChange="onSlideChange">
        <swiper-slide
            v-for="item in images"
            :key="item"
            style="width: 100%; display: flex; align-items: center; justify-content: center;">
          <el-image class="middle-image" :src="item" fit="contain"></el-image>
        </swiper-slide>
      </swiper>


      <!--        <div class="bottom-text">-->
      <!--          介绍文案：如何设计出吸引视线的标题呢？标题文字的组合需要注意哪些细节？有哪些简单实用的标题制作技巧呢？本期会为大家逐一解答。本期会为大家逐一解答。本期会为大家逐一解答。-->
      <!--        </div>-->
      <div class="dots">
        <div v-for="(item,index) in images" class="dot"
          :style="{ backgroundColor: activeIndex === index ? '#1677ff' : '' }"></div>
      </div>
    </div>
    <div class="right-content">
      <div class="panel-header">
        <el-image class="logo" src="/icon/16.png" />
        <div class="title">欢迎登录留痕批改机</div>
      </div>
      <el-form class="login-form" ref="loginForm" :model="form" :rules="rules">
        <el-form-item prop="username">
          <el-input class="input" v-model="form.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input class="input" v-model="form.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-link class="forget-password">忘记密码</el-link>
        <el-form-item>
          <el-button class="login-form-btn" type="primary" @click="doLogin" :loading="logining">登录</el-button>
        </el-form-item>
        <div class="privacy">
          <el-checkbox v-model="acceptPrivacy">
            <span class="left">阅读并接受</span>
            <span class="right">《纸质作业留痕批改机平台隐私政策》</span>
          </el-checkbox>
        </div>
      </el-form>

      <div class="other-login">
        <div class="line"></div>
        <el-text class="text">其他登录方式</el-text>
        <div class="line"></div>
      </div>

      <div class="login-container">
        <div class="login-item">
          <el-image class="image" src="/icon/phone.png" />
          <el-text class="text">手机验证码</el-text>
        </div>

        <div class="login-item">
          <el-image class="image" src="/icon/zheXueMa.png" />
          <el-text class="text">浙学码扫码</el-text>
        </div>

        <div class="login-item">
          <el-image class="image" src="/icon/dingding.png" />
          <el-text class="text">钉钉</el-text>
        </div>

        <div class="login-item">
          <el-image class="image" src="/icon/zheZhengDing.png" />
          <el-text class="text">浙政钉</el-text>
        </div>
      </div>


    </div>

  </div>
</template>
<script>

import {mapActions} from "pinia";
import {useUserStore} from "@/store";
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/swiper-bundle.css';
import {Autoplay} from "swiper/modules";

export default {
  components: {
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      form: {
        username: "",
        password: "",
      },
      rules: {
        username: [
          {required: true, message: "请输入用户名", trigger: "blur"}
        ],
        password: [
          {required: true, message: "请输入密码", trigger: "blur"}
        ]
      },
      images: [
        'https://pic2.ziyuan.wang/user/tanfuhua/2025/01/Snipaste_2025-01-06_13-25-06_cd7f49e3423c9.png',
        'https://pic2.ziyuan.wang/user/tanfuhua/2024/12/swiper2_67e1d321143c9.png',
        'https://pic2.ziyuan.wang/user/tanfuhua/2024/12/swiper3_ab60057af7a44.jpg'
      ],
      activeIndex: 0,
      acceptPrivacy: true,
      logining: false
    }
  },
  methods: {
    Autoplay,
    onSlideChange(swiper) {
      // 获取当前的索引
      this.activeIndex = swiper.activeIndex;
    },
    ...mapActions(useUserStore, ['showLogin', 'setUser', 'initAuthData']),
    doLogin() {
      if (!this.acceptPrivacy) {
        this.$message.warning("请先阅读并接受隐私政策")
        return
      }
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.logining = true
          this.$axios.post("/api/login", this.form, {
            headers: {'content-type': 'application/x-www-form-urlencoded'},
          }).then(res => {
            this.showLogin(false)
            this.setUser(res.data)
            return this.initAuthData() // 初始化权限数据
          }).then(() => {
            this.$message.success("登录成功")
            console.log('path',this.$route.meta?.prevPath)
            let prevPath = this.$route.meta?.prevPath;
            if (prevPath && prevPath !== '/') {
              this.$router.back()
            } else {
              this.$router.push('/comprehensive')
            }
          }).finally(() => {
            this.logining = false
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  width: calc(100vw / 0.9);
  height: calc(100vh / 0.9);
  display: flex;

  .left-content {
    display: flex;
    flex-direction: column;
    width: calc(100% - 450px);
    align-items: center;
    padding: 0 4.5vw;
    background: linear-gradient(to bottom, #eef7ff, #f2f8ff);
    .header {
      padding: 10vh 0 5vh 0;
      width: 100%;
      display: flex;
      justify-content: left;
      height: 47px;
      align-items: center;
      z-index: 2;

      .image {
        width: 40px;
        height: 40px;
      }

      .left-title {
        font-weight: bold;
        font-size: 34px;
        color: #333333;
        letter-spacing: 0;
        margin-left: 9.25px;
      }

      .right-title {
        font-weight: bold;
        font-size: 34px;
        letter-spacing: 0;
        background-image: linear-gradient(to bottom, #23B0FF, #006FD1);
        background-clip: text;
        -webkit-background-clip: text; /* 兼容Webkit内核浏览器 */
        color: transparent;
      }
    }

    .swiper-container {
      width: calc(100vw - 600px) !important;
      height: calc(100vh - 15vh - 47px - 40px);
      display: flex;
      align-items: center;
      justify-content: center;

      .middle-image {
        height: 100%;
        width: auto;
        z-index: 1;
      }
    }

    .bottom-text {
      width: 55vw;
      height: 40px;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      letter-spacing: 0;
      text-align: center;
      margin-top: 2vh;
    }

    .dots {
      width: 69.6px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
      margin-top: 5.93vh;
      margin-bottom: 48px;
      .dot {
        width: 7px;
        height: 7px;
        border-radius: 50%;
        background-color: #e2eefe;
      }
    }
  }

  .right-content {
    display: flex;
    flex-direction: column;
    width: 450px;
    flex-shrink: 1;
    padding: 50px;
    box-shadow: 0 0 20px 0 #829ad01a;

    .panel-header {
      margin-top: 10%;
      display: flex;
      width: 380px;

      .logo {
        width: 47.41px;
        height: 38.5px;
      }

      .title {
        font-weight: 700;
        font-size: 24px;
        color: #333333;
        letter-spacing: 0.65px;
        margin-left: 6.75px;
      }
    }

    .login-form {
      margin-top: 51px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .input {
        width: 360px;
        height: 46px;
        background: #F4F6FA;
        border-radius: 6px;
      }

      .forget-password {
        font-weight: 400;
        font-size: 15px;
        color: #666666;
        letter-spacing: 0;
        text-align: center;
        float: left;
      }

      .login-form-btn {
        width: 360px;
        height: 46px;
        border-radius: 6px;
        margin-top: 20px;
      }

      .privacy {
        height: 20px;

        .left {
          color: #999999;
          font-size: 14px;
          font-face: PingFangSC;
          font-weight: 400;
          line-height: 0;
          letter-spacing: 0;
          paragraph-spacing: 0;
          text-align: left;
        }

        .right {
          color: #5276e3;
          font-size: 14px;
          font-face: PingFangSC;
          font-weight: 400;
          line-height: 0;
          letter-spacing: 0;
          paragraph-spacing: 0;
          text-align: left;
        }
      }
    }

    .other-login {
      display: flex;
      align-items: center;
      margin-top: 50px;
      width: 380px;

      .line {
        width: 143.5px;
        height: 1px;
        border-top: 1px solid #DDDDDD;
      }

      .text {
        width: 93px;
        height: 17px;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        letter-spacing: 0;
        text-align: center;
      }
    }

    .login-container {
      display: flex;
      align-items: center;
      margin-top: 23.49px;
      flex-wrap: wrap;
      width: 380px;
      gap: 8px;

      .login-item {
        display: flex;
        align-items: center;
        padding: 11px 10px;
        width: 121px;
        height: 46px;
        background: #FFFFFF;
        border: 1px solid #F0F2F5;
        border-radius: 4px;

        .image {
          width: 24px;
          height: 24px;
        }

        .text {
          width: 65px;
          height: 18px;
          font-weight: 400;
          font-size: 13px;
          color: #999999;
          letter-spacing: 0;
          margin-left: 8px;
        }
      }
    }
  }

}
</style>