export const initScoreRange = (scoreTypes, scoreTypesMaxScore) => {
    const ranges = {};
    for (let i = 0; i < scoreTypes.length; i++) {
        let maxScore = scoreTypesMaxScore[i]
        ranges[scoreTypes[i]] = [
            {maxx: maxScore, minn: (maxScore * 0.85).toFixed(1), name: 'A', sliderRange: [0, 15]},
            {
                maxx: (maxScore * 0.85 - 0.5).toFixed(1),
                minn: (maxScore * 0.6).toFixed(1),
                name: 'B',
                sliderRange: [15, 40]
            },
            {maxx: (maxScore * 0.6 - 0.5).toFixed(1), minn: 0, name: '<PERSON>', sliderRange: [40, 100]},
        ];
    }

    return ranges;
}