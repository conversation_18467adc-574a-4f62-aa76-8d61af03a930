<template>
  <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑学校' : '添加学校'"
      :before-close="handleClose"
      width="680px"
      :close-on-click-modal="false"
  >
    <el-form
        :model="form"
        ref="formRef"
        label-width="100px"
        class="form-container"
    >
      <el-form-item
          label="学校名称"
          prop="schoolName"
          :rules="[
          { required: true, message: '请输入学校名称', trigger: 'blur' },
          { min: 2, max: 50, message: '学校名称长度在 2 到 50 个字符之间', trigger: 'blur' }
        ]"
      >
        <el-input
            v-model="form.schoolName"
            placeholder="请输入学校名称"
            clearable
        />
      </el-form-item>

      <el-form-item
          label="学校地址"
          prop="address"
          :rules="[
          { required: true, message: '请输入学校地址', trigger: 'blur' }
        ]"
      >
        <el-input
            v-model="form.address"
            placeholder="请输入学校地址"
            clearable
        />
      </el-form-item>

      <el-form-item
          label="联系电话"
          prop="phoneNumber"
          :rules="[
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
        ]"
      >
        <el-input
            v-model="form.phoneNumber"
            placeholder="请输入联系电话"
            clearable
        />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button v-if="isEdit" type="danger" @click="deleteSchool">
        删除该学校
      </el-button>
      <el-button class="button" type="primary" @click="submitForm">
        确定
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {mapState} from "pinia";
import {useUserStore} from "@/store";
import {ElMessageBox, ElMessage} from "element-plus";

export default {
  name: "SchoolManagementDialog",
  data() {
    return {
      dialogVisible: false,
      form: {
        schoolName: "",
        address: "",
        phoneNumber: ""
      },
      isEdit: false,
      currentSchoolId: null
    };
  },
  methods: {
    openDialog(schoolObj = null) {
      if (schoolObj) {
        this.isEdit = true;
        this.currentSchoolId = schoolObj.schoolId;
        this.populateForm(schoolObj);
      } else {
        this.isEdit = false;
        this.currentSchoolId = null;
        this.resetForm();
      }
      this.dialogVisible = true;
    },

    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
      this.isEdit = false;
      this.currentSchoolId = null;
      this.$emit('cancel')
    },

    resetForm() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
      this.form.schoolName = "";
      this.form.address = "";
      this.form.phoneNumber = "";
    },

    populateForm(schoolObj) {
      this.form.schoolName = schoolObj.schoolName || "";
      this.form.address = schoolObj.address || "";
      this.form.phoneNumber = schoolObj.phoneNumber || "";
    },

    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.isEdit && this.currentSchoolId) {
            const payload = {
              schoolId: this.currentSchoolId,
              schoolName: this.form.schoolName,
              address: this.form.address,
              phoneNumber: this.form.phoneNumber
            };

            this.$axios
                .post(`/api/school/update`, payload)
                .then((res) => {
                  ElMessage.success("学校更新成功");
                  this.dialogVisible = false;
                  this.resetForm();
                  this.$emit("fetchSchools");
                })
                .catch((error) => {
                  console.error("更新学校错误：", error);
                  ElMessage.error("网络错误，请稍后再试");
                });
          } else {
            const payload = {
              schoolName: this.form.schoolName,
              address: this.form.address,
              phoneNumber: this.form.phoneNumber
            };

            this.$axios
                .post("/api/school/add", payload)
                .then((res) => {
                  ElMessage.success("学校添加成功");
                  this.dialogVisible = false;
                  this.resetForm();
                  this.$emit("fetchSchools");
                })
                .catch((error) => {
                  console.error("添加学校错误：", error);
                  ElMessage.error("网络错误，请稍后再试");
                });
          }
        } else {
          ElMessage.error("请检查表单填写是否正确");
        }
      });
    },

    deleteSchool() {
      ElMessageBox.confirm(
          `此操作将永久删除学校 "${this.form.schoolName}"，是否继续？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }
      )
          .then(() => {
            this.$axios
                .delete(`/api/school/delete/${this.currentSchoolId}`)
                .then((res) => {
                  ElMessage.success("学校删除成功");
                  this.dialogVisible = false;
                  this.resetForm();
                  this.$emit("fetchSchools");
                })
                .catch((error) => {
                  console.error("删除学校错误：", error);
                  ElMessage.error("网络错误，请稍后再试");
                });
          })
          .catch(() => {
            // 用户取消删除
          });
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.button {
  width: 90px;
  height: 32px;
  background: #3981ff;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  margin-right: 10px;
}

.form-container {
  padding: 20px;
}

.el-dialog .el-dialog__footer {
  text-align: center;
}

.el-button[type="primary"] {
  background-color: #3981ff;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
}

.el-input {
  width: 100%;
}
</style>
