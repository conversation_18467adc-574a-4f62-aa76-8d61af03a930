<template>
    <div class="form-wrapper">
        <div class="form-header">
            <el-text size="large" tag="b" type="primary">{{ title }}</el-text>
        </div>
        <el-form class="question-form" label-position="top">
            <el-form-item label="题目：" prop="question">
                <el-input v-model="question.question" placeholder="请输入题目" type="textarea"
                    :autosize="{ minRows: 3 }"></el-input>
            </el-form-item>
            <el-form-item label="正确答案：" prop="correctAnswer">
                <el-input v-model="question.correctAnswer" placeholder="请输入正确答案" type="textarea"
                    :autosize="{ minRows: 3 }"></el-input>
            </el-form-item>
            <el-form-item label="高度：" prop="height" label-position="left">
                <el-input-number v-model="question.height" :controls="false"></el-input-number>
            </el-form-item>
        </el-form>
        <div class="form-footer">
            <el-button type="warning" @click="onClose">取消</el-button>
            <el-button type="primary" @click="onSubmit">确认</el-button>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            title: '',
            question: {
                question: '',
                correctAnswer: '',
                height: 3
            },
            id: ''
        }
    },
    methods: {
        show({ title, data }) {
            this.title = title
            if (data) {
                this.id = data.id
                this.question = Object.assign(this.question, data)
            }
        },
        onClose() {
            this.title = ''
            this.question = {
                question: '',
                correctAnswer: '',
                height: 3,
            }
            this.id = ''
            this.$emit('onClose')
        },
        onSubmit() {
            if (this.id && this.id !== '') {
                this.$axios.post("/api/doc/question/update", this.question).then(res => {
                    this.$message.success('更新成功')
                    this.onClose()
                })
            } else {
                this.$axios.post("/api/doc/question/add", this.question).then(res => {
                    this.$message.success('新增成功')
                    this.onClose()
                })
            
            }
        }
    },
}
</script>

<style lang="scss" scoped>
.form-wrapper {
    border: 1px solid var(--el-border-color);
    padding: 15px;

    .form-header {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .form-footer {
        text-align: right;
    }

}
</style>